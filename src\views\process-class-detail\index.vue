<template>
  <div class="process-class-detail">
    <Breadcrumb class="none-container-padding" />
    <FSpin :spinning="processConfigLoading">
      <ProcessClassInfo
        class="marginB24"
        :processConfig="processConfig"
        :classId="classId"
        :processConfigLoading="processConfigLoading"
      />
    </FSpin>

    <RelevanceInfo />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, provide, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getByIdProcessConfig } from '@/api/processManagement'
import Breadcrumb from '@/components/Breadcrumb/index.vue'
import ProcessClassInfo from './components/ProcessClassInfo/index.vue'
import RelevanceInfo from './components/RelevanceInfo/index.vue'

const router = useRouter()
const route = useRoute()
const processConfigLoading = ref<boolean>(false) // 流程配置详情加载状态
const processConfig = ref<any>({}) // 流程配置详情
const classId = computed(() => route.params.id as string) // 流程配置类id
const back = () => {
  router.push({ name: 'ProcessClass' })
}

const onGetByIdProcessConfig = async () => {
  if (!classId.value) {
    throw new Error('流程配置类ID不能为空')
  }
  try {
    processConfigLoading.value = true
    const res = await getByIdProcessConfig(classId.value)
    if (res.code !== 200) throw new Error(res.msg)
    processConfig.value = res?.data ?? {}
  } finally {
    processConfigLoading.value = false
  }
}

onMounted(() => {
  onGetByIdProcessConfig()
})

// const processConfig = inject<Ref<any>>('processConfig')
// const classId = inject<Ref<string>>('classId')
// const onGetByIdProcessConfig = inject<() => Promise<void>>('onGetByIdProcessConfig')
provide('processConfig', processConfig)
provide('onGetByIdProcessConfig', onGetByIdProcessConfig)
provide('classId', classId)
provide('setProcessConfigLoading', (loading: boolean) => {
  processConfigLoading.value = loading
})
provide('back', back)
</script>

<style lang="scss" scoped>
.process-class-detail {
  .none-container-padding {
    margin-top: -20px;
    margin-left: -20px;
    width: calc(100% + 40px);
  }
  .card-content-shadow {
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
    border-radius: 4px;
  }
  .flex {
    display: flex;
    align-items: center;
  }
  .space-between {
    justify-content: space-between;
  }
  :deep(.fs-btn-sm) {
    padding: 0 8px !important;
    font-size: 12px !important;
  }
}
</style>
