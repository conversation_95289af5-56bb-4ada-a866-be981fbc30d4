<template>
  <div ref="wrapperRef" class="milepost-form-render-wrapper" v-if="isShow" @click="handleElementClick">
    <Render ref="renderRef" :schema="schema" :props="renderProps" :config="renderConfig" />
    <SeeImage v-model="seeImageObj.flag" :src="seeImageObj.src" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, markRaw, watch, reactive, onUnmounted, nextTick } from 'vue'
import { Render } from 'fs-speed'
import { messageInstance as message } from '@fs/smart-design'
import { BAESURL, S3URL, getToken, getUserInfo, deepClone, useI18n, CRMURL, MOMURL } from '@/utils'
import { getFormSchema } from '@/api'
import SeeImage from '@/components/SeeImage/index.vue'

interface IProps {
  id: string | number
  type: 'view' | 'edit'
  data: Record<string, unknown>
}

const i18n = useI18n()
const props = defineProps<IProps>()
const token = getToken()
const userInfo = markRaw(deepClone(getUserInfo()))

const isShow = ref(false)
const seeImageObj = reactive({ src: '', flag: false })
// const amisScoped = ref<IScopedContext>()
const schema = ref<object>()
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const renderRef = ref<any>()
const wrapperRef = ref<HTMLDivElement>()

const renderConfig = {
  notify: (type: 'error' | 'success' | 'info' | 'warning', msg: string) => message[type] && message[type](msg),
  useMobileUI: false,
}

const renderProps = ref(
  markRaw({
    // scopeRef: (scoped: IScopedContext) => (amisScoped.value = scoped),
    locale: i18n.language,
    data: {
      ...(markRaw(deepClone(props.data)) || {}),
      envType: props.type || 'view',
      envToken: token,
      envUrl: BAESURL,
      envS3Url: S3URL + '/api/s3/upload',
      envS3BaseUrl: S3URL,
      envUserInfo: userInfo,
      envCrmUrl: CRMURL,
      envMomURL: MOMURL,
    },
  })
)

watch(
  [props],
  () => {
    updateAmisProps(i18n.language)
  },
  { deep: true }
)

watch(
  () => props.id,
  () => {
    initSchema()
  }
)

watch(schema, () => {
  isShow.value = true
})

onMounted(() => {
  initSchema()
  // 微前端环境下，国际化语言变化监听
  if (window.__MICRO_APP_ENVIRONMENT__) {
    // 监听基座下发的数据变化
    window.microApp.addDataListener(emitLanguageChange)
  }
})

onUnmounted(() => {
  if (window.__MICRO_APP_ENVIRONMENT__) {
    window.microApp.removeDataListener(emitLanguageChange)
  }
})

const emitLanguageChange = (data: { path: string; language: string }) => {
  if (data.language) {
    isShow.value && (isShow.value = false)
    updateAmisProps(data.language)
    nextTick(() => (isShow.value = true))
  }
}

const initSchema = async () => {
  isShow.value && (isShow.value = false)
  const res = await getFormSchema(props.id)
  schema.value = markRaw(JSON.parse((res?.data?.content as string) ?? '{}'))
}

const handleElementClick = (e: any) => {
  if (props.type === 'view' && e.target.nodeName === 'IMG') {
    seeImageObj.src = e.target.currentSrc
    seeImageObj.flag = true
  }
}

// TODO: 切换成英文后，无法动态切换为中文，必须要刷新才行
const updateAmisProps = (language: string) => {
  const currData = markRaw({
    // scopeRef: (scoped: IScopedContext) => (amisScoped.value = scoped),
    locale: language,
    data: {
      ...(markRaw(deepClone(props.data)) || {}),
      envType: props.type || 'view',
      envToken: token,
      envUrl: BAESURL,
      envS3Url: S3URL + '/api/s3/upload',
      envS3BaseUrl: S3URL,
      envUserInfo: userInfo,
      envCrmUrl: CRMURL,
      envMomURL: MOMURL,
    },
  })
  if (JSON.stringify(currData) !== JSON.stringify(renderProps.value)) {
    renderProps.value = currData
  }
}

defineExpose({
  getAmisInstance: () => renderRef.value?.getAmisRenderRef?.(),
  getAmisScoped: () => renderRef.value?.getAmisInstance?.(),
})
</script>
