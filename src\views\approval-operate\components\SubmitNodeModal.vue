<template>
  <div class="submit-node-container">
    <FModal
      width="720px"
      v-model:visible="visible"
      :title="i18n.t('同意')"
      :confirm-loading="confirmLoading"
      :get-container="target"
      centered
      @ok="submitFn"
    >
      <FForm ref="formRef" :model="formState" :rules="rules" layout="vertical">
        <FRow :gutter="[24, 24]">
          <FCol :span="24">
            <FFormItem :label="i18n.t('抄送人')" name="msg">
              <FCascader
                dropdown-class-name="notification-cust-cascader"
                multiple
                show-arrow
                :placeholder="i18n.t('请输入')"
                style="width: 100%"
                :field-names="{ label: 'name', value: 'uuid', children: 'departmentChildrens' }"
                :max-tag-count="10"
                :options="personList"
                :show-search="{ filterOption }"
                show-checked-strategy="SHOW_CHILD"
                @change="onChangePerson"
                v-model:value="formState.makeUuidList"
                :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
                :key="key"
              ></FCascader>
            </FFormItem>
          </FCol>
          <FCol :span="24">
            <FFormItem :label="i18n.t('审批说明')" name="msg">
              <FTextarea v-model:value="formState.msg" style="min-height: 88px" :placeholder="i18n.t('请输入')" />
            </FFormItem>
          </FCol>
          <FCol :span="24">
            <FFormItem
              label="上传文件"
              name="attachment"
              :progress="{ strokeWidth: 2, showInfo: false }"
              style="width: 100%"
            >
              <FUploadDragger v-model:file-list="formState.files" action="" :before-upload="handleUpload">
                <p class="upload-drag-icon">
                  <span class="iconfont icontubiao_shangchuan_mian upload-icon"></span>
                </p>
                <p class="ant-upload-text">{{ i18n.t('单击或拖动文件到此区域') }}</p>
                <p class="ant-upload-hint">
                  {{
                    i18n.t(
                      '支持上传PDF, JPG, JPEG, PNG, DOC, DOCX, PPT, PPTX, XLS, XLSX, TXT, ZIP, RAR格式文件，文件大小2G'
                    )
                  }}
                </p>
              </FUploadDragger>
            </FFormItem>
          </FCol>
        </FRow>
      </FForm>
      <div></div>
    </FModal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, inject, Ref, onMounted } from 'vue'
import { useI18n } from '@/utils'
import type { FormInstance } from '@fs/smart-design/dist/ant-design-vue_es'
import type { Rule } from '@fs/smart-design/dist/ant-design-vue_es/form'
import { message } from '@fs/smart-design'
import { submitApprovalNode, SubmitNodeParams, HandleNodeList, upload, getDepartment } from '@/api'
import { fileLimit } from '@/views/approval-operate/utils'

const i18n = useI18n()
const emits = defineEmits(['update:visible'])
const confirmLoading = ref<boolean>(false)
const handleNode = inject<Ref<HandleNodeList>>('handleNode') as Ref<HandleNodeList> // 当前里程碑信息
const getApprovalInfoFn = inject('getApprovalInfoFn') as () => void // 更新当前里程碑信息
const formRenderRef = inject<Ref>('formRenderRef')
const visible = ref<boolean>(false)
const formRef = ref<FormInstance>()
const formState = reactive<SubmitNodeParams>({
  msg: undefined,
  files: [],
  makeUuidList: [],
})
const rules: Record<string, Rule[]> = {
  // msg: [{ required: true, message: i18n.t('请输入流程 InstanceId') }],
  // templateCode: [{ required: true, message: i18n.t('请输入模板编码') }],
  // uuid: [{ required: true, message: i18n.t('请选择人员') }],
}
const target = ref(() => document.querySelector('#container'))
const personList = ref([])
const filterOption = (input: string, option: any) => option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
let key = ref<number>(Date.now() + Math.random())

const checkChildren = (node: any, users: any = []) => {
  return node.reduce((users: any, cur: any) => {
    console.log(Object.keys(cur))
    if (cur.uuid && cur.name && Object.keys(cur).length === 2) {
      users.push(cur.uuid)
    } else if (cur.departmentChildrens && cur.departmentChildrens.length) {
      checkChildren(cur.departmentChildrens, users)
    }
    return users
  }, users)
}

let selectLists: string | any[] = []
const onChangePerson = (value: any, selectedOptions: any) => {
  selectLists =
    selectedOptions.map((item: any) => {
      return item[item.length - 1]
    }) || []
}

const handleUpload = (file: any) => {
  if (!fileLimit(file)) {
    file.status = 'error'
    return false
  }
  let key = Date.now() + '' + Math.random()
  file.status = 'uploading'
  file.key = key
  uploadItem(file, key)
  return false
}
// 检测上传进度
const onHandleUploadProgress = (progressEvent: any, key: string) => {
  const index = formState.files.findIndex((item: any) => item.key === key)
  index !== -1 &&
    (formState.files[index].percent = Math.round((progressEvent.loaded / progressEvent.total) * 10000) / 100.0)
}
const uploadItem = (file: any, key: string) => {
  let data = new FormData()
  data.append('file', file)
  data.append('isOpen', 'false')
  data.append('expire', '0')
  upload(data, onHandleUploadProgress, key).then((res: any) => {
    const index = formState.files.findIndex((item: any) => item.key === key)
    if (res && index !== -1) {
      formState.files[index].fileUrl = res
      formState.files[index].status = 'success'
    } else if (index !== -1) {
      formState.files[index].status = 'error'
      message.error(i18n.t('上传失败，请移除！'))
    }
  })
}

const submitFn = async () => {
  try {
    confirmLoading.value = true
    if (!formRef.value) {
      return
    }
    const flag = formState.files.some((item: any) => item.status !== 'success')
    if (flag) {
      message.warning(i18n.t('请检查文件是否上传完成或上传失败'))
      return
    }
    if (formRenderRef?.value) {
      const scoped = formRenderRef.value?.getAmisScoped()
      const $form = scoped?.getComponentByName('page.nodeForm')
      if (!$form) throw new Error(i18n.t(`表单实例获取失败，请联系管理员！`))

      await $form?.submit()
      const res = await $form?.doAction({ actionType: 'submit' })

      if (!res || !res.code) throw new Error(i18n.t('表单提交接口配置异常，请联系管理员！'))
      if (res.code !== 200) throw new Error(i18n.t('表单信息提交失败'))

      message.success(i18n.t('表单信息提交成功'))
    }
    await formRef.value.validate()
    const makeUuidList = checkChildren(selectLists)
    const params = {
      files: (formState?.files ?? []).map(item => ({ fileName: item.name, size: item.size, url: item.fileUrl })),
      formData: {},
      milepostId: handleNode?.value?.id,
      makeUuidList: makeUuidList?.length ? makeUuidList : undefined,
      msg: formState?.msg || undefined,
    }
    const res = await submitApprovalNode(params)
    if (res.code !== 200) throw new Error(res.msg)
    message.success(res.msg || i18n.t('审批通过！'))
    getApprovalInfoFn()
    visible.value = false
  } catch (error: any) {
    message.error(error.message)
  } finally {
    confirmLoading.value = false
  }
}

const open = () => {
  formRef.value?.resetFields()
  key.value = Date.now() + Math.random()
  formState.makeUuidList = []
  selectLists = []
  formState.msg = undefined
  formState.files = []
  visible.value = true
}

const handleTree = (node: any, users: any = []) => {
  return node.reduce((users: any, cur: any) => {
    if (cur.departmentChildrens) {
      let data = JSON.parse(JSON.stringify(cur))
      data.departmentChildrens = JSON.parse(JSON.stringify(data.uuidAndNames || []))
      !((cur.uuidAndNames || []).length + (cur.departmentChildrens || []).length) && (data.disabled = true)
      users.push(data)
      cur.departmentChildrens.length && handleTree(cur.departmentChildrens, data.departmentChildrens)
    }
    return users
  }, users)
}

const onGetDepartment = async () => {
  const res = await getDepartment()
  if (res.code !== 200) throw new Error(res.msg)
  personList.value = handleTree(res.data)
}

onMounted(() => {
  if (!personList.value.length) requestIdleCallback(onGetDepartment)
})

defineExpose({ open })
</script>
<style scoped lang="scss">
:deep(.fs-form-item-control-input-content) {
  height: auto !important;
}
:deep(.fs-modal-footer) {
  padding: 12px 24px !important;
}
.upload-drag-icon {
  margin: 0;
  padding: 0;
  height: 48px;
  .upload-icon {
    color: #d8d8d8;
    font-size: 36px;
  }
}
</style>
