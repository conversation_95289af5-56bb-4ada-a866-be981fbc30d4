<template>
  <div class="add-field-container">
    <FModal
      width="720px"
      v-model:visible="visible"
      :title="(type && i18n.t('新增字段')) || i18n.t('字段编辑')"
      @cancel="cancelFn"
      @ok="submitFn"
    >
      <FForm ref="formRef" :model="formState" :rules="rules" layout="vertical">
        <FRow :gutter="[24, 24]">
          <FCol :span="12">
            <FFormItem :label="i18n.t('序号')" name="sort">
              <FInputNumber style="width: 100%" v-model:value="formState.sort" :placeholder="i18n.t('请输入')" />
            </FFormItem>
          </FCol>
          <FCol :span="12">
            <FFormItem :label="i18n.t('数据类型')" name="dataType">
              <FSelect
                :placeholder="i18n.t('请选择')"
                v-model:value="formState.dataType"
                :options="dataTypeOptions"
                allow-clear
              />
            </FFormItem>
          </FCol>
          <FCol :span="12">
            <FFormItem :label="i18n.t('字段名')" name="fieldName">
              <FInput v-model:value="formState.fieldName" :placeholder="i18n.t('请输入')" />
            </FFormItem>
          </FCol>
          <FCol :span="12">
            <FFormItem :label="i18n.t('字段编码')" name="fieldCode">
              <FInput v-model:value="formState.fieldCode" :placeholder="i18n.t('请输入')" />
            </FFormItem>
          </FCol>
          <!-- <FCol :span="12">
            <FFormItem label="引用实体" name="quoteEntityId">
              <FSelect
                placeholder="请选择"
                v-model:value="formState.quoteEntityId"
                :options="entityTypeLists"
                :field-names="{ label: 'name', value: 'id' }"
                allow-clear
              />
            </FFormItem>
          </FCol> -->
          <FCol :span="12">
            <FFormItem :label="i18n.t('是否系统字段')" name="isSystem">
              <FRadioGroup v-model:value="formState.isSystem" :options="options" />
            </FFormItem>
          </FCol>
          <FCol :span="12">
            <FFormItem :label="i18n.t('启用状态')" name="status">
              <FRadioGroup v-model:value="formState.status" :options="options" />
            </FFormItem>
          </FCol>
          <FCol :span="12">
            <FFormItem :label="i18n.t('是否可为空')" name="isNull">
              <FRadioGroup v-model:value="formState.isNull" :options="options" />
            </FFormItem>
          </FCol>
          <FCol :span="12">
            <FFormItem :label="i18n.t('是否允许修改')" name="isUpdate">
              <FRadioGroup v-model:value="formState.isUpdate" :options="options" />
            </FFormItem>
          </FCol>
          <FCol :span="12">
            <FFormItem :label="i18n.t('是否主键')" name="isPrimaryKey">
              <FRadioGroup v-model:value="formState.isPrimaryKey" :options="options" />
            </FFormItem>
          </FCol>
          <FCol :span="12">
            <FFormItem :label="i18n.t('是否显示')" name="isShow">
              <FRadioGroup v-model:value="formState.isShow" :options="options" />
            </FFormItem>
          </FCol>
        </FRow>
      </FForm>
      <div></div>
    </FModal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { useI18n } from '@/utils'
import { FieldList } from '@/types/entity'
import type { FormInstance } from '@fs/smart-design/dist/ant-design-vue_es'
import type { Rule } from '@fs/smart-design/dist/ant-design-vue_es/form'
import { saveDatamodelfield, updateDatamodelfield, getEntityType } from '@/api'
import { FInputNumber } from '@fs/smart-design'

interface IProps {
  visible: boolean
  data?: FieldList
  type?: boolean
}

const i18n = useI18n()
const props = defineProps<IProps>()
const emits = defineEmits(['update:visible', 'getFieldList'])
const visible = computed({
  get: () => props.visible,
  set: val => emits('update:visible', val),
})
const options = computed(() => [
  { label: i18n.t('是'), value: 0 },
  { label: i18n.t('否'), value: 1 },
])
const formRef = ref<FormInstance>()
const formState = reactive<FieldList>({
  dataType: undefined,
  entityId: undefined,
  fieldCode: undefined,
  fieldName: undefined,
  id: undefined,
  isNull: undefined,
  isPrimaryKey: undefined,
  isSystem: undefined,
  isUpdate: undefined,
  quoteEntityId: undefined,
  sort: undefined,
  status: undefined,
  isShow: undefined,
})
const rules: Record<string, Rule[]> = {
  sort: [{ required: true, message: i18n.t('请输入排序') }],
  dataType: [{ required: true, message: i18n.t('请选择数据类型') }],
  fieldName: [{ required: true, message: i18n.t('请选择字段名') }],
  fieldCode: [{ required: true, message: i18n.t('请选择字段编码') }],
  quoteEntityId: [{ required: true, message: i18n.t('请选择引用实体') }],
  isSystem: [{ required: true, message: i18n.t('请选择是否系统字段') }],
  status: [{ required: true, message: i18n.t('请选择启用状态') }],
  isNull: [{ required: true, message: i18n.t('请选择是否可为空') }],
  isUpdate: [{ required: true, message: i18n.t('请选择是否允许修改') }],
  isPrimaryKey: [{ required: true, message: i18n.t('请选择是否主键') }],
  isShow: [{ required: true, message: i18n.t('请选择是否显示') }],
}
const entityTypeLists = ref<any>([])
const dataTypeOptions = ref<any>([
  { label: i18n.t('输入框'), value: 'inputCode' },
  { label: i18n.t('数字输入框'), value: 'inputNumberCode' },
  { label: i18n.t('下拉选'), value: 'selectCode' },
  { label: i18n.t('下拉多选'), value: 'selectMultipleCode' },
  // { label: i18n.t('日期选择'), value: 'datePickerCode' },
  { label: i18n.t('日期范围选择'), value: 'rangePickerCode' },
  { label: i18n.t('数字范围选择'), value: 'rangeNumberCode' },
])

const getEntityTypeList = async () => {
  const res = await getEntityType({ entityType: 0 })
  if (res.code !== 200) throw new Error(res.msg)
  entityTypeLists.value = res.data || []
}

const cancelFn = () => {
  formRef.value?.resetFields()
  visible.value = false
}

const submitFn = async () => {
  if (!formRef.value) {
    return
  }
  await formRef.value.validate()
  let res
  if (props.type) {
    res = await saveDatamodelfield(formState)
  } else {
    res = await updateDatamodelfield(formState)
  }
  if (res.code !== 200) throw new Error(res.msg)
  cancelFn()
  emits('getFieldList')
}

const setFieldFn = (data: FieldList) => {
  Object.entries(formState).forEach(([key, value]) => {
    formState[key as keyof typeof formState] = data[key as keyof typeof data]
  })
}

watch(
  () => visible.value,
  val => {
    val && setFieldFn(props.data as FieldList)
  }
)

onMounted(() => {
  getEntityTypeList()
})
</script>
<style scoped lang="scss">
.fs-radio-box {
  line-height: 32px;
}
</style>
