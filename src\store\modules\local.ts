import { Commit } from 'vuex'
import { ProcessListSearch, ILocalProjectUuidData } from '@/types/processListModel'
import { triggerListSearch } from '@/types/excutionListModel'
import { getUserInfo } from '@/utils'

type IProcessListSearchInfo = {
  [key: string]: ProcessListSearch | undefined
}

type ILocalProjectUuidDataInfo = {
  [key: string]: ILocalProjectUuidData | undefined
}

type IBoardItem = {
  componentValue?: any
  componentArgsValue?: Record<string, any>
  [key: string]: any
}

type IProcessBoardSearchInfo = {
  [key: string]: IBoardItem
}

type IAutomationSearchInfo = {
  [key: string]: triggerListSearch
}

type ILocalUserData = {
  [key: string]: any
}

interface LocalStore {
  localCountryId: number[] | undefined
  localSearchData: IProcessListSearchInfo
  localProjectUuidData: ILocalProjectUuidDataInfo
  localBoardSearchData: IProcessBoardSearchInfo
  localProcessManagementSearchData: IProcessBoardSearchInfo
  localUserData: ILocalUserData
  localAutomationSearchData: IAutomationSearchInfo
}

const userInfo = getUserInfo()

const state: LocalStore = {
  localCountryId:
    (sessionStorage.getItem('LOCAL_COUNTRY_ID') && JSON.parse(sessionStorage.getItem('LOCAL_COUNTRY_ID') as string)) ||
    undefined,
  localSearchData:
    (sessionStorage.getItem('LOCAL_SEARCH_DATA') &&
      JSON.parse(sessionStorage.getItem('LOCAL_SEARCH_DATA') as string)) ||
    undefined,
  localProjectUuidData:
    (sessionStorage.getItem('LOCAL_PROJECT_UUID_DATA') &&
      JSON.parse(sessionStorage.getItem('LOCAL_PROJECT_UUID_DATA') as string)) ||
    undefined,
  localBoardSearchData:
    (sessionStorage.getItem('LOCAL_BOARD_SEARCH_DATA') &&
      JSON.parse(sessionStorage.getItem('LOCAL_BOARD_SEARCH_DATA') as string)) ||
    undefined,
  localProcessManagementSearchData:
    (sessionStorage.getItem('LOCAL_PROCESS_MANAGEMENT_DATA') &&
      JSON.parse(sessionStorage.getItem('LOCAL_PROCESS_MANAGEMENT_DATA') as string)) ||
    undefined,
  localUserData:
    (localStorage.getItem(`LOCAL_USER_DATA_${userInfo.adminId}`) &&
      JSON.parse(localStorage.getItem(`LOCAL_USER_DATA_${userInfo.adminId}`) as string)) ||
    undefined,
  localAutomationSearchData:
    (sessionStorage.getItem('LOCAL_AUTOMATION_SEARCH_DATA') &&
      JSON.parse(sessionStorage.getItem('LOCAL_AUTOMATION_SEARCH_DATA') as string)) ||
    undefined,
}
const mutations = {
  SET_LOCALCOUNTRYID: (state: LocalStore, localCountryId: number[]) => {
    state.localCountryId = localCountryId
    sessionStorage.setItem('LOCAL_COUNTRY_ID', JSON.stringify(localCountryId))
  },
  SET_LOCAL_SEARCH_DATA: (state: LocalStore, localSearchData: ProcessListSearch) => {
    const info = Object.assign({}, state.localSearchData, localSearchData)
    state.localSearchData = info
    sessionStorage.setItem('LOCAL_SEARCH_DATA', JSON.stringify(info))
  },
  SET_LOCAL_PROJECT_UUID_DATA: (state: LocalStore, localProjectUuidData: ILocalProjectUuidData) => {
    const info = Object.assign({}, state.localProjectUuidData, localProjectUuidData)
    state.localProjectUuidData = info
    sessionStorage.setItem('LOCAL_PROJECT_UUID_DATA', JSON.stringify(info))
  },
  SET_LOCAL_BOARD_SEARCH_DATA: (state: LocalStore, localBoardSearchData: IProcessBoardSearchInfo) => {
    state.localBoardSearchData = localBoardSearchData
    sessionStorage.setItem('LOCAL_BOARD_SEARCH_DATA', JSON.stringify(localBoardSearchData))
  },
  SET_LOCAL_PROCESS_MANAGEMENT_SEARCH_DATA: (
    state: LocalStore,
    localProcessManagementSearchData: IProcessBoardSearchInfo
  ) => {
    const info = Object.assign({}, state.localProcessManagementSearchData, localProcessManagementSearchData)
    state.localProcessManagementSearchData = info
    sessionStorage.setItem('LOCAL_PROCESS_MANAGEMENT_DATA', JSON.stringify(info))
  },
  SET_LOCAL_USER_DATA: (state: LocalStore, localUserData: ILocalUserData) => {
    const info = Object.assign({ feiShuName: userInfo.feiShuName || '--' }, state.localUserData, localUserData)
    state.localUserData = info
    localStorage.setItem(`LOCAL_USER_DATA_${userInfo.adminId}`, JSON.stringify(info))
  },
  SET_LOCAL_AUTOMATION_SEARCH_DATA: (state: LocalStore, localAutomationSearchData: IAutomationSearchInfo) => {
    state.localAutomationSearchData = localAutomationSearchData
    sessionStorage.setItem('LOCAL_AUTOMATION_SEARCH_DATA', JSON.stringify(localAutomationSearchData))
  },
}

const getters = {
  getLocalCountryId: (state: LocalStore) => state.localCountryId,
  getLocalSearchData: (state: LocalStore) => state.localSearchData,
  getLocalProjectUuidData: (state: LocalStore) => state.localProjectUuidData,
  getLocalBoardUuidData: (state: LocalStore) => state.localBoardSearchData,
  getLocalProcessManagementSearchData: (state: LocalStore) => state.localProcessManagementSearchData,
  getLocalUserData: (state: LocalStore) => state.localUserData,
  getLocalAutomationData: (state: LocalStore) => state.localAutomationSearchData,
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
}
