<template>
  <FModal v-model:visible="visible" @ok="handleOk" @cancel="handleCancel">
    <div style="padding: 20px 0">
      <FForm ref="formRef" :model="formData" :rules="formRules" :label-col="{ span: 4 }">
        <FFormItem :label="i18n.t('成员')" name="userIds">
          <FSelect
            v-model:value="formData.userIds"
            mode="multiple"
            :options="props.userList"
            :filter-option="filterOption"
            :field-names="{ label: 'name', value: 'uuid' }"
            show-search
            allow-clear
            :placeholder="i18n.t('请选择人员')"
          />
        </FFormItem>
      </FForm>
    </div>
  </FModal>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue'

import type { IUser } from '@/types/handle'
import type { FormInstance } from '@fs/smart-design/dist/ant-design-vue_es'
import { useI18n } from '@/utils'

const i18n = useI18n()

type UserType = IUser & { name: string }

interface IProps {
  modelValue: boolean
  userList: UserType[]
}

const props = defineProps<IProps>()
const emits = defineEmits(['update:modelValue', 'add'])

const formRef = ref<FormInstance>()
const formData = reactive<{ userIds: string[] }>({ userIds: [] })
const formRules = computed(() => ({
  userIds: [{ required: true, message: i18n.t('请选择成员'), trigger: 'change' }],
}))
const visible = computed({
  get: () => props.modelValue,
  set: (val: boolean) => emits('update:modelValue', val),
})

const handleOk = async () => {
  const $form = formRef.value as FormInstance
  await $form.validate()
  emits('add', { ...formData })
  handleCancel()
}

const handleCancel = () => {
  const $form = formRef.value as FormInstance
  $form.resetFields()
  visible.value = false
}

const filterOption = (input: string, option: UserType) => option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
</script>

<style lang="scss" scoped></style>
