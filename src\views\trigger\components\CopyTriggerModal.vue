<template>
  <FModal
    v-model:visible="visible"
    :title="i18n.t('复制触发器')"
    :loading="loading"
    :width="400"
    @cancel="cancelFn"
    @ok="submitFn"
  >
    <div class="edit-time-box">
      <FForm ref="formRef" :model="formState" :rules="rules" layout="vertical">
        <FRow :gutter="[0, 24]">
          <FCol :span="24">
            <FFormItem :label="i18n.t('触发器名称')" name="triggerName">
              <FInput v-model:value="formState.triggerName" />
            </FFormItem>
          </FCol>
        </FRow>
      </FForm>
    </div>
  </FModal>
</template>
<script lang="ts" setup>
import { reactive, computed, ref } from 'vue'
import type { FormInstance } from '@fs/smart-design/dist/ant-design-vue_es'
import type { Rule } from '@fs/smart-design/dist/ant-design-vue_es/form'
import { useI18n } from '@/utils'
import { copyTrigger } from '@/api/automotion'

interface FormState {
  triggerName: string
}

interface IProps {
  visible: boolean
  id: number
}

const i18n = useI18n()
const emits = defineEmits(['fetchData', 'update:visible'])
const props = defineProps<IProps>()
const loading = ref(false)
const formState = reactive<FormState>({
  triggerName: undefined,
})
const rules: Record<string, Rule[]> = {
  triggerName: [{ required: true, message: i18n.t('请输入名称') }],
}
const formRef = ref<FormInstance>()
const visible = computed({
  get: () => props.visible,
  set: val => emits('update:visible', val),
})

const cancelFn = () => {
  formRef.value?.resetFields()
  visible.value = false
}

const submitFn = async () => {
  try {
    if (!formRef.value) return
    loading.value = true
    await formRef.value.validate()
    const params = {
      id: props.id,
      triggerName: formState.triggerName,
    }
    const res = await copyTrigger(params)
    if (res.code !== 200) throw new Error(res.msg)
    emits('fetchData')
    cancelFn()
  } finally {
    loading.value = false
  }
}
</script>
<style lang="scss" scoped>
.edit-time-box {
  :deep(.fs-form-item-control-input-content) {
    height: auto !important;
  }
}
</style>
