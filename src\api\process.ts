import { request } from '@/utils'
import { BasicPageParams } from '@/types/common'
import type { ProcessListParams, ProcessListResponse, ProcessExportParams } from '@/types/processListModel'
import {
  IIndicatorCenterInfo,
  IProcessRoleAndUser,
  IRelationProcess,
  IRes,
  IResDataList,
  ISearchConfig,
  ITabConfig,
} from '@/types/request'
import { IProcess, IPermitInstance, IProcessClassType, SuccessIRes } from '@/types/handle'

// 查询列表
export function getProcessList(data: ProcessListParams, page: BasicPageParams): Promise<ProcessListResponse> {
  delete page.total
  return request.post('/api/bpmInstance/getInstanceList', { ...data, ...page })
}

// 获取草稿列表
export const getDraftProcessInfo = async (data: ProcessListParams, page: BasicPageParams) => {
  delete page.total
  const res = await request.post<IRes<IResDataList<IProcess[]>>>('/api/instanceDraft/getPage', { ...data, ...page })
  return res as unknown as IRes<IResDataList<IProcess[]>>
}

// 根据 id 查询草稿信息
export const getDraftProcessInfoById = async (id: string) => {
  const res = request.get<IRes<IProcess>>(`/api/instanceDraft/getById/${id}`)
  return res as unknown as IRes<IProcess>
}

//  根据里程碑id查询该里程碑信息
export function GetCooperateList(params: any): Promise<any> {
  return request.get('/api/demandProcessing/selectByMilepostId', {
    params,
  })
}

// 统计流程实例的数量
export function getProccessCount(): Promise<any> {
  return request.get('/api/bpmInstance/count')
}

// 统计查询条件的流程实例的数量
export function getProccessQueryCount(data: ProcessListParams): Promise<any> {
  return request.post('/api/bpmInstance/searchCount', data)
}

// 统计查询条件的流程实例的数量
export function getOtherProccessQueryCount(data: ProcessListParams, otherUrl): Promise<any> {
  return request({
    method: 'post',
    url: otherUrl + '/api/bpmInstance/searchCount',
    data,
  })
}

// 获得流程定义的标签及流程节点
export function GetTagAndNode(): Promise<IRes<IProcessClassType[]>> {
  return request.get('/api/bpmDefine/getTagAndNode')
}

// 获取当前流程第一个里程碑的表单 id
export function getFormByProcessDefineKey(processDefineKey: string): Promise<any> {
  return request.get(`/api/bpmInstance/getFormByProcessDefineKey?processDefineKey=${processDefineKey}`)
}

// 获取当前流程第一个里程碑的表单 id
export function getLtcByNumber(opportunityId: string): Promise<any> {
  return request.get(`/api/bpmDefine/getLtcByNumber?number=${opportunityId}`)
}

// 指标中心
export const getIndicatorCenterInfo = async () => {
  const { data } = await request.get<IRes<IIndicatorCenterInfo>>('/api/bpmInstance/getIndicatorCenterInfo')
  return data as unknown as IIndicatorCenterInfo
}

// 获取流程的角色信息
export const getProcessRoleAndUser = async (id: number) => {
  const res = request.get<IRes<IProcessRoleAndUser[]>>(
    '/api/instanceRelateRole/getInstanceRelateRoleByInstanceId?instanceId=' + id
  )
  return res as unknown as IRes<IProcessRoleAndUser[]>
}

interface IProcessRoleAndUserParams {
  ext?: unknown
  id?: number
  instanceId: number
  name: string
  roleCode?: string
  roleName: string
  uuid: string
}

// 修改流程角色信息
export const updateProcessRoleAndUser = async (params: IProcessRoleAndUserParams) => {
  return request.post<IRes>('/api/instanceRelateRole/updateInstanceRelateRole', params)
}

// 新增流程角色信息
export const createProcessRoleAndUser = async (params: IProcessRoleAndUserParams) => {
  return request.post<IRes>('/api/instanceRelateRole/addInstanceRelateRole', params)
}

// 删除流程角色信息
export const deleteProcessRoleAndUser = async (id: number) => {
  return request.get<IRes>('/api/instanceRelateRole/deleteaInstanceRelateRole?id=' + id)
}

// 检验权限
export const verifyPermissions = async (instanceId: number) => {
  const res = request.get<SuccessIRes>(`/api/bpmInstance/verifyPermissions/${instanceId}`)
  return res as unknown as SuccessIRes
}

// 添加流程成员
export const addProcessOtherUser = async (instanceId: number, uuidList: string[]) => {
  return request.post<IRes>('/api/instanceRelateRole/addOtherRelateRole', { instanceId, uuidList })
}

// 添加流程关联关系
export const addProcessRelation = async (
  instanceId: number,
  instanceCode: string,
  relevanceInstanceIdList: number[]
) => {
  return request.post<IRes>('/api/relevanceInstance/add', { instanceId, instanceCode, relevanceInstanceIdList })
}

// 删除流程关联关系
export const deleteProcessRelation = async (id: number) => {
  return request.get<IRes>('/api/relevanceInstance/delete/' + id)
}

// 获取可关联流程列表
export const getPermitInstanceList = async (processConfigId: number | null) => {
  const url = processConfigId
    ? '/api/relevanceInstance/getPermitInstanceList?processConfigId=' + processConfigId
    : '/api/relevanceInstance/getPermitInstanceList'
  const res = request.get<IRes<IPermitInstance[]>>(url)
  return res as unknown as IRes<IPermitInstance[]>
}

// 查询关联流程
export const getProcessRelateList = async (instanceId: number) => {
  const res = request.get<IRes<IRelationProcess[]>>('/api/relevanceInstance/selectRelevanceProcessDetail/' + instanceId)
  return res as unknown as IRes<IRelationProcess[]>
}

// 查询待办任务列表
export const getToDoList = async (processId: number) => {
  const res = request.get<SuccessIRes>('/api/demandProcessing/getPendingTasks/' + processId)
  return res as unknown as SuccessIRes
}

interface IRecallProcessParams {
  instanceId: number
  instanceTopicName: string
  processInstanceCode: string
  processType: string
  milepostId: number
  msg: string
}

// 撤回流程
export const recallProcess = async (params: IRecallProcessParams) => {
  const res = request.post<IRes<number>>('/api/demandProcessing/recall', params)
  return res as unknown as IRes
}

// 修改里程碑的计划完成时间
export const updateMilepostTime = async (params: {
  instanceId: number
  milepostId: number
  forcastTime: string | null
}) => {
  const res = request.post<IRes>('/api/demandProcessing/updateMilepostTime', params)
  return res as unknown as IRes
}

// 导出流程信息
export const exportProcess = async (params: ProcessExportParams) => {
  const res = request.post<IRes>('/api/file/export', params)
  return res as unknown as IRes
}

// 获取搜索配置
export const getSearchConfigById = async (processConfigId: number) => {
  const res = request.get<IRes<ISearchConfig[]>>(`/api/searchModule/getByConfigId/${processConfigId}`)
  return res as unknown as IRes<ISearchConfig[]>
}

// 获取搜索数据源
export const getSearchConfigOptions = async (url: string) => {
  const res = request.get<IRes<any[]>>(url)
  return res as unknown as IRes<any[]>
}

interface IhurryMessageParams {
  milepostId?: string | number
  taskId?: string | number
}

// 获取搜索数据源
export const hurryMessage = async (params: IhurryMessageParams) => {
  const res = request.get<IRes<any>>('/api/message/hurryMessage', { params })
  return res as unknown as IRes<any>
}

// 获取搜索数据源
export const getTabConfig = async (instanceId: number) => {
  const res = request.get<IRes<ITabConfig[]>>(`/api/bpmInstance/getTabConfig/${instanceId}`)
  return res as unknown as IRes<ITabConfig[]>
}

// 创建群组
export const createFSGroup = async (instanceId: number | string) => {
  const res = request.get<IRes<string>>(`/api/chats/create/${instanceId}`)
  return res as unknown as IRes<string>
}

// 获取群组 id
export const getFSGroupId = async (instanceId: number | string) => {
  const res = request.get<IRes<string>>(`/api/chats/getChatsId/${instanceId}`)
  return res as unknown as IRes<string>
}

// 查询是否可添加任务池任务
export const isItConfigured = async (processId: number) => {
  const res = request.get<SuccessIRes>('/api/demandProcessing/taskPool/isItConfigured/' + processId)
  return res as unknown as SuccessIRes
}

// 查询流程配置
export const getProcessConfigByInstanceId = async (processId: number) => {
  const res = request.get<SuccessIRes>('/api/bpmDefine/getByInstanceId/' + processId)
  return res as unknown as SuccessIRes
}

// 查询文档管理分类文件下拉
export const getDocumentType = async (params: { instanceId: number }) => {
  const res = request.get<SuccessIRes>('/api/plm/getDocumentType', { params })
  return res as unknown as SuccessIRes
}

export interface PageDocumentListParams {
  correlateInstanceId?: number
  currPage?: number
  docAssortId?: number
  docTypeId?: number
  groupId?: number
  input?: string
  pageSize?: number
}

// 查询文档管理分类文件下拉
export const getDocumentList = async (data: PageDocumentListParams) => {
  const res = request.post<SuccessIRes>('/api/plm/page', data)
  return res as unknown as SuccessIRes
}

// 查询文档管理分类文件下拉
export const getDocumentPageByGroupIdList = async (data: PageDocumentListParams) => {
  const res = request.post<SuccessIRes>('/api/plm/pageByGroupId', data)
  return res as unknown as SuccessIRes
}

// 删除文档
export const deleteDocument = async (params: { id: number }) => {
  const res = request.get<SuccessIRes>('/api/plm/deleteDoc', { params })
  return res as unknown as SuccessIRes
}

// 批量删除文档
export const batchDeleteDocument = async (params: number[]) => {
  const res = request.post<SuccessIRes>('/api/plm/document/deleteByIds', params)
  return res as unknown as SuccessIRes
}

// 查询流程ID
export const selectByopportunityId = async (params: { opportunityId: number }) => {
  const res = request.get<SuccessIRes>('/api/bpmInstance/selectByopportunityId', { params })
  return res as unknown as SuccessIRes
}
export interface ProcessIpdListParams {
  creator?: string
  endTime?: string
  pageNum?: number
  pageSize?: number
  pbu?: string
  pdOwner?: string
  processConfigId?: number
  queryInput?: string
  startTime?: string
  type?: string
  [key: string]: any
}

// 查询ipd列表
export function getIpdProcessList(data: ProcessIpdListParams, page: BasicPageParams): Promise<ProcessListResponse> {
  delete page.total
  return request.post('/api/ipd/page', { ...data, ...page })
}

export const getIpdDictionary = async () => {
  const res = await request.get<IRes>(`/api/ipd/getDictionary`)
  return res as unknown as IRes
}

// 查询ipd列表
export function getMmProcessList(data: ProcessIpdListParams, page: BasicPageParams): Promise<ProcessListResponse> {
  delete page.total
  return request.post('/api/ipd/mmPage', { ...data, ...page })
}

export const getIpdSubProcessTable = async (instanceId: number, type: number) => {
  // type 1 => ir/ar/sr 2=> 测试
  const res = await request.get<IRes>(`/api/ipd/getSubProcessTable/${instanceId}/${type}`)
  return res as unknown as IRes
}

export const saveIIpdSubProcess = async (params: any) => {
  const res = await request.post<SuccessIRes>(`/api/ipd/saveSubProcess`, params)
  return res as unknown as SuccessIRes
}

export const deleteIpdSubProcess = async (params: any) => {
  const res = await request.post<SuccessIRes>(`/api/ipd/delSubProcess`, params)
  return res as unknown as SuccessIRes
}

export const updateIpdSubProcess = async (params: any) => {
  const res = await request.post<SuccessIRes>(`/api/ipd/updateSubProcess`, params)
  return res as unknown as SuccessIRes
}

export const getIpdSubProcessDetails = async (instanceId: number) => {
  const res = await request.get<IRes>(`api/demandProcessing/getDetails/${instanceId}`)
  return res as unknown as IRes
}

// 根据ipd主流程实例id查询IR-SR-AR Test的数量
export const getNewIpdRelevanceProcess = async (processId: number) => {
  const res = request.get<SuccessIRes>('/api/ipd/getSubProcessTable/' + processId)
  return res as unknown as SuccessIRes
}
