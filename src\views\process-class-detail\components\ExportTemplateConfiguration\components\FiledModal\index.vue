<template>
  <FModal
    width="720px"
    wrapClassName="bom-modal-wrap-container"
    v-model:visible="visible"
    :title="handleData?.[handleType]?.title ?? '新增'"
    centered
    :confirm-loading="loading"
    @cancel="onCancelFn"
    @ok="onSubmitFn"
  >
    <FForm ref="formRef" :model="formState" :rules="rules" layout="vertical">
      <FRow :gutter="[24, 24]">
        <FCol :span="12">
          <FFormItem label="字段名称（表头）" name="fieldName">
            <FInput v-model:value="formState.fieldName" placeholder="字段名称（表头）" />
          </FFormItem>
        </FCol>
        <FCol :span="12">
          <FFormItem label="排序" name="sortOrder">
            <FInputNumber v-model:value="formState.sortOrder" style="width: 100%" placeholder="排序" />
          </FFormItem>
        </FCol>
        <FCol :span="12">
          <FFormItem label="关联数据" name="relatedDataType">
            <FRadioGroup
              v-model:value="formState.relatedDataType"
              :options="[
                { label: '流程数据字段', value: 0 },
                { label: '系统字段', value: 2 },
              ]"
              @change="relatedDataTypeChange"
            />
          </FFormItem>
        </FCol>
        <FCol :span="12" v-if="formState.relatedDataType === 0">
          <FFormItem label="流程节点" name="nodeId">
            <FSelect
              v-model:value="formState.nodeId"
              placeholder="请选择"
              :options="processNodeList"
              :field-names="{ label: 'milepostName', value: 'nodeId' }"
              optionFilterProp="milepostName"
              @change="onChangeNodeFn"
            />
          </FFormItem>
        </FCol>
        <FCol :span="12">
          <FFormItem label="关联字段" name="code">
            <FSelect
              v-model:value="formState.code"
              placeholder="请选择"
              :options="filedList"
              optionFilterProp="label"
            />
          </FFormItem>
        </FCol>
        <FCol :span="12" v-if="formState.relatedDataType === 0">
          <FFormItem label="数据字典" name="dictionaryCode">
            <FInput v-model:value="formState.dictionaryCode" placeholder="请输入" />
          </FFormItem>
        </FCol>
      </FRow>
    </FForm>
    <div></div>
  </FModal>
</template>

<script setup lang="ts">
import { message } from '@fs/smart-design'
import { useI18n } from '@fs/i18n'
import { ref, reactive, inject, Ref, onMounted, computed } from 'vue'
import {
  getResNodeList,
  saveExportTemplateField,
  updateExportTemplateField,
  getSysField,
  getNodeField,
  getNextSortOrder,
} from '@/api'
import { getValueFn } from '@/views/process-operate/components/CustomComponents/BusinessComponent/utils'

const i18n = useI18n()
const emits = defineEmits(['updateChange'])
const classId = inject<Ref<string>>('classId')
const visible = ref<boolean>(false)
const processNodeList = ref([])
const processNodeFiledList = ref([])
const sysFieldList = ref([])
const templateId = ref()
const loading = ref<boolean>(false)
const formRef = ref()
const formState = reactive<any>({
  code: undefined,
  relatedDataType: undefined,
  fieldName: undefined,
  dictionaryCode: undefined,
  sortOrder: undefined,
  nodeId: undefined,
})
const currentRowRecord = ref<any>()
const handleType = ref('')
const handleData = computed(() => ({
  saveExportTemplateField: {
    title: i18n.t('新增字段'),
    msg: i18n.t('新增字段成功！'),
    defaultForm: { relatedDataType: 0 },
    baseParams: { templateId: templateId?.value },
    initFn: async () => {
      await initFileSortFn()
    },
    apiUrl: saveExportTemplateField,
  },
  updateExportTemplateField: {
    title: i18n.t('编辑字段'),
    msg: i18n.t('编辑字段成功！'),
    defaultForm: { relatedDataType: 0 },
    baseParams: { id: currentRowRecord?.value?.id, templateId: templateId?.value },
    initFn: async () => {
      await initProcessNodeFormFiled()
    },
    apiUrl: updateExportTemplateField,
  },
}))
const filedList = computed(
  () =>
    (formState.relatedDataType === 0 && processNodeFiledList.value) ||
    (formState.relatedDataType === 2 && sysFieldList.value) ||
    []
)

const rules: Record<string, any[]> = {
  fieldName: [{ required: true, message: i18n.t('请输入') }],
  sortOrder: [{ required: true, message: i18n.t('请输入') }],
  relatedDataType: [{ required: true, message: i18n.t('请选择') }],
  nodeId: [{ required: true, message: i18n.t('请选择') }],
  code: [{ required: true, message: i18n.t('请选择') }],
}

const initFileSortFn = async () => {
  const res = await getNextSortOrder(templateId?.value)
  formState.sortOrder = res?.data ?? undefined
}

const initProcessNodeFormFiled = async () => {
  if (!formState.nodeId || formState.relatedDataType !== 0) return
  const node = getValueFn(processNodeList.value, formState.nodeId, 'nodeId') ?? {}
  await initProcessNodeFiled(node)
}

const initProcessNodeFiled = async node => {
  processNodeFiledList.value = []
  const res = await getNodeField(node.processDefineKey, node.nodeId)
  processNodeFiledList.value = (res?.data ?? []).map(item => ({
    label: item?.label ?? item?.fileldKey,
    value: item?.fileldKey,
  }))
}

const onChangeNodeFn = (value, option) => {
  initProcessNodeFiled(option)
  formState.code = undefined
}

const relatedDataTypeChange = e => {
  formState.code = undefined
  formState.nodeId = undefined
}

const onCancelFn = () => {
  visible.value = false
}

const onSubmitFn = async () => {
  try {
    loading.value = true
    if (!formRef.value) return
    await formRef.value.validate()
    const params: any = Object.assign({}, formState, handleData?.value?.[handleType?.value]?.baseParams)
    await handleData?.value?.[handleType?.value]?.apiUrl(params)
    message.success(handleData?.value?.[handleType?.value]?.msg ?? i18n.t('操作成功'))
    onCancelFn()
    emits('updateChange')
  } finally {
    loading.value = false
  }
}

const onInitData = async () => {
  Object.entries(formState).forEach(([key, value]) => {
    formState[key] =
      (currentRowRecord.value || {})[key] ?? handleData?.value?.[handleType?.value]?.defaultForm?.[key] ?? undefined
  })
}

const initProcessNode = async () => {
  const res = await getResNodeList(classId.value)
  processNodeList.value = res?.data ?? []
}

const initBaseFiled = async () => {
  const res = await getSysField()
  sysFieldList.value = (res?.data ?? []).map(item => ({ label: item?.label, value: item?.fieldCode }))
}

const onOpenFn = async (typeValue: string, id: number, data: any = {}) => {
  visible.value = true
  currentRowRecord.value = data
  handleType.value = typeValue
  templateId.value = id
  await onInitData()
  handleData?.value?.[handleType?.value]?.initFn?.()
}

defineExpose({ onOpenFn })

onMounted(() => {
  requestIdleCallback(initProcessNode)
  requestIdleCallback(initBaseFiled)
})
</script>

<style scoped lang="scss"></style>
