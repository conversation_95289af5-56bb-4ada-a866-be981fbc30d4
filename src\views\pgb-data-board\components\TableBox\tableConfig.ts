import type { TableColumnsType } from '@fs/smart-design/dist/ant-design-vue_es'
import { VNode, h, computed } from 'vue'
import useCopy from '@/hooks/useCopy'
import { BAESURL, GOTO_WHITE, transformDate } from '@/utils'
import i18n from '@/i18n'

const statusData = computed(() => ({
  '0': {
    label: i18n.t('进行中'),
    class: 'iconfont icontubiao_jinhangzhong colorFA8F23',
  },
  '1': {
    label: i18n.t('已完成'),
    class: 'iconfont iconyiwanchengicon color2FCC83',
  },
  '2': {
    label: i18n.t('已办结'),
    class: 'iconfont iconyiwanchengicon color378EEF',
  },
}))

export const columns = computed<TableColumnsType>(() => [
  {
    title: i18n.t('项目名称'),
    dataIndex: 'projectName',
    fixed: 'left',
    width: 160,
  },
  {
    title: i18n.t('项目编号'),
    dataIndex: 'processInstanceCode',
    fixed: 'left',
    width: 160,
  },
  {
    title: i18n.t('流程类型'),
    dataIndex: 'processConfigName',
    width: 136,
  },
  {
    title: i18n.t('紧急程度'),
    dataIndex: 'isUrgent',
    customRender: ({ text, value, record }): VNode => {
      return h(
        'span',
        {
          class: ['urgent-label', (text && 'is-urgent') || ''],
        },
        (text && i18n.t('紧急')) || i18n.t('一般')
      )
    },
    width: 80,
  },
  {
    title: i18n.t('需求类型'),
    dataIndex: 'type',
    width: 160,
  },
  {
    title: i18n.t('整体延期率'),
    dataIndex: 'overallDelayRate',
    customRender: ({ text, value, record }): VNode => {
      return h('span', text + '%')
    },
    width: 96,
  },
  {
    title: i18n.t('总驳回次数'),
    dataIndex: 'overalRejectCount',
    width: 96,
  },
  {
    title: i18n.t('节点评审意见'),
    dataIndex: 'reviewMsg',
    width: 320,
  },
  {
    title: i18n.t('项目状态'),
    dataIndex: 'projecStatus',
    customRender: ({ text, value, record }): VNode => {
      return h(
        'span',
        {
          style: {
            display: 'flex',
            alignItems: 'center',
            height: '18px',
            color: '#333333',
          },
        },
        [
          h('i', {
            style: {
              marginRight: '4px',
            },
            class: statusData.value[text as keyof typeof statusData.value].class,
          }),
          statusData.value[text as keyof typeof statusData.value].label,
        ]
      )
    },
    width: 110,
  },
  {
    title: i18n.t('当前项目节点'),
    dataIndex: 'topicName',
    width: 120,
  },
  {
    title: i18n.t('节点延期率'),
    dataIndex: 'delayRate',
    customRender: ({ text, value, record }): VNode => {
      return h('span', text + '%')
    },
    width: 96,
  },
  {
    title: i18n.t('节点驳回次数'),
    dataIndex: 'rejectCount',
    width: 104,
  },
  {
    title: i18n.t('计划完成时间'),
    dataIndex: 'forcastTime',
    customRender: ({ text, value, record }): VNode => {
      return h('span', transformDate(text, 'YYYY-MM-DD'))
    },
    width: 120,
  },
  {
    title: i18n.t('节点负责人'),
    dataIndex: 'superviser',
    width: 120,
  },
  {
    title: i18n.t('关键进展'),
    dataIndex: 'evolve',
    width: 320,
  },
  {
    title: i18n.t('立项关键资料'),
    dataIndex: 'files',
    width: 104,
  },
  {
    title: i18n.t('所属BU'),
    dataIndex: 'bu',
    width: 120,
  },
  {
    title: i18n.t('所属DT'),
    dataIndex: 'dt',
    width: 120,
  },
  {
    title: i18n.t('所属KR'),
    dataIndex: 'kp',
    width: 320,
  },
  {
    title: i18n.t('国高项目'),
    dataIndex: 'project',
    width: 180,
  },
  {
    title: i18n.t('项目链接'),
    dataIndex: 'isUrgent',
    customRender: ({ text, value, record }): VNode => {
      const copyTest = GOTO_WHITE.includes(record.processConfigId)
        ? `${BAESURL}/bpm-manage/process/detail/${record.instanceId}`
        : `${BAESURL}/bpm-manage/demand/handle/${record.instanceId}`

      return h('i', {
        style: { cursor: 'pointer', color: '#378eef' },
        class: 'iconfont icontubiao_fuzhi copy-text',
        'data-clipboard-text': copyTest,
        onClick: () => useCopy('.copy-text'),
      })
    },
    width: 80,
  },
])
