<template>
  <div v-sticky="stickyInfo" class="nav" ref="navRef">
    <span
      v-for="(item, index) in list"
      :key="index"
      :class="['nav-item', { 'nav-active': navActive === item.value }]"
      @click="onNavChange(item.value)"
    >
      {{ item.label }}
    </span>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch, ref } from 'vue'
interface IProps {
  value: any
  list: {
    label: string
    value: any
  }[]
}

const props = defineProps<IProps>()
const emits = defineEmits(['change', 'update:value'])

const navRef = ref()
const stickyInfo = reactive({
  container: '#container',
  offset: 12,
  type: 'sticky',
  top: 94,
})

const navActive = ref(0)

watch(
  () => props.value,
  val => {
    navActive.value = val
  },
  { deep: true, immediate: true }
)
watch(
  () => navActive.value,
  val => {
    emits('update:value', val)
  },
  { deep: true }
)

const onNavChange = val => {
  navActive.value = val
  emits('change', val)
}
</script>

<style scoped lang="scss">
.nav {
  padding: 0px 24px;
  background-color: #fff;
  display: flex;
  height: 54px;
  line-height: 54px;
  border-bottom: 1px solid #eee;
  border-top: 1px solid #eee;
  width: calc(100% + 20px);

  .nav-item {
    color: #333;
    font-size: 14px;
    font-weight: 400;
    cursor: pointer;
    position: relative;
    margin-right: 32px;
  }

  .nav-active {
    font-weight: 500;

    &::after {
      content: '';
      width: 32px;
      height: 2px;
      background: #378eef;
      position: absolute;
      bottom: -1px;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
</style>
