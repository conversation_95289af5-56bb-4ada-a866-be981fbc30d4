import type { EChartsOption } from '@/components/BaseEchart/config'
import i18n from '@/i18n'

export const barOptions: EChartsOption = {
  color: ['#378EEF', '#2FCC83', '#FA8F23', '#F5CC38', '#F04141'],
  legend: {
    itemWidth: 8,
    itemHeight: 8,
    bottom: 0,
    itemGap: 16,
    textStyle: {
      color: '#666666',
    },
    data: [
      {
        name: i18n.t('进行中'),
        icon: 'path://M0 0h1024v1024H0z',
      },
      {
        name: i18n.t('已延期'),
        icon: 'path://M0 0h1024v1024H0z',
      },
      {
        name: i18n.t('已完成'),
        icon: 'path://M0 0h1024v1024H0z',
      },
      {
        name: i18n.t('已办结'),
        icon: 'path://M0 0h1024v1024H0z',
      },
      {
        name: i18n.t('延期率'),
        icon: 'path://M2730.666667 170.666667v682.666666H0v-682.666666z',
      },
    ],
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line',
      lineStyle: {
        color: '#666',
        type: 'solid',
      },
    },
    textStyle: {
      color: '#fff',
    },
    backgroundColor: '#000000B3',
    borderColor: 'transparent',
  },
  grid: {
    left: '10px',
    right: '10px',
    top: '28px',
    bottom: '24px',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    axisTick: {
      show: false,
    },
    axisLine: {
      lineStyle: {
        color: '#cccccc',
      },
    },
    axisLabel: {
      color: '#999999',
      interval: 0,
      formatter: function (value: string) {
        let ret = '' //拼接加\n返回的类目项
        const maxLength = 8 //每项显示文字个数
        const valLength = value.length //X轴类目项的文字个数
        const rowN = Math.ceil(valLength / maxLength) //类目项需要换行的行数
        if (rowN > 1) {
          //如果类目项的文字大于3,
          for (let i = 0; i < rowN; i++) {
            let temp = '' //每次截取的字符串
            const start = i * maxLength //开始截取的位置
            const end = start + maxLength //结束截取的位置
            //这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧
            temp = value.substring(start, end) + '\n'
            ret += temp //凭借最终的字符串
          }
          return ret
        } else {
          return value
        }
      },
    },
    axisPointer: {
      show: true,
      type: 'line',
      lineStyle: {
        color: '#666666',
      },
    },
    data: [],
  },
  yAxis: [
    {
      type: 'value',
      name: i18n.t('数量（个）'),
      nameTextStyle: {
        padding: [0, 0, 0, 10],
      },
      splitLine: {
        lineStyle: {
          color: '#EEEEEE',
        },
      },
      axisLabel: {
        color: '#999999',
        formatter: function (value: any) {
          return value + ''
        },
      },
    },
    {
      type: 'value',
      name: i18n.t('比率（%）'),
      nameTextStyle: {
        padding: [0, -34, 0, 0],
      },
      splitLine: {
        lineStyle: {
          color: '#EEEEEE',
        },
      },
      axisLabel: {
        color: '#999999',
        formatter: function (value: any) {
          return value + '%'
        },
      },
    },
  ],
  series: [],
}
