<template>
  <div class="relevance-info-container">
    <NavTabs :list="navs" v-model:value="nav" @change="tabChangeFn" />
    <div class="tab-content" v-if="nav">
      <FSpin :spinning="componentsLoading" style="width: 100%">
        <keep-alive>
          <component v-show="!componentsLoading" :is="components[nav].component" :ref="setComponentsRefFn" />
        </keep-alive>
      </FSpin>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, markRaw, defineAsyncComponent, nextTick, computed } from 'vue'
import { message } from '@fs/smart-design'
import NavTabs from '../NavTabs/index.vue'

interface IProps {
  matlId?: string
  instanceId?: string
  processType?: string
  viewTableData?: any[]
  fromData?: any
  isChange?: boolean
}

const props = defineProps<IProps>()
const componentsLoading = ref<boolean>(false)
const navs = ref([
  {
    value: 'ExportTemplateConfiguration',
    label: '导出模板',
  },
])
const nav = ref('ExportTemplateConfiguration')
const components = reactive({
  ExportTemplateConfiguration: {
    componentTitle: '导出模板配置',
    componentViewKey: 'ExportTemplateConfiguration',
    componentRef: undefined,
    component: markRaw(
      defineAsyncComponent(
        () => import('@/views/process-class-detail/components/ExportTemplateConfiguration/index.vue')
      )
    ),
    componentAttr: {},
    // getDataFn: async () => {},
  },
})

const tabChangeFn = val => {
  // !components[val]?.info?.flag && components[val]?.getDataFn()
}

const setComponentsRefFn = el => {
  nextTick(() => {
    components[nav.value].componentRef = el
  })
}

defineExpose({
  getRelevanceInfoDataFn: components,
})
</script>

<style scoped lang="scss">
.relevance-info-container {
  background-color: #fff;
  .tab-content {
    min-height: min-content;
    padding: 24px;
    transition: all 1s;
  }
}
</style>
