<template>
  <FModal
    class="remark-modal-wrapper"
    width="700px"
    v-model:visible="visible"
    :title="props.title"
    :confirm-loading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div>
      <FForm ref="formRef" :model="formData" :rules="formRules" layout="vertical">
        <FFormItem :label="i18n.t('撤回类型')" name="isSave">
          <FRadioGroup v-model:value="formData.isSave">
            <FRadio :value="1">{{ i18n.t('创建草稿') }}</FRadio>
            <FRadio :value="0">{{ i18n.t('仅撤回') }}</FRadio>
          </FRadioGroup>
        </FFormItem>
        <FFormItem :label="props.text" name="remark">
          <FTextarea v-model:value="formData.remark" :rows="5" :placeholder="`${i18n.t('请输入')}${props.text}`" />
        </FFormItem>
      </FForm>
    </div>
  </FModal>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import type { FormInstance } from '@fs/smart-design/dist/ant-design-vue_es'
import { useI18n } from '@/utils'

interface IProps {
  modelValue: boolean
  title: string
  text: string
}

interface IFormData {
  remark: string
  isSave: 0 | 1
}

const props = defineProps<IProps>()
const emits = defineEmits(['update:modelValue', 'submit'])
const i18n = useI18n()
const confirmLoading = ref(false)
const formRef = ref<FormInstance>()
const formData = reactive<IFormData>({ remark: '', isSave: 1 })
const formRules = {
  remark: [{ required: true, message: i18n.t('请输入备注') }],
  isSave: [{ required: true, message: i18n.t('请选择') }],
}
const visible = computed({
  get: () => props.modelValue,
  set: (val: boolean) => emits('update:modelValue', val),
})

watch(
  () => props.modelValue,
  () => {
    if (!props.modelValue && confirmLoading.value) handleCancel()
  }
)

// 确认
const handleOk = async () => {
  const $form = formRef.value as FormInstance
  await $form.validate()
  confirmLoading.value = true
  const data = { ...formData }
  emits('submit', data)
}

// 取消
const handleCancel = () => {
  const $form = formRef.value as FormInstance
  $form.resetFields()
  visible.value = false
  formData.remark = ''
  formData.isSave = 1
}
</script>

<style lang="scss">
.remark-modal-wrapper {
  .fs-form-item-control-input > div.fs-form-item-control-input-content {
    height: 100% !important;
  }
}
</style>
