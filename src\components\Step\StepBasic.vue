<template>
  <div class="step-main marginB24">
    <div class="step-title-box marginB8">
      <FTooltip>
        <template #title>{{ item.topicName || item?.contentData?.milepostName }}</template>
        <p
          class="title"
          :style="{ 'font-weight': item.status === 2 ? 'bold' : 'normal', color: item.status === -99999 ? '#bbb' : '' }"
        >
          {{ item.topicName || item?.contentData?.milepostName }}
        </p>
      </FTooltip>
      <span v-if="item.status === 4" class="completed">
        <span class="iconfont">&#xe610;</span>{{ i18n.t('流程终止') }}
      </span>
    </div>
    <FTooltip placement="bottomLeft" color="#fff" :overlay-style="{ width: 'auto', maxWidth: '400px' }">
      <template #title>
        <div style="display: flex; color: #333; margin-bottom: 8px" v-if="item.baseFormData?.sys_completion_tag">
          <span style="margin-right: 4px; color: #999; white-space: nowrap">[终止原因]</span>
          <FTag color="error" size="small">{{ item.baseFormData?.sys_completion_tag ?? '--' }}</FTag>
        </div>
        <div style="display: flex; color: #333; margin-bottom: 8px" v-if="item.baseFormData?.sys_completion_reason">
          <span style="margin-right: 4px; color: #999; white-space: nowrap" class="label">[终止说明]</span>
          <span>{{ item.baseFormData?.sys_completion_reason ?? '--' }}</span>
        </div>
        <div style="display: flex; color: #333">
          <span style="margin-right: 4px; color: #999; white-space: nowrap" class="label">[备注]</span>
          <span>{{ (item.formData?.describe && getHTMLSpingtext(item.formData.describe)) || '--' }}</span>
        </div>
      </template>
      <div class="plan" v-if="[0, 1, 2].includes(item.status) && item.invalid">
        <span class="iconfont fontSize14" style="margin-right: 2px">&#xe668;</span>
        <span>
          {{ i18n.t('计划时间') }}：{{ transformDate(item.forcastTime, 'MM/DD') }}
          {{ transformDate(item.forcastTime, 'HH') <= '12' ? 'PM' : 'AM' }}
        </span>
      </div>
      <div class="dept" :style="{ color: item.status === 2 ? '#666' : '#bbb' }">
        <span>{{ item.superviser }}</span>
        <template v-if="item.invalid">
          <template v-if="[3, 5].includes(item.status) && props.index !== 0">
            <br />
            <span class="inlne-block mt8"
              >{{ i18n.t('完成时间') }} {{ transformDate(item.completeTime, 'YYYY-MM-DD') }}</span
            >
            <br />
            <span class="inlne-block mt8">{{ i18n.t('实际耗时') }} {{ item.realityDuration }} {{ i18n.t('天') }}</span>
          </template>
          <span v-if="item.status === 5 || (item.status === 2 && item.overdueDuration)" style="color: #ee2727">
            <span class="iconfont fontSize14 icon-spase">&#xe633;</span>{{ i18n.t('延期') }}
            {{ item.overdueDuration }}
            {{ i18n.t('天') }}
          </span>
          <span v-if="item.status === 2 && item.remainderDuration" style="color: #febc2e">
            <span class="iconfont fontSize14 icon-spase">&#xe668;</span>{{ i18n.t('剩余') }}
            {{ item.remainderDuration }}
            {{ i18n.t('天') }}
          </span>
        </template>
        <!-- <span v-if="item.status === 3 && item.isOverdue === 0" style="color: #999">
          <span class="iconfont fontS12 icon-spase">&#xe632;</span>按时完成
        </span> -->
      </div>
    </FTooltip>
    <!-- <div class="content" v-if="item.formData?.describe" :title="getHTMLSpingtext(item.formData.describe)">
      {{ getHTMLSpingtext(item.formData.describe) }}
    </div> -->
    <div class="step-line">
      <!-- 0未开始，1待领取，2已领取-处理中，3已完成， 4办结， 5逾期完成 -->
      <div
        class="line-style"
        :data-status="item.status"
        :style="{ background: getColor(item) }"
        v-if="index !== data.length - 1"
      />
      <span v-if="item.status === 4" class="iconfont">&#xe65e;</span>
      <img v-else :src="require(`@/assets/images/bpm/step-${getStepImg(item)}.png`)" />
    </div>
  </div>
</template>
<script setup lang="ts">
import { transformDate, getHTMLSpingtext, useI18n } from '@/utils'
import { IProcess } from '@/types/handle'

interface IProps {
  item: IProcess
  data: IProcess[]
  index: string | number
}

const i18n = useI18n()
const props = defineProps<IProps>()

const getStepImg = (data: Record<string, unknown>) => {
  let state = data.status
  if (state === 2) {
    const isOverdue = data.isOverdue
    if (isOverdue === 1) {
      state = 22 //逾期
    } else {
      state = 99 // 未逾期
    }
  }
  switch (state) {
    case 22:
      return 3
    case 99:
      return 2
    case 3:
    case 4:
    case 5:
      return 1
    case 0:
    default:
      return 4
  }
}
// 0未开始，1待领取，2已领取-处理中，3已完成， 4办结， 5逾期完成
const getColor = (t: any) => {
  let state = t.status
  if (state === 2) {
    const isOverdue = t.isOverdue
    if (isOverdue === 1) {
      state = 22 //逾期
    } else {
      state = 99 // 未逾期
    }
  }
  switch (state) {
    case 22:
      return '#ff4a4a'
    case 99:
      return '#FEBC2E'
    case 3:
    case 5:
      return '#D9F1EB'
    default:
      return '#EBEBEB'
  }
}
// const getOverTime = (item: IProcess): number => {
//   if (!item.nodeStartTime) return 0

//   const status = item.status
//   const startTime = new Date(item.nodeStartTime)
//   const currTime = status == 5 ? new Date(item.completeTime) : new Date()

//   let count = 0
//   let currDay = new Date(startTime.getTime())
//   currDay.setDate(currDay.getDate() + 1)
//   while (currDay <= currTime) {
//     if (currDay.getDay() !== 0) count += 1
//     // else if (currDay.getDay() === 6) count += 0.5

//     currDay.setDate(currDay.getDate() + 1)
//   }

//   const workday = (item.duration - count).toFixed(1)
//   const [integer, decimal] = workday.split('.')
//   if (Number(decimal) === 0) return Number(integer)
//   if (Number(decimal) > 5) return Number(integer) > 0 ? Number(integer) + 1 : Number(integer) - 1
//   return Number(integer) + 0.5
// }
</script>
<style lang="scss" scoped>
.mt8 {
  margin-top: 8px;
}
.inlne-block {
  display: inline-block;
}
.delay {
  background-color: #ff4a4a !important;
}
.marginB8 {
  margin-bottom: 8px;
}
.marginB24 {
  margin-bottom: 24px;
}
.step-main {
  width: 100%;
  height: 90px;
  position: relative;
  .step-title-box {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  .completed {
    color: #3dcca6;
    font-weight: 700;
    margin-left: 5px;
    .iconfont {
      font-size: 12px;
      margin-right: 2px;
    }
  }
}
.title {
  max-width: calc(100% - 10px);
  color: #333;
  margin-bottom: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.plan {
  color: #666;
  margin-bottom: 6px;
}
.dept {
  color: #bbb;
  margin-bottom: 8px;
  .icon-spase {
    margin-left: 3px;
    margin-right: 3px;
  }
}
.content {
  color: #bbb;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 146px;
}
.step-line {
  width: 100%;
  position: absolute;
  bottom: 0;
  .line-style {
    height: 2px;
    background: #d9f1eb;
  }
  > img {
    position: absolute;
    bottom: 0;
    transform: translateY(50%);
  }
  > .iconfont {
    color: #3dcca6;
    position: absolute;
    bottom: 0;
    transform: translateY(50%);
  }
}
</style>
