.fl {
  float: left;
}
.fr {
  float: right !important;
}
.overHidden {
  overflow: hidden;
}
//宽度
.w { width: 100%; }
.width120 {
  width: 120px;
}
.width240 {
  width: 240px;
}
//间距
.marginR5 {
  margin-right: 5px;
}
.marginR8 {
  margin-right: 8px;
}
.marginB5 {
  margin-bottom: 5px;
}
.marginB20 {
  margin-bottom: 20px;
}
.marginR20 {
  margin-right: 20px;
}
.marginR24 {
  margin-right: 24px;
}
.marginR2 {
  margin-right: 2px;
}
.marginR7 {
  margin-right: 7px;
}
.marginR4 {
  margin-right: 4px;
}
.marginR6 {
  margin-right: 6px;
}
.marginT8 {
  margin-top: 8px;
}
.marginB8 {
  margin-bottom: 8px;
}
.marginL12 {
  margin-left: 12px;
}
.marginL4 {
  margin-left: 4px;
}
.marginR12 {
  margin-right: 12px;
}
.marginR10 {
  margin-right: 10px;
}
.marginT10 {
  margin-top: 10px;
}
.marginB10 {
  margin-bottom: 10px;
}
.marginB16 {
  margin-bottom: 16px;
}
.marginT20 {
  margin-top: 20px;
}
.marginT24 {
  margin-top: 24px;
}
.marginL24 {
  margin-left: 24px;
}
.marginL20 {
  margin-left: 20px;
}
.marginR16 {
  margin-right: 16px;
}
.marginB12 {
  margin-bottom: 12px;
}
.marginB24 {
  margin-bottom: 24px;
}
.marginR9 {
  margin-right: 9px;
}
.marginL16 {
  margin-left: 16px;
}
//字体大小
.fontSize12{
  font-size: 12px !important;
}
.fontSize14 {
  font-size: 14px;
}
.fontSize16 {
  font-size: 16px;
}
.fontSize18 {
  font-size: 18px;
}
//字体颜色
.colorBBB{
  color: #bbb;
}
.color999{
  color: #999;
}
.color333{
  color:#333 !important;
}
.color666{
  color: #666 !important;
}
.color4677C7{
  color: #4677C7
}
.colord8d8d8{
  color: #D8D8D8;
}
.fontWeigtht500 {
  font-weight: 500;
}
.fontWeight600 {
  font-weight: 600;
}
.cursor {
  cursor: pointer;
}
.iconfont-hover:hover{
  width: 20px;
  height: 20px;
  background: #E1E8F0;
  border-radius: 2px;
}
.in-block {
  display: inline-block;
}
//筛选样式
.select-options {
  padding: 20px;
  box-shadow: 0 5px 15px 0 rgb(223 226 230 / 80%);
  background: #fff;
  border-radius: 4px;
  .btn_div_relative {
    display: inline-block;
    position: relative;
    height: 32px;
  }
  .btn_span_absolute {
    position: absolute;
    top: -9px;
    left: 5px;
    z-index: 10;
    color: #999;
    background: #fff;
    font-size: 12px;
    -webkit-transform: scale(0.91);
    -ms-transform: scale(0.91);
    -moz-transform: scale(0.91);
    transform: scale(0.91);
    padding: 0 2px;
  }
}

//表格卡片样式
.fei-su-card {
  background: #FFFFFF;
  box-shadow: 0px 2px 8px 0px rgba(88,98,110,0.08);
  border-radius: 4px;
  margin-top: 20px;
  padding: 24px 24px 0px;
}
//表格样式
.fs-table-container table > thead > tr:first-child th:first-child{
  border-radius: 3px !important;
}
.fs-table-container table > thead > tr:last-child th:first-child{
  border-radius: 3px !important;
}
.fs-table-thead > tr > th {
  background: #EBF3FD !important;
  color: #333 !important;
  font-weight: 500 !important;
  border-bottom: none !important;
}
.fs-table-thead > tr > th:not(:last-child):not(.fs-table-selection-column):not(.fs-table-row-expand-icon-cell):not([colspan])::before{
  display: none !important
}
.fs-table-tbody > tr > td {
  color: #333 !important;
  border-bottom: 1px solid #F1F4F8 !important
}
.fs-table-tbody tr:nth-child(2n) td{
  background: #FBFDFF;
}
.fs-table-tbody > tr.fs-table-row:hover > td, .fs-table-tbody > tr > td.fs-table-cell-row-hover{
  background-color: #EBF3FD;
}

//分页器
.fs-pagination-item{
  border: 1px solid #CCCCCC !important
}
.fs-pagination-item a{
  color: #333333 !important
}
.fs-pagination-item:hover{
  border: 1px solid #4677C7 !important
}
.fs-pagination-item a:hover{
  color: #4677C7 !important
}
.fs-pagination-item-active a{
  color: #4677C7 !important;
  font-weight: normal !important;
}
.fs-pagination-item.fs-pagination-item-active{
  border: 1px solid #4677C7 !important
}

//弹框样式
.fs-modal-header {
  padding: 12.5px 24px !important;
  border-radius: 3px 3px 0 0;
}
.fs-modal-close-x {
  height: 48px !important;
  line-height: 48px !important;
  font-size: 14px !important;
}
.fs-modal-footer {
  border-top: 1px solid #EEEEEE !important;
  // padding: 16px 24px;
}
.fs-modal-footer .fs-btn + .fs-btn:not(.fs-dropdown-trigger){
  margin-left: 12px !important;
}
.fs-modal-footer .fs-btn {
  padding: 0px !important;
  width: 80px !important;
  text-align: center;
}
.fs-form-item-explain-error{
  font-size: 12px !important;
}
//确认弹框样式
.z-pover  .fs-popover-inner-content{
  width: 280px !important;
  height: auto !important;
  padding: 16px !important;
  background: #FFFFFF !important;
  box-shadow: 0px 4px 12px 1px rgba(88,98,110,0.14) !important;
  border-radius: 3px !important;
}
.z-popover .fs-popover-inner-content {
  width: 350px !important;
}
.z-pover  .fs-popover-message {
  padding: 0px  0px 24px 0px !important;
}
.z-pover  .fs-btn {
  width: 64px !important;
  height: 24px !important;
  border-radius: 3px;
  font-size: 12px !important;
}
.z-pover .fs-popover-message > .anticon{
  top: 4px !important;
  margin-top: -2px !important;
}

.z-pover .fs-popover-message-title > p{
  margin-bottom: 4px;
}
//树结构样式
.fs-tree-title {
  font-weight: 400 !important;
  color: #333333 !important;
  font-size: 14px !important;
}
// .fs-tree-switcher {
//   line-height: 22px !important;
// }
// .fs-tree .fs-tree-node-content-wrapper {
//   line-height: 22px !important;
//   padding: 0px !important
// }
//ant样式
.fs-select-selection-selected-value,
.fs-select-dropdown-menu-item,
.fs-select-item-option-content,
.fs-btn,
.fs-table,
.fs-select,
.fs-form label,
.fs-select-selection__placeholder,
.fs-form-explain,
.fs-select-search__field__placeholder,
.fs-select-selection--multiple .fs-select-selection__choice__content,
.fs-input {
  font-size: 12px !important;
}
.fs-form-inline .fs-form-item {
  margin-right: 10px !important;
}
.fs-select-item-option-active:not(.fs-select-item-option-disabled){
  background: #F1F4F8;
}
.fs-select-item-option-selected:not(.fs-select-item-option-disabled){
  background:#E1E8F0;
}
.fs-select-dropdown{
  padding: 0px  4px !important;
}
.fs-select-item{
  border-radius: 3px !important;
  font-weight: normal !important;
  color: #333 !important;
}

.fs-picker-input > input{
  font-size: 12px !important;
}

.fs-input-search .fs-input {
  height: 32px !important;
}
.fs-input-search-button{
  border-left: 1px solid #fff !important
}
.fs-input-affix-wrapper {
  padding: 0px 8px !important;
  height: 32px !important;
}
.fs-form-item-control-input-content{
  height: 32px !important;
  .fs-input {
    height: 100% !important;
  }
}
.fs-textarea  .fs-form-item-control-input-content{
  height: 64px !important;
}

.fs-btn-primary{
  color: #fff !important;
  border-color: #378EEF !important;
  background: #378EEF !important;
}
.fs-btn-primary:hover, .fs-btn-primary:focus{
  color: #fff !important;
  border-color: #5FA4F2 !important;
  background: #5FA4F2 !important;
}
.fs-btn-primary:disabled{
  color: #fff !important;
  border-color: #AFD1F8 !important;
  background: #AFD1F8 !important;
}
.fs-btn-primary, .fs-btn{
  padding: 0px 16px !important;
  font-size: 14px !important;
  border-radius: 3px !important;
}
.fs-card-body{
  padding: 24px 22px 24px 24px !important;
}
// .fs-row .fs-col-12{
//   width: 316px !important;
//   flex: none;
// }
.fs-col-16{
  max-width: 100% !important;
  flex: 0 0 100% !important;
}
.fs-form-item-required {
  font-weight: 400;
  color: #333333 !important;
  line-height: 18px;
}
.fs-form .fs-form-item:last-of-type{
  margin-bottom: 0 !important;
}
.fs-select-single .fs-select-selector .fs-select-selection-search{
  left: 7px;
}



.fei-su-title {
  color: #333;
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 15px;
}
//分页器
.fei-su-pagination {
  display: flex;
  justify-content: flex-end;
  padding: 15px 0 25px;
  >span {
    line-height: 32px;
    color: #bbb;
    margin-right: 10px;
  }
}
.fei-su-tip {
  margin-top: -30px;
}


::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background: rgba(187, 187, 187, 0.5);
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  //-webkit-box-shadow: inset 0 0 6px rgba(255,255,255,0.1);
  border-radius: 3px;
  background: rgba(255, 255, 255, 1);
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 3px;
  box-shadow: inset 0 0 6px rgba(187, 187, 187, 0.5);
  background: rgba(187, 187, 187, 0.5);
}

//抽屉
.custom-class  .fs-drawer-body {
  padding: 0px !important;
  overflow: hidden !important;
}

.form-editor .fs-form-item-control-input-content {
  height: 100%;
}
// pre标签 超出文本自动换行
.pre-warp{
  white-space: pre-wrap;
  word-wrap: break-word;
}


// hash: 防止颜色被 js css 覆盖
.fs-message-success .anticon {
  color: #2FCC83;
}
.fs-message-error .anticon {
  color: #F04141;
}
.fs-message-warning .anticon {
  color: #FA8F23;
}
.fs-message-info .anticon,
.fs-message-loading .anticon {
  color: #378EEF;
}

.fs-badge-count {
  position: absolute;
  top: 0;
  right: 0;
  transform: translate(50%, -50%);
  transform-origin: 100% 0;
}

.fs-badge-count-sm {
  min-width: 14px;
  height: 14px;
  padding: 0;
  font-size: 12px;
  line-height: 14px;
  border-radius: 7px;
}
