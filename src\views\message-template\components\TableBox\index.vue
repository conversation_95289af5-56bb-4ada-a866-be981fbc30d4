<template>
  <div class="table-box-container">
    <div class="header">
      <span class="title">{{ i18n.t('消息模板列表') }}</span>
      <FButton class="export-btn" type="primary" @click="handleTemolateModalFn(undefined)">
        <i class="iconxinzeng iconfont"></i>{{ i18n.t('创建模板') }}
      </FButton>
    </div>
    <FTable
      :data-source="list"
      :loading="loading"
      :columns="columns"
      table-layout="fixed"
      :pagination="false"
      :scroll="{ x: '100%' }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <span :class="[record.status === 1 && 'open', record.status === 0 && 'off']">{{
            (record.status === 0 && i18n.t('禁用')) || (record.status === 1 && i18n.t('启用')) || '--'
          }}</span>
        </template>
        <template v-if="column.dataIndex === 'remarks'">
          <MoreTextTips v-if="record.remarks" :lineClamp="3">
            {{ record.remarks }}
          </MoreTextTips>
        </template>
        <template v-if="column.dataIndex === 'created'">
          <p class="marginB5">{{ record.createdUserName || '--' }}</p>
          <p class="marginB5">
            {{ (record.createdTime && transformDate(record.createdTime, 'YYYY-MM-DD HH:mm:ss')) || '--' }}
          </p>
        </template>
        <template v-if="column.dataIndex === 'update'">
          <p class="marginB5">{{ record.updateUserName || '--' }}</p>
          <p class="marginB5">
            {{ (record.updateTime && transformDate(record.updateTime, 'YYYY-MM-DD HH:mm:ss')) || '--' }}
          </p>
        </template>
        <template v-if="column.dataIndex === 'handle'">
          <FRow :gutter="[12, 0]">
            <FCol :span="6">
              <TipBtn :tip-title="i18n.t('编辑')">
                <i class="iconfont icontubiao_xietongbianji hover-btn" @click="handleTemolateModalFn(record)"></i>
              </TipBtn>
            </FCol>
            <FCol :span="6">
              <TipBtn
                has-pop
                :tip-title="i18n.t('删除')"
                :pop-title="i18n.t('确定删除选中的消息模板吗？')"
                @onConfirmFn="onDeleteConfirmFn(record)"
              >
                <i class="iconfont icontubiao_xietongshanchu hover-btn"></i>
              </TipBtn>
            </FCol>
            <FCol :span="6">
              <TipBtn :tip-title="i18n.t('测试同步发送消息')">
                <i class="iconfont iconyijiantongbu hover-btn" @click="sendMsgTemolateModalFn(record)"></i>
              </TipBtn>
            </FCol>
          </FRow>
        </template>
      </template>
    </FTable>
    <AddTemolateModal
      :type="type"
      v-model:visible="visible"
      :data="fieldData"
      @getFieldList="emits('getProcessList')"
    />
    <SendMsgTemolateModal v-model:visible="sendMsgVisible" :data="sendMsgFieldData" />
  </div>
</template>

<script setup lang="ts">
import { PageMessageTemplateRes } from '@/types/messageTemplate'
import MoreTextTips from '@/components/MoreTextTips/index'
import AddTemolateModal from '../AddTemolateModal/index.vue'
import SendMsgTemolateModal from '../SendMsgTemolateModal/index.vue'
import TipBtn from '../TipBtn/index.vue'
import { columns } from './tableConfig'
import { useI18n, transformDate } from '@/utils'
import { deleteMessageTemplate } from '@/api'
import { ref } from 'vue'
import { message } from '@fs/smart-design'

const i18n = useI18n()

type propsType = {
  list: PageMessageTemplateRes[]
  loading: boolean
}
const props = withDefaults(defineProps<propsType>(), {
  list: () => [],
  loading: false,
})
const emits = defineEmits(['getProcessList'])
const visible = ref<boolean>(false)
const type = ref<boolean>(false) // true 新增 false 编辑
const fieldData = ref<any>()
const sendMsgVisible = ref<boolean>(false)
const sendMsgFieldData = ref<any>()

const sendMsgTemolateModalFn = (data: any) => {
  sendMsgVisible.value = true
  sendMsgFieldData.value = data
}

const handleTemolateModalFn = (data: any) => {
  visible.value = true
  type.value = !data
  fieldData.value = data
}

const onDeleteConfirmFn = async (record: any) => {
  if (!record.id) return
  const res = await deleteMessageTemplate(record.id)
  if (res.code !== 200) throw new Error(res.msg)
  message.success(i18n.t('删除成功'))
  emits('getProcessList')
}
</script>

<style scoped lang="scss">
.table-box-container {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    .title {
      font-size: 14px;
      font-weight: 500;
      color: #333333;
    }
  }
  :deep(.fs-table-cell) {
    &:empty {
      &::after {
        content: '--';
      }
    }
    &.fs-table-cell-fix-right-first {
      &:empty {
        &::after {
          content: '';
        }
      }
    }
  }

  .off {
    display: inline-block;
    padding: 0 3px;
    background: #eafaf2;
    border-radius: 2px;
    border: 1px solid #f04141;
    color: #f04141;
    line-height: 18px;
    background: #fdecec;
  }
  .open {
    display: inline-block;
    padding: 0 3px;
    background: #eafaf2;
    border-radius: 2px;
    border: 1px solid #2fcc83;
    color: #2fcc83;
    line-height: 18px;
  }
  .mr12 {
    margin-right: 12px;
  }
  .hover-btn {
    color: #378eef;
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    &:hover {
      background-color: #d8d8d8;
    }
  }
}
</style>
