<template>
  <div class="breadcrumb-wrapper">
    <FBreadcrumb>
      <FBreadcrumbItem v-for="item in props.data" :key="item">{{ item }}</FBreadcrumbItem>
    </FBreadcrumb>
  </div>
</template>

<script setup lang="ts">
interface IProps {
  data: string[]
}
const props = defineProps<IProps>()
</script>

<style scoped lang="scss">
.breadcrumb-wrapper {
  display: flex;
  align-items: center;
  height: 42px;
  margin: -24px -24px 0 -24px;
  padding: 0 24px;
  background: #fbfdff;
  box-shadow: 0px 2px 8px 0px #58626e14;
}
</style>
