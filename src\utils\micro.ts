/* eslint-disable @typescript-eslint/no-explicit-any */
import { debounce } from './common'

/**
 * @description: 获取 micro app 实例
 * <AUTHOR>
 * @date 2022-11-21
 * @returns any
 */
export const getMicroAppInstance = () => {
  return window.microApp
}

/**
 * @description: 获取基础路由
 * <AUTHOR>
 * @date 2022-11-21
 * @returns string
 */
export const BaseRoute = window.__MICRO_APP_BASE_ROUTE__ || '/'

/**
 * @description: 是否是微前端环境
 * <AUTHOR>
 * @date 2022-11-21
 * @returns boolean
 */
export const isMicroApp = window.__MICRO_APP_ENVIRONMENT__ ?? false

/**
 * @description 获取主应用下发的全局数据
 * <AUTHOR>
 * @date 2022-07-15
 * @returns {void} void
 */
export const getMicroGlobalData = () => {
  if (isMicroApp) {
    return window.microApp.getGlobalData()
  } else {
    throw new Error('非微前端环境')
  }
}

/**
 * @description 获取主应用下发的数据
 * <AUTHOR>
 * @date 2022-07-15
 * @returns {void} void
 */
export const getMicroAppData = () => {
  if (isMicroApp) {
    return window.microApp.getData()
  } else {
    throw new Error('非微前端环境')
  }
}

/**
 * @description 设置主应用的头部是否显示
 * <AUTHOR>
 * @date 2022-07-15
 * @param {any} microGlobalData:ecord<string, any>
 * @param {any} isShow:boolean
 * @returns {void} void
 */
export const setMainAppHeaderShow = (microGlobalData: Record<string, any>, isShow: boolean) => {
  microGlobalData?.methods?.setIsHeaderShow(isShow)
}

/**
 * @description 设置主应用的侧边栏是否显示
 * <AUTHOR>
 * @date 2022-07-15
 * @param {any} microGlobalData:ecord<string, any>
 * @param {any} isShow:boolean
 * @returns {void} void
 */
export const setMainAppSidebarShow = (microGlobalData: Record<string, any>, isShow: boolean) => {
  microGlobalData?.methods?.setIsSidebarShow(isShow)
}

/**
 * @description 设置主应用的头部和侧边栏是否显示
 * <AUTHOR>
 * @date 2022-07-15
 * @param {any} microGlobalData: Record<string, any>
 * @param {any} isHeaderShow:boolean
 * @param {any} isSidebarShow?:boolean
 * @returns {void} void
 */
export const setMainAppLayoutShow = (
  microGlobalData: Record<string, any>,
  isHeaderShow: boolean,
  isSidebarShow?: boolean
) => {
  isSidebarShow = isSidebarShow ?? isHeaderShow
  setMainAppHeaderShow(microGlobalData, isHeaderShow)
  setMainAppSidebarShow(microGlobalData, isSidebarShow)
}

/**
 * @description 获取 token
 * <AUTHOR>
 * @date 2022-07-15
 * @returns {any} any
 */
export const getToken = () => {
  if (isMicroApp) {
    const micorGlobalData = getMicroGlobalData()
    const token = micorGlobalData?.methods?.getToken()
    if (token.startsWith('Bearer')) return token
    return `Bearer ${token}`
  }
}

/**
 * @description 获取用户信息
 * <AUTHOR>
 * @date 2022-07-15
 * @returns {any} any
 */
export const getUserInfo = () => {
  if (isMicroApp) {
    const micorGlobalData = getMicroGlobalData()
    const userInfo = micorGlobalData?.methods?.getUserInfo() ?? {}
    userInfo?.uuid && (userInfo.adminId = userInfo.uuid)
    userInfo?.userName && (userInfo.feiShuName = userInfo.userName)
    return userInfo
  }
}

/**
 * @description 获取语言类型
 * <AUTHOR>
 * @date 2022-07-15
 * @returns {string} string
 */
export const getLanguage = () => {
  if (isMicroApp) {
    const micorGlobalData = getMicroGlobalData()
    return micorGlobalData?.methods?.getLanguage() || navigator.language
  }
}

/**
 * @description 更新token
 * @returns Promise<any>
 */
export const updateToken = async () => {
  if (isMicroApp) {
    const micorGlobalData = getMicroGlobalData()
    return micorGlobalData?.methods?.updateToken?.()
  }
}

/**
 * @description 获取权限信息
 * @date 2023-10-07
 * @returns {any} any
 */
export const getPower = () => {
  if (isMicroApp) {
    const micorGlobalData = getMicroGlobalData()
    return micorGlobalData?.methods?.getPower()
  }
}

/**
 * @description: 登出
 * <AUTHOR>
 * @date 2022-07-15
 * @returns {any} any
 */
export const logout = debounce(() => {
  if (isMicroApp) {
    const micorGlobalData = getMicroGlobalData()
    return micorGlobalData?.methods?.logout()
  }
}, 200)
