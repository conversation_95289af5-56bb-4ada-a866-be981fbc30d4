<template>
  <div class="add-field-container">
    <FModal
      width="420px"
      v-model:visible="visible"
      :title="i18n.t('测试同步发送消息')"
      :confirmLoading="confirmLoading"
      @cancel="cancelFn"
      @ok="submitFn"
    >
      <FForm ref="formRef" :model="formState" :rules="rules" layout="vertical">
        <FRow :gutter="[24, 24]">
          <FCol :span="24">
            <FFormItem :label="i18n.t('流程 InstanceId')" name="instanceId">
              <FInput
                style="width: 100%"
                v-model:value="formState.instanceId"
                :placeholder="i18n.t('请输入流程 InstanceId')"
              />
            </FFormItem>
          </FCol>
          <FCol :span="24">
            <FFormItem :label="i18n.t('模板编码')" name="templateCode">
              <FInput
                style="width: 100%"
                v-model:value="formState.templateCode"
                :placeholder="i18n.t('请输入模板编码')"
              />
            </FFormItem>
          </FCol>
          <FCol :span="24">
            <FFormItem :label="i18n.t('人员')" name="uuid">
              <FSelect
                :placeholder="i18n.t('请选择人员')"
                v-model:value="formState.uuid"
                :options="userList"
                option-filter-prop="name"
                :field-names="{ label: 'name', value: 'uuid' }"
                allow-clear
                show-search
              />
            </FFormItem>
          </FCol>
          <FCol :span="24">
            <div style="margin-bottom: 8px">{{ i18n.t('请求结果：') }}</div>
            <pre class="result-box">{{ resultData || '--' }}</pre>
          </FCol>
        </FRow>
      </FForm>
      <div></div>
    </FModal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useI18n } from '@/utils'
import { MessageTemplateParams } from '@/types/messageTemplate'
import type { FormInstance } from '@fs/smart-design/dist/ant-design-vue_es'
import type { Rule } from '@fs/smart-design/dist/ant-design-vue_es/form'
import { message } from '@fs/smart-design'
import { synSendMsgMessageTemplate } from '@/api'
import { useStore } from 'vuex'
import { StoreUserType } from '@/store/modules/user'

interface IProps {
  visible: boolean
  data?: MessageTemplateParams
}

const i18n = useI18n()
const store = useStore()
const props = defineProps<IProps>()
const emits = defineEmits(['update:visible'])
const confirmLoading = ref<boolean>(false)
const visible = computed({
  get: () => props.visible,
  set: val => emits('update:visible', val),
})
const userList = computed<StoreUserType[]>(() => store.state.user.allUser || [])
const formRef = ref<FormInstance>()
const formState = reactive<MessageTemplateParams>({
  instanceId: undefined,
  templateCode: undefined,
  uuid: undefined,
})
const rules: Record<string, Rule[]> = {
  instanceId: [{ required: true, message: i18n.t('请输入流程 InstanceId') }],
  templateCode: [{ required: true, message: i18n.t('请输入模板编码') }],
  uuid: [{ required: true, message: i18n.t('请选择人员') }],
}
const resultData = ref()

const cancelFn = () => {
  formRef.value?.resetFields()
  visible.value = false
}

const submitFn = async () => {
  try {
    confirmLoading.value = true
    if (!formRef.value) {
      return
    }
    await formRef.value.validate()
    const res = await synSendMsgMessageTemplate(formState)
    if (res.code !== 200) throw new Error(res.msg)
    resultData.value = res.msg || i18n.t('测试同步发送消息成功')
    message.success(res.msg || i18n.t('测试同步发送消息成功'))
  } catch (error: any) {
    resultData.value = JSON.stringify(error, null, '\t')
  } finally {
    confirmLoading.value = false
  }
}

const setFieldFn = (data: MessageTemplateParams) => {
  Object.entries(formState).forEach(([key, value]) => {
    formState[key as keyof typeof formState] = data[key as keyof typeof data] || undefined
  })
}

watch(
  () => visible.value,
  val => {
    val && setFieldFn((props.data as MessageTemplateParams) || {})
  }
)
</script>
<style scoped lang="scss">
:deep(.fs-form-item-control-input-content) {
  height: auto !important;
}
.result-box {
  max-height: 240px;
  padding: 12px 16px;
  background: #ebf3fd;
  border-radius: 3px;
  border: 1px solid #afd1f8;
  color: #333333;
  font-size: 12px;
}
</style>
