import { transformDate } from '@/utils'
import { GetTagAndNode, getDepartment } from '@/api'
import { messageInstance } from '@fs/smart-design'
import { ProcessListParams, IOptions } from '@/types/pgbDataBoard'
import { reactive } from 'vue'
import dayjs from 'dayjs'
import i18n from '@/i18n'

const handleTree = (node: any, users: any = []) => {
  return node.reduce((users: any, cur: any) => {
    if (cur.departmentChildrens) {
      const data = JSON.parse(JSON.stringify(cur))
      data.departmentChildrens = JSON.parse(JSON.stringify(data.uuidAndNames || []))
      !((cur.uuidAndNames || []).length + (cur.departmentChildrens || []).length) && (data.disabled = true)
      users.push(data)
      cur.departmentChildrens.length && handleTree(cur.departmentChildrens, data.departmentChildrens)
    }
    return users
  }, users)
}

const checkChildren = (node: any, users: any = []) => {
  return node.reduce((users: any, cur: any) => {
    if (cur.uuid && cur.name && Object.keys(cur).length === 2) {
      users.push(cur.uuid)
    } else if (cur.departmentChildrens && cur.departmentChildrens.length) {
      checkChildren(cur.departmentChildrens, users)
    }
    return users
  }, users)
}

const roleLists = await (async () => {
  const res = await getDepartment()
  if (res.code !== 200) throw new Error(res.msg)
  let data: any = []
  data = handleTree(res.data)
  return data
})()

const processLists = await (async () => {
  const res = await GetTagAndNode()
  let data: any = []
  if (res.code === 200) {
    data = res.data || []
  } else {
    messageInstance.warn(res.msg)
  }
  return data
})()

const filterOption = (input: string, option: any) => option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0

export class Search {
  options: IOptions;
  [key: string]: any
  constructor(callback: any = undefined) {
    this.callback = callback
    this.options = reactive({
      processConfigId: {
        componentName: 'FSelect',
        componentValueKey: 'processConfigId',
        componentValue: undefined,
        componentArgsValue: {},
        componentAttrs: {
          style: {
            width: '120px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          placeholder: i18n.t('请选择'),
          pressLine: i18n.t('流程类型'),
          fieldNames: { label: 'processName', value: 'id' },
          showSearch: true,
          optionFilterProp: 'processName',
          options: processLists,
          allowClear: true,
          onChange: (value: any, option: any) => {
            this.options.tag.componentValue = undefined
            this.options.tag.componentAttrs.options = (option?.dictionarys ?? []).map((tag: any) => ({
              ...tag,
              label: tag.name,
              value: `$.${tag.field}:${tag.value}`,
            }))
            this.options.processConfigId.componentArgsValue = {
              typeOptions: this.options.tag.componentAttrs.options,
            }
            // this.callback(this.submit())
          },
        },
        setComponentValueFormat: (data: any) => {
          this.options.processConfigId.componentValue = (data || {})?.componentValue || undefined
          this.options.processConfigId.componentArgsValue = (data || {})?.componentArgsValue || undefined
          this.options.tag.componentAttrs.options = (data || {})?.componentArgsValue?.typeOptions || []
        },
        clearComponentValueFn: () => {
          this.options.tag.componentAttrs.options = []
        },
      },
      tag: {
        componentName: 'FSelect',
        componentValueKey: 'tag',
        componentValue: undefined,
        componentAttrs: {
          style: {
            width: '120px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          placeholder: i18n.t('请选择'),
          pressLine: i18n.t('需求类型'),
          options: [],
          allowClear: true,
          onChange: () => {
            // this.callback(this.submit())
          },
        },
        setComponentValueFormat: (data: any) => {
          this.options.tag.componentValue = (data || {})?.componentValue || undefined
        },
      },
      projectUuidList: {
        componentName: 'FCascader',
        componentValueKey: 'projectUuidList',
        componentValue: undefined,
        componentArgsValue: {},
        componentAttrs: {
          style: {
            width: '120px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          multiple: true,
          showArrow: true,
          placeholder: i18n.t('请选择'),
          pressLine: i18n.t('项目成员'),
          fieldNames: { label: 'name', value: 'uuid', children: 'departmentChildrens' },
          maxTagCount: 'responsive',
          options: roleLists,
          showSearch: { filterOption },
          showCheckedStrategy: 'SHOW_CHILD',
          allowClear: true,
          onChange: (value: any, selectedOptions: any) => {
            const selectLists =
              selectedOptions.map((item: any) => {
                return item[item.length - 1]
              }) || []
            Object.assign(this.options.projectUuidList.componentArgsValue, { selectLists })
            // this.callback(this.submit())
          },
        },
        getComponentValueFormat: (value: any, argsValue: any) => {
          const projectUuidList = checkChildren(argsValue.selectLists)
          if (projectUuidList.length) {
            return { projectUuidList: projectUuidList }
          } else {
            this.options.projectUuidList.componentValue = undefined
            return undefined
          }
        },
        setComponentValueFormat: (data: any) => {
          this.options.projectUuidList.componentValue = (data || {})?.componentValue || undefined
          this.options.projectUuidList.componentArgsValue = (data || {})?.componentArgsValue || {}
        },
      },
      status: {
        componentName: 'FSelect',
        componentValueKey: 'status',
        componentValue: undefined,
        componentAttrs: {
          style: {
            width: '120px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          placeholder: i18n.t('请选择'),
          pressLine: i18n.t('项目状态'),
          options: [
            { label: i18n.t('进行中'), value: 0 },
            { label: i18n.t('已完成'), value: 1 },
            { label: i18n.t('已办结'), value: 2 },
          ],
          allowClear: true,
          onChange: () => {
            // this.callback(this.submit())
          },
        },
        setComponentValueFormat: (data: any) => {
          this.options.status.componentValue = (data || {}).componentValue ?? undefined
        },
      },
      time: {
        componentName: 'FRangePicker',
        componentValueKey: 'time',
        componentValue: undefined,
        componentAttrs: {
          style: {
            width: '240px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          pressLine: i18n.t('创建时间'),
          placeholder: [i18n.t('开始日期'), i18n.t('结束日期')],
          onChange: () => {
            // this.callback(this.submit())
          },
        },
        getComponentValueFormat: (value: any) => {
          if (!value || value.length !== 2) return undefined
          return {
            startTime: transformDate(value[0], 'YYYY-MM-DD HH:mm:ss'),
            endTime: transformDate(value[1], 'YYYY-MM-DD HH:mm:ss'),
          }
        },
        setComponentValueFormat: (data: any) => {
          this.options.time.componentValue =
            ((data || {})?.componentValue && [dayjs(data.componentValue[0]), dayjs(data.componentValue[1])]) ||
            undefined
        },
      },
      queryInput: {
        componentName: 'FInput',
        componentValueKey: 'queryInput',
        componentValue: undefined,
        componentAttrs: {
          style: {
            width: '240px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          pressLine: i18n.t('快速搜索'),
          placeholder: i18n.t('项目名称/项目编号'),
          onPressEnter: () => {
            // this.callback(this.submit())
          },
        },
        setComponentValueFormat: (data: any) => {
          this.options.queryInput.componentValue = (data || {})?.componentValue || undefined
        },
      },
    })
    // this.init()
  }

  init() {
    // this.callback(this.submit())
  }

  submit() {
    const params: ProcessListParams = {}
    Object.values(this.options).forEach(value => {
      ;(value.componentValue &&
        value.getComponentValueFormat &&
        Object.assign(
          params,
          value.getComponentValueFormat(value.componentValue, value?.componentArgsValue || null)
        )) ||
        (params[value.componentValueKey] = value.componentValue)
    })
    return params
  }

  clear() {
    Object.entries(this.options).forEach(([key, value]) => {
      value.componentValue = undefined
      value.componentArgsValue && (value.componentArgsValue = {})
      value.clearComponentValueFn && value.clearComponentValueFn()
    })
    // this.callback(this.submit())
  }

  setCacheSearch() {
    const cachData: Record<string, any> = {}
    Object.entries(this.options).forEach(([key, value]) => {
      if ((value.componentValue !== undefined && value.componentValue !== null) || value?.componentArgsValue) {
        cachData[key] = {}
        value.componentValue !== undefined &&
          value.componentValue !== null &&
          (cachData[key].componentValue = value.componentValue)
        value.componentArgsValue && (cachData[key].componentArgsValue = value.componentArgsValue)
      }
    })
    return cachData
  }

  setDefaultSearch(data: any) {
    Object.entries(data).forEach(([key, value]) => {
      value && this.options[key].setComponentValueFormat(value)
    })
  }
}
