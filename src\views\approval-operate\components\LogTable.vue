<template>
  <div class="log-table-container">
    <FTable
      :data-source="list"
      :columns="columns"
      table-layout="fixed"
      :loading="tableLoading"
      :pagination="{
        total: pageData.total,
        current: pageData.currPage,
        pageSize: pageData.pageSize,
        showTotal: (total: number) => `${i18n.t('共')}${total}${i18n.t('条')}`,
        showQuickJumper: true,
        showSizeChanger: true,
        onChange: onPaginationChangeFn
      }"
      :scroll="{ x: '100%', y: 'calc(100vh - 458px)' }"
    >
      <template #bodyCell="{ column, text }">
        <template v-if="column.dataIndex === 'type' && text">
          <FTag
            :border="false"
            :color="
              ([1].includes(text) && 'success') ||
              ([3, 4, 6].includes(text) && 'warning') ||
              ([2, 5].includes(text) && 'error')
            "
          >
            {{
              (text === 1 && i18n.t('审批通过')) ||
              (text === 2 && i18n.t('拒绝')) ||
              (text === 3 && i18n.t('加签')) ||
              (text === 4 && i18n.t('减签')) ||
              (text === 5 && i18n.t('驳回')) ||
              (text === 6 && i18n.t('转派'))
            }}
          </FTag>
        </template>
        <template v-if="column.dataIndex === 'nodeMode' && text">
          <span>{{
            (text === 1 && i18n.t('单人审批')) || (text === 2 && i18n.t('会签')) || (text === 3 && i18n.t('或签'))
          }}</span>
        </template>
        <template v-if="column.dataIndex === 'recipient' && text?.length">
          <MoreTextTips :line-clamp="5" v-if="text">
            <div v-for="item in text" :key="item.uuid">{{ item.userName }}</div>
          </MoreTextTips>
        </template>
        <template v-if="['files'].includes(column.dataIndex) && text && text?.length">
          <div class="file-box" v-for="item in text || []" :key="item.url">
            <img class="icon-pic marginR4" :src="getPicFn(item)" />
            <span class="text-name" :title="item.fileName" @click="onPreviewFn(item)">{{ item.fileName }}</span>
            <i class="iconfont icontubiao_xiazai marginL4" @click="downLoadFile(item)"></i>
          </div>
        </template>
        <template v-if="column.dataIndex === 'content'">
          <MoreTextTips :line-clamp="5" v-if="text">
            {{ text }}
          </MoreTextTips>
        </template>
        <template v-if="['createdTime'].includes(column.dataIndex) && text">
          <span>
            {{ (text && transformDate(text, 'YYYY-MM-DD HH:mm:ss')) || '--' }}
          </span>
        </template>
      </template>
    </FTable>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useI18n, transformDate, download, S3URL } from '@/utils'
import { BasicPageParams } from '@/types/processBoard'
import MoreTextTips from '@/components/MoreTextTips/index'
import { getApprovalQueryLog, PageApprovalQueryLogRes } from '@/api'
import pdf from '../image/pdf.png'
import chm from '../image/chm.png'
import divPic from '../image/div.png'
import other from '../image/other.png'
import png from '../image/png.png'
import ppt from '../image/ppt.png'
import txt from '../image/txt.png'

const i18n = useI18n()
const pageData = reactive<BasicPageParams>({
  currPage: 1,
  pageSize: 10,
  total: 0,
})
const tableLoading = ref<boolean>(false)
const list = ref<PageApprovalQueryLogRes[]>()
const { id } = useRoute().params as { id: string }
const columns = ref([
  {
    title: i18n.t('操作人'),
    dataIndex: 'createUser',
    width: 138,
  },
  {
    title: i18n.t('操作时间'),
    dataIndex: 'createdTime',
    width: 160,
  },
  {
    title: i18n.t('节点名称'),
    dataIndex: 'topicName',
    width: 138,
  },
  {
    title: i18n.t('操作方式'),
    dataIndex: 'type',
    width: 107,
  },
  {
    title: i18n.t('审批类型'),
    dataIndex: 'nodeMode',
    width: 107,
  },
  {
    title: i18n.t('接收人'),
    dataIndex: 'recipient',
    width: 138,
  },
  {
    title: i18n.t('审批意见'),
    dataIndex: 'content',
    width: 200,
  },
  {
    title: i18n.t('附件'),
    dataIndex: 'files',
    width: 170,
  },
])

const getLogTableFn = async () => {
  try {
    tableLoading.value = true
    const params = {
      instanceId: id,
      pageNum: pageData.currPage,
      pageSize: pageData.pageSize,
    }
    const res = await getApprovalQueryLog(params)
    if (res.code !== 200) throw new Error(res.msg)
    pageData.total = res?.data?.totalCount || 0
    list.value = res?.data?.list ?? []
  } finally {
    tableLoading.value = false
  }
}

const onPaginationChangeFn = (current: number, pageSize: number) => {
  pageData.currPage = current
  pageData.pageSize = pageSize
  getLogTableFn()
}

const getPicFn = (item: any) => {
  const type = item.fileName.substring(item.fileName.lastIndexOf('.') + 1).toLowerCase()
  if (['pdf'].includes(type)) {
    return pdf
  } else if (['chm'].includes(type)) {
    return chm
  } else if (['png', 'jpg'].includes(type)) {
    return png
  } else if (['ppt'].includes(type)) {
    return ppt
  } else if (['txt'].includes(type)) {
    return txt
  } else if (['div'].includes(type)) {
    return divPic
  }
  return other
}

const onPreviewFn = (item: any) => {
  const httpUrl = `${S3URL}/api/s3/getFileByUrl?url=${encodeURIComponent(item.url)}&filename=${encodeURIComponent(
    item.fileName
  )}&type=1`
  window.open(httpUrl, '_blank')
}

const downLoadFile = (item: any) => {
  download(item.url, item.fileName)
}

onMounted(() => {
  getLogTableFn()
})
</script>

<style scoped lang="scss">
.log-table-container {
  .file-box {
    display: flex;
    height: 32px;
    align-items: center;
    padding: 0 4px;
    &:hover {
      background-color: #f1f4f8;
      border-radius: 2px;
      .icontubiao_xiazai {
        display: inline-block;
      }
    }
    .icon-pic {
      width: 16px;
    }
    .text-name {
      max-width: 78%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
    }
    .icontubiao_xiazai {
      display: none;
      flex: 1;
      text-align: right;
      font-size: 16px;
      color: #999;
      cursor: pointer;
    }
  }
  .hover-btn {
    color: #378eef;
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    &:hover {
      background-color: #d8d8d8;
    }
  }
  .status-box {
    display: flex;
    align-items: center;
    &.no-view {
      .iconfont {
        color: #bbbbbb;
      }
    }
    &.wait-check {
      .iconfont {
        color: #f04141;
      }
    }
    &.success {
      .iconfont {
        color: #2fcc83;
      }
    }
  }
  :deep(.fs-table-tbody) {
    .fs-table-cell {
      &:empty {
        &::after {
          content: '--';
        }
      }
      &.fs-table-cell-fix-right-first {
        &:empty {
          &::after {
            content: '';
          }
        }
      }
    }
  }
  :deep(.fs-pagination) {
    margin-bottom: 0;
  }
}
</style>
