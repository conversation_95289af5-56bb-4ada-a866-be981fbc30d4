<template>
  <FModal
    width="1200px"
    v-model:visible="visible"
    :title="i18n.t('项目类型')"
    :body-style="{ padding: 0 }"
    :footer="null"
  >
    <!-- 侧边栏 -->
    <div class="create-process-wrapper">
      <div class="create-process-nav">
        <div class="create-process-nav-group">
          <p class="create-process-nav-group-type">{{ i18n.t('常用') }}</p>
          <p
            title="常用流程"
            :class="['create-process-nav-group-item', active == COMMON ? 'active' : '']"
            @click="handleCommonClick"
          >
            {{ i18n.t('常用流程') }}
          </p>
        </div>
        <div class="create-process-nav-group">
          <p class="create-process-nav-group-type">{{ i18n.t('流程') }}</p>
          <p
            v-for="[key] in processGroupData"
            :class="['create-process-nav-group-item', active == key ? 'active' : '']"
            :key="key"
            :title="key || i18n.t('其他')"
            @click="handleAnchorClick(key)"
          >
            {{ key || i18n.t('其他') }}
          </p>
        </div>
      </div>
      <!-- 内容区域 -->
      <div ref="mainRef" class="create-process-main" @scroll="handleScroll">
        <div class="process-form">
          <FInput
            class="w276"
            v-model:value="formData.processName"
            @change="handleFormDataChange(formData.processName)"
            :placeholder="i18n.t('请输入')"
          >
            <template #suffix>
              <Icon icon="icontubiao_sousuo1" color="#bbb" size="14" />
            </template>
          </FInput>
          <div class="__shadow"></div>
        </div>

        <!-- 常规列表 -->
        <div
          :class="['process-group', `__${key}`]"
          v-show="!formData.processName && active !== COMMON"
          v-for="[key, processList] in processGroupData"
          :id="key"
          :key="key"
        >
          <p class="process-group-name">{{ key || i18n.t('其他') }}</p>
          <ProcessItem
            v-for="process in processList"
            :data="process"
            :key="process.id"
            @click="handleProcessClick(process)"
            @collect="handleCollectClick"
          />
        </div>

        <!-- 常用列表 -->
        <div
          class="process-group"
          v-show="!formData.processName && active === COMMON"
          v-for="[key, processList] in processCommonData"
          :id="key"
          :key="key"
        >
          <p class="process-group-name">{{ key }}</p>
          <ProcessItem
            v-for="process in processList"
            :data="process"
            :key="process.id"
            @click="handleProcessClick(process)"
            @collect="handleCollectClick"
          />
          <FEmpty
            v-show="!processList.length"
            style="margin-top: 16px"
            :image="imgEmpty"
            :description="key === i18n.t('我的收藏') ? i18n.t('暂无收藏，点击星星图标可收藏') : i18n.t('暂无访问记录')"
          />
        </div>

        <!-- 查询时候的显示列表 -->
        <div class="process-group" v-show="formData.processName">
          <ProcessItem
            v-for="process in processQueryData"
            :data="process"
            :key="process.id"
            @click="handleProcessClick(process)"
            @collect="handleCollectClick"
          />
        </div>
      </div>
    </div>
  </FModal>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, reactive, ref } from 'vue'
import { LocationQueryRaw, useRouter } from 'vue-router'
import { messageInstance as message } from '@fs/smart-design'
import {
  deleteProcessCollect,
  getAllClassList,
  getClassList,
  getCollectApprovalProcess,
  getCommonApprovalProcess,
  getFlowChartProcessAll,
  getProcessCollect,
  getProcessCommon,
  saveProcessCollect,
  getApprovalCreateForm,
  getAllCreateProcessList,
} from '@/api'
import { IClassData } from '@/types/class'
import { debounce, useI18n } from '@/utils'

import Icon from '@/components/Icon/index.vue'
import ProcessItem from './ProcessItem.vue'

import imgEmpty from '@/assets/images/empty.png'

interface IProps {
  modelValue: boolean
  params?: unknown // 流程内创建关联流程的参数
  source?: 'BPM' | 'approval' // 流程类型 流程来源 BPM 审批
}

const COMMON = 'common'

const i18n = useI18n()
const router = useRouter()
const props = defineProps<IProps>()
const emits = defineEmits(['update:modelValue'])
const processSource = computed(() => props.source || 'BPM')
const visible = computed({ get: () => props.modelValue, set: val => emits('update:modelValue', val) })
const mainRef = ref<HTMLElement>()
const black = new Function(`return ${process.env.VUE_APP_DISABLED_BLACK}`)()

const active = ref<string>('')
const activeScrollFlag = ref<boolean>(true)
const formData = reactive({ processName: '' })
const allProcessData = ref<IClassData[]>([])

const collectMap = ref<Map<number, number>>(new Map())
const collectProcessIds = ref<number[]>([])
const commonProcessData = ref<IClassData[]>([])

const processQueryData = ref<IClassData[]>([])
const processGroupData = computed(() => {
  const data = TFProcessCollect(allProcessData.value).filter(item => !black.includes(item.id))
  return TFProcessType(data)
})
const processCommonData = computed(() => {
  const data = TFProcessCollect(allProcessData.value)
  const map = new Map<string, IClassData[]>()
  const collectData = data.filter(item => item.collect)
  map.set(
    i18n.t('最近访问'),
    commonProcessData.value.filter(item => !black.includes(item.id))
  )
  map.set(
    i18n.t('我的收藏'),
    collectData.filter(item => !black.includes(item.id))
  )
  return map
})

onMounted(async () => {
  await queryProcessCollect()
  queryAllProcessList()
  await queryCommonProcess()
  if (collectProcessIds.value.length + commonProcessData.value.length) handleCommonClick()
})

// 点击锚点
const handleAnchorClick = (id: string) => {
  formData.processName = ''
  processQueryData.value = []
  activeScrollFlag.value = false
  active.value = id
  nextTick(() => {
    if (!mainRef.value) return
    const $main = mainRef.value
    const $target = $main.querySelector(`.__${id}`) as HTMLElement // id 有特殊字符，所以额外使用 class 来定位
    if ($target) $main.scrollTop = $target.offsetTop - 80
  })
}

// 滚动事件
const handleScroll = debounce((e: Event) => {
  if (!activeScrollFlag.value) return (activeScrollFlag.value = true)
  if (active.value === COMMON || formData.processName) return

  const $target = e.target as HTMLElement
  const $gourps = $target.querySelectorAll('.process-group')
  const $active = Array.from($gourps).find($item => {
    const $itemTarget = $item as HTMLElement
    const data = $itemTarget.offsetTop - $target.scrollTop
    return data > 0 && data < 200
  })
  $active && ($active as HTMLElement).id && (active.value = ($active as HTMLElement).id)
}, 100)

// 处理常用流程点击事件
const handleCommonClick = () => {
  formData.processName = ''
  processQueryData.value = []
  active.value = COMMON
  nextTick(() => {
    if (!mainRef.value) return
    const $main = mainRef.value
    $main.scrollTop = 0
  })
}

// 处理收藏点击事件
const handleCollectClick = async (process: IClassData) => {
  if (process.collect) {
    await delCollectProcess(process.id)
  } else {
    await collectProcess(process.id)
  }
  queryProcessCollect()
}

// 创建流程跳转
const handleProcessClick = async (item: IClassData) => {
  let res
  if (processSource.value === 'approval') res = await getApprovalCreateForm(item.id)
  const params = { id: item.id, processDefineKey: (res && res?.data?.id) || item.processDefineKey }
  router.push({ name: 'DemandAdd', params, query: props.params as LocationQueryRaw })
  visible.value = false
}

// 收藏流程
const collectProcess = async (id: number) => {
  await saveProcessCollect(id)
  message.success(i18n.t('添加成功'))
}

// 取消收藏流程
const delCollectProcess = async (id: number) => {
  const collectId = collectMap.value.get(id) as number
  await deleteProcessCollect(collectId)
  message.success(i18n.t('移除成功'))
}

// 获取用户收藏流程
const queryProcessCollect = async () => {
  let res
  if (processSource.value === 'approval') res = await getCollectApprovalProcess()
  if (processSource.value === 'BPM') res = await getProcessCollect()

  const { data = [] } = res
  collectMap.value = new Map(data.map(item => [item.configId, item.id]))
  collectProcessIds.value = (data || []).map(item => item.configId)
}

// 获取用户常用流程
const queryCommonProcess = async () => {
  let res
  if (processSource.value === 'approval') res = await getCommonApprovalProcess()
  if (processSource.value === 'BPM') res = await getProcessCommon()

  const { data = [] } = res
  TFProcessCollect(data)
  commonProcessData.value = data
}

// 查询流程列表
const queryProcessList = async (processName: string) => {
  let res
  if (processSource.value === 'approval') res = await getFlowChartProcessAll(processName)
  if (processSource.value === 'BPM') res = await getAllCreateProcessList({ processName })

  const { data = [] } = res
  active.value = ''
  processQueryData.value = data.filter(item => !black.includes(item.id))
}

// 查询流程列表
const queryAllProcessList = async () => {
  let res
  if (processSource.value === 'BPM') res = await getAllCreateProcessList()
  if (processSource.value === 'approval') res = await getFlowChartProcessAll()

  const { data = [] } = res
  active.value = ''
  allProcessData.value = data ?? []
}

// 处理表单数据变化
const handleFormDataChange: (name: string) => void = debounce(queryProcessList, 200)

// 处理收藏
const TFProcessCollect = (data: IClassData[]) => {
  const collect = collectProcessIds.value
  return data.map(item => ({ ...item, collect: collect.includes(item.id) }))
}
// 处理流程分类
const TFProcessType = (data: IClassData[]) => {
  const map = new Map()
  data.forEach((item: IClassData) => {
    if (!item.businessType) item.businessType = i18n.t('其他')
    map.has(item.businessType) ? map.get(item.businessType).push(item) : map.set(item.businessType, [item])
  })
  return map
}
</script>

<style lang="scss" scoped>
.fr {
  float: right;
}
.w276 {
  width: 276px !important;
}
.create-process-wrapper {
  display: flex;
  .create-process-nav {
    width: 196px;
    padding: 8px 0;
    border-right: 1px solid #eee;
    box-sizing: border-box;
    .create-process-nav-group {
      margin-bottom: 16px;

      .create-process-nav-group-type,
      .create-process-nav-group-item {
        padding: 0 26px;
        margin-bottom: 2px;
        height: 35px;
        line-height: 35px;
      }
      .create-process-nav-group-type {
        color: #999999;
        font-size: 12px;
      }
      .create-process-nav-group-item {
        position: relative;
        color: #333333;
        font-size: 14px;
        cursor: pointer;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &.active {
          background-color: #ebf3fd;

          &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            width: 2px;
            background-color: #1890ff;
          }
        }

        &:hover {
          background-color: #f1f4f8;
        }
      }
    }
  }
  .create-process-main {
    position: relative;
    flex: 1;
    height: 628px;
    padding: 0 16px;
    overflow: hidden;
    overflow-y: auto;

    // background: linear-gradient(#fff, #fff), linear-gradient(rgba(0, 0, 0, 0.8), transparent 100%);
    // background-size: 100% 80px, 100% 75px;
    // background-repeat: no-repeat;
    // background-attachment: local, scroll;

    .process-form {
      position: sticky;
      top: 0;
      padding: 24px 32px 12px 32px;
      margin: 0 -16px 12px -16px;
      background-color: #fff;
    }

    .process-group {
      margin-right: -30px;
      margin-bottom: 36px;
      overflow: hidden;
      .process-group-name {
        height: 24px;
        line-height: 24px;
        padding: 0 16px;
        color: #333333;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}

:deep(.fs-empty-description) {
  margin-top: -35px;
}
</style>
