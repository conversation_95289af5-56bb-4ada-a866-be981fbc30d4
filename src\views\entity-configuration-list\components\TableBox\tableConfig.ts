import type { TableColumnsType } from 'ant-design-vue'
import { VNode, h, computed } from 'vue'
import { transformDate } from '@/utils'
import i18n from '@/i18n'

export const columns = computed<TableColumnsType>(() => [
  {
    title: i18n.t('实体分类'),
    dataIndex: 'entityClass',
    width: 132,
  },
  {
    title: i18n.t('实体名称'),
    dataIndex: 'name',
    width: 120,
  },
  {
    title: i18n.t('实体编码'),
    dataIndex: 'code',
    width: 120,
  },
  {
    title: i18n.t('状态'),
    dataIndex: 'status',
    width: 120,
  },
  {
    title: i18n.t('实体类型'),
    dataIndex: 'entityType',
    customRender: ({ text, value, record }): VNode => {
      return h('span', (text === 0 && i18n.t('主实体')) || (text === 1 && i18n.t('明细实体')) || '--')
    },
    width: 84,
  },
  {
    title: i18n.t('主实体'),
    dataIndex: 'masterEntityName',
    width: 140,
  },
  {
    title: i18n.t('备注说明'),
    dataIndex: 'describe',
    width: 308,
  },
  {
    title: i18n.t('创建人'),
    dataIndex: 'createdUserName',
    width: 137,
  },
  {
    title: i18n.t('创建时间'),
    dataIndex: 'createdTime',
    width: 160,
    customRender: ({ text, value, record }): VNode => {
      return h('span', transformDate(text, 'YYYY-MM-DD HH:mm:ss'))
    },
  },
  {
    title: i18n.t('操作'),
    dataIndex: 'handle',
    fixed: 'right',
    width: 96,
  },
])
