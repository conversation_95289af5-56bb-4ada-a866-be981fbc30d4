import type { TableColumnsType } from 'ant-design-vue'
import { computed } from 'vue'
import i18n from '@/i18n'

export const columns = computed<TableColumnsType>(() => [
  {
    title: i18n.t('序号'),
    dataIndex: 'sort',
    fixed: 'left',
    width: 90,
  },
  {
    title: i18n.t('字段名称'),
    dataIndex: 'fieldName',
    fixed: 'left',
    width: 120,
  },
  {
    title: i18n.t('字段编码'),
    dataIndex: 'fieldCode',
    width: 100,
  },
  {
    title: i18n.t('数据来源'),
    dataIndex: 'status',
    width: 130,
  },
  {
    title: i18n.t('数据类型'),
    dataIndex: 'dataType',
    width: 156,
  },
  {
    title: i18n.t('是否启用'),
    dataIndex: 'status',
    width: 130,
  },
  {
    title: i18n.t('是否允许修改'),
    dataIndex: 'isUpdate',
    width: 130,
  },
  {
    title: i18n.t('是否可为空'),
    dataIndex: 'isNull',
    width: 130,
  },
  {
    title: i18n.t('是否主键'),
    dataIndex: 'isPrimaryKey',
    width: 130,
  },
  {
    title: i18n.t('是否系统字段'),
    dataIndex: 'isSystem',
    width: 130,
  },
  {
    title: i18n.t('是否显示'),
    dataIndex: 'isShow',
    width: 130,
  },
  {
    title: i18n.t('引用实体'),
    dataIndex: 'quoteEntityId',
    width: 130,
  },
  {
    title: i18n.t('操作'),
    dataIndex: 'handle',
    fixed: 'right',
    width: 92,
  },
])
