<template>
  <div class="project-plan-container">
    <Plan v-if="invalid" :process-info="props.processInfo" />
    <PartakeRole :process-info="processInfo" :other="props.other" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import Plan from './components/Plan/index.vue'
import PartakeRole from './components/PartakeRole/index.vue'
import { IProcess } from '@/types/handle'
import { IProcessRoleAndUser } from '@/types/request'

interface IProps {
  processInfo: IProcess[]
  other: { processRoleInfo: IProcessRoleAndUser[] }
}
const props = defineProps<IProps>()
const emits = defineEmits(['operate'])
const invalid = computed(() => props.processInfo[0]?.invalid)
</script>

<style lang="scss" scoped>
.project-plan-container {
  margin-top: 2px;
}
</style>
