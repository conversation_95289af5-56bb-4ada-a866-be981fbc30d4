import type { EChartsOption } from '@/components/BaseEchart/config'
import i18n from '@/i18n'

export const barOptions: EChartsOption = {
  color: ['#378EEF', '#2FCC83'],
  legend: {
    itemWidth: 8,
    itemHeight: 8,
    bottom: 0,
    icon: 'path://M0 0h1024v1024H0z',
    itemGap: 16,
    textStyle: {
      color: '#666666',
    },
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line',
      lineStyle: {
        color: '#666',
        type: 'solid',
      },
    },
    backgroundColor: '#000000B3',
    borderColor: 'transparent',
    formatter: function (params: any) {
      return `<div style="color: #fff">${params[0].name}</div>
      <div style="display: flex; justify-content: space-between; color: #fff"><span style="display: inline-block; margin-right: 24px"><i style="display: inline-block;width: 8px; height: 8px;margin-right:4px;background-color: #378EEF;"></i>${i18n.t(
        '进行中数量'
      )}</span><span>${params[0].value}${i18n.t('个')}</span></div>
      <div style="display: flex; justify-content: space-between; color: #fff"><span style="display: inline-block; margin-right: 24px"><i style="display: inline-block;width: 8px; height: 8px;margin-right:4px;background-color: #2FCC83;"></i>${i18n.t(
        '已完成数量'
      )}</span><span>${params[1].value}${i18n.t('个')}</span></div>`
    },
  },
  grid: {
    left: '38px',
    right: '0',
    top: '8px',
    bottom: '58px',
  },
  xAxis: {
    type: 'category',
    axisTick: {
      show: false,
    },
    axisLine: {
      lineStyle: {
        color: '#cccccc',
      },
    },
    axisLabel: {
      color: '#999999',
    },
    axisPointer: {
      show: true,
      type: 'line',
      lineStyle: {
        color: '#666666',
      },
    },
    data: [i18n.t('PBG项目管理流程'), i18n.t('1-N流程'), i18n.t('IPD流程')],
  },
  yAxis: {
    splitLine: {
      lineStyle: {
        color: '#EEEEEE',
      },
    },
    axisLabel: {
      color: '#999999',
      formatter: function (value: any) {
        return value + ''
      },
    },
  },
  series: [
    {
      name: i18n.t('进行中数量'),
      data: [],
      barWidth: 40,
      barGap: '40%',
      type: 'bar',
    },
    {
      name: i18n.t('已完成数量'),
      data: [],
      barWidth: 40,
      barGap: '40%',
      type: 'bar',
    },
  ],
}
