<template>
  <div :class="['describe-box', show ? 'open-describe-box' : '']" :style="{ maxHeight: boxMaxHeight || 106 + 'px' }">
    <div :class="['text-box', show ? 'open-text' : '']">
      <slot></slot>
    </div>
    <div :class="['btn-box', show ? 'open-btn-box' : '']" :style="{ top: btnTop || 90 + 'px' }">
      <span class="show-btn" @click="show = !show">
        {{ show ? i18n.t('收起') : i18n.t('更多') }}
        <i :class="['iconfont', show ? 'iconjiantoushang' : 'iconjiantouxia']"></i
      ></span>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from '@fs/i18n'

interface IProps {
  boxMaxHeight?: number | string
  btnTop?: number | string
}

const props = defineProps<IProps>()
const i18n = useI18n()
const show = ref<boolean>(false)
</script>
<style scoped lang="scss">
.describe-box {
  position: relative;
  width: 100%;
  overflow: hidden;
  background-color: inherit;
  .text-box {
    width: 100%;
  }
  .open-text {
    margin-bottom: 18px;
  }
  .instock-qty-box {
    display: flex;
    justify-content: space-between;
    .name {
      margin-right: 12px;
    }
  }
  .btn-box {
    position: absolute;
    width: 100%;
    height: 18px;
    line-height: 18px;
    background-color: inherit;
    .show-btn {
      display: flex;
      align-items: center;
      height: 18px;
      cursor: pointer;
      color: #378eef;
    }
    &.open-btn-box {
      top: calc(100% - 18px) !important;
    }
  }
  &.open-describe-box {
    max-height: none !important;
  }
}
</style>
