<template>
  <div class="expandable-tree">
    <ExpandableTreeItem
      v-for="item in items"
      :key="item.id"
      :item="item"
      :level="0"
      :icon-class-open="iconClassOpen"
      :icon-class-closed="iconClassClosed"
      :indent="indent"
      :expanded-ids="expandedIds"
      ref="treeRefs"
    >
      <template #label="slotProps">
        <slot name="label" v-bind="slotProps" />
      </template>
      <template #default="slotProps">
        <slot v-bind="slotProps" />
      </template>
    </ExpandableTreeItem>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, ref, defineExpose } from 'vue'
import ExpandableTreeItem from './ExpandableTreeItem.vue'

interface TreeItem {
  id: string | number
  label: string
  children?: TreeItem[]
  [key: string]: any
}

const props = defineProps<{
  items: TreeItem[]
  iconClassOpen?: string
  iconClassClosed?: string
  indent?: number
  expandedIds?: (string | number)[]
}>()

const treeRefs = ref<InstanceType<typeof ExpandableTreeItem>[]>([])

function expandAll() {
  treeRefs.value.forEach(ref => ref.expandAll?.())
}

function collapseAll() {
  treeRefs.value.forEach(ref => ref.collapseAll?.())
}

defineExpose({
  expandAll,
  collapseAll,
})
</script>
