<template>
  <Collapse style="margin-top: 12px" :header="currtInfo?.title" v-if="currtInfo?.flag">
    <template #extra>
      <FSpace :size="[8]" wrap @click.stop>
        <TipBtn
          v-if="!isReadOnly && selectedRowKeys.length"
          has-pop
          tip-title="批量删除"
          pop-title="确定删除选中的数据吗？"
          @onConfirmFn="batchDelete"
        >
          <FButton class="fs-btn">
            <template #icon><Icon class="marginR4" icon="iconshanchu2" :size="12" /></template>批量删除
          </FButton>
        </TipBtn>
        <FButton
          class="fs-btn"
          type="primary"
          @click="addRow"
          v-if="!isReadOnly && currtInfo?.addRowFlag && currtInfo?.addRowFlag()"
        >
          <template #icon><Icon class="marginR4" icon="icontubiao_tianjia1" :size="12" /></template>添加行
        </FButton>
      </FSpace>
    </template>
    <FForm ref="formRef" :model="tableData" layout="vertical" validateTrigger="none" name="tableForm">
      <FTable
        :columns="columns"
        :data-source="tableData"
        v-model:expandedRowKeys="expandedRowKeys"
        :row-key="(data:any) => data.id"
        :sticky="{ offsetHeader: -20 }"
        :scroll="{ x: 'min-content' }"
        :loading="loading"
        :row-selection="
          !isReadOnly
            ? {
                selectedRowKeys,
                onChange: onSelectChange,
                getCheckboxProps: getRowSelectionProps,
              }
            : undefined
        "
      >
        <template #headerCell="{ column }">
          <template v-if="column.className">
            <span :class="column.className">{{ column.title }}</span>
          </template>
        </template>
        <template #bodyCell="{ column, record, index }">
          <!-- 动态渲染表单组件 -->
          <template v-if="column.dataIndex && column.dataIndex !== 'operate' && column.component">
            <FFormItem
              :name="[index, column.dataIndex]"
              :rules="creatRules(column, record, index) || []"
              class="form-item-no-margin"
            >
              <component
                :is="column.component"
                v-model:value="record[column.dataIndex]"
                v-bind="column.componentAttrs || {}"
                :componentConfig="{
                  dataType: column.componentType || 'input',
                  isDetail:
                    !record.isEditing ||
                    column.isDetail ||
                    (currtInfo?.disabledDataKey ?? []).includes(column.dataIndex),
                  valueFormatFn: column?.valueFormatFn ?? undefined,
                  componentAttrs: column.componentAttrs || {},
                  currtRecord: record,
                }"
              />
            </FFormItem>
          </template>

          <!-- 操作列 -->
          <template v-if="column.dataIndex === 'operate'">
            <FSpace :size="[8]" wrap class="handle-row-box">
              <template v-if="!isReadOnly">
                <template v-if="record.isEditing">
                  <TipBtn key="save" tip-title="保存">
                    <i class="iconfont icontubiao_baocun1 hover-btn" @click="saveRow(record, index)"></i>
                  </TipBtn>
                  <TipBtn key="cancel" tip-title="取消">
                    <i class="iconfont icontubiao_quxiao hover-btn" @click="cancelEdit(record, index)"></i>
                  </TipBtn>
                </template>
                <template v-else>
                  <TipBtn
                    key="add-child"
                    tip-title="添加子级"
                    v-if="currtInfo?.addChildRowFlag && currtInfo?.addChildRowFlag(record)"
                  >
                    <i class="iconfont iconxinzeng hover-btn" @click="addChildRow(record)"></i>
                  </TipBtn>
                  <TipBtn key="edit" tip-title="编辑" v-if="isRowEditable(record)">
                    <i class="iconfont icontubiao_xietongbianji hover-btn" @click="editRow(record)"></i>
                  </TipBtn>
                  <TipBtn
                    v-if="isRowDeletable(record)"
                    has-pop
                    tip-title="删除"
                    pop-title="确定删除选中的数据吗？"
                    @onConfirmFn="removeRow(record)"
                  >
                    <i class="iconfont icontubiao_xietongshanchu hover-btn"></i>
                  </TipBtn>
                </template>
              </template>
            </FSpace>
          </template>
        </template>
      </FTable>
    </FForm>
  </Collapse>
</template>

<script setup lang="ts">
import { Ref, computed, inject, ref, watch, onMounted, reactive } from 'vue'
import { IProcess } from '@/types/handle'
import { message } from '@fs/smart-design'
import dayjs from 'dayjs'
import { useStore } from 'vuex'
import Icon from '@/components/Icon/index.vue'
import TipBtn from '@/views/message-template/components/TipBtn/index.vue'
import Collapse from '@/views/process-operate/components/Main/components/Milepost/components/Collapse/index.vue'
import CustomInput from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomInput/index.vue'
import CustomSelect from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomSelect/index.vue'
import CustomDatePicker from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomDatePicker/index.vue'
import CustomUpload from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomUpload/index.vue'
import ProcessDetail from '@/views/process-operate/components/CustomComponents/BusinessComponent/ProcessDetail/index.vue'
import CustomInputTextarea from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomInputTextarea/index.vue'
import { getIpdSubProcessTable, saveIIpdSubProcess, deleteIpdSubProcess, updateIpdSubProcess } from '@/api'
import {
  getValueFn,
  initializeData,
  cleanEmptyChildren,
  findNodeWithPath,
  isEmpty,
} from '@/views/process-operate/components/CustomComponents/BusinessComponent/utils'

interface IProps {
  isReadOnly?: any
}

const props = defineProps<IProps>()
const store = useStore()

// 注入的数据
const currtMilepost = inject<Ref<IProcess>>('currtMilepost') as Ref<IProcess> // 当前里程碑信息
const processId = inject<number>('processId') // 流程 id
const processNo = inject<Ref<string>>('processNo') // 流程 code
const initNewIpdRelevanceProcessInfo = inject('initNewIpdRelevanceProcessInfo') as () => void

// 本地状态
const loading = ref(false)
const tableData = ref<any[]>([])
const formRef = ref<any>() // 表单引用
const selectedRowKeys = ref<string[]>([]) // 选中的行键
const expandedRowKeys = ref<string[]>([]) // 展开的行键

const typeOptions = [
  { label: '研发类型', value: '研发类型' },
  { label: '包装类型', value: '包装类型' },
  { label: '结构类型', value: '结构类型' },
  { label: '外观类型', value: '外观类型' },
  { label: '认证类型', value: '认证类型' },
  { label: '知识产权类型', value: '知识产权类型' },
  { label: '质量类型', value: '质量类型' },
  { label: '工艺类型', value: '工艺类型' },
  { label: '计划类型', value: '计划类型' },
  { label: '运营类型', value: '运营类型' },
  { label: '营销类型', value: '营销类型' },
  { label: '财务类型', value: '财务类型' },
  { label: '产品培训', value: '产品培训' },
  { label: '资料归档', value: '资料归档' },
  { label: '产品BOM类型', value: '产品BOM类型' },
  { label: '上线资料输出', value: '上线资料输出' },
  { label: '小批量试产', value: '小批量试产' },
  { label: '量产与备库执行', value: '量产与备库执行' },
  { label: '产品发布', value: '产品发布' },
  { label: '供应链类型', value: '供应链类型' },
  { label: 'SDV', value: 'SDV' },
  { label: 'SIT', value: 'SIT' },
  { label: 'SVT', value: 'SVT' },
  { label: '实验局测试', value: '实验局测试' },
]

// 页面配置信息
const info = reactive<Record<string, any>>({
  Task_deeffb1: {
    title: '新增IR关联流程',
    type: 1,
    flag: true,
    addRowFlag: () => [1, 2].includes(currtMilepost?.value?.status),
    editRowFlag: (record: any) => !record?.instanceId && record?.level === 0,
    deleteRowFlag: (record: any) => !record?.instanceId && record?.level === 0,
    selectRowFlag: (record: any) => !!record?.instanceId || record?.level !== 0,
    baseParams: {
      preId: 0,
      processType: 1,
      ipdInstanceId: processId,
      ipdInstanceCode: processNo.value,
    },
    submitKeys: ['title', 'type', 'ownerUuid', 'endDate', 'irProdModel', 'irVers', 'isUrgent', 'file', 'describemsg'],
    optionsList: {
      typeOptions: typeOptions,
    },
  },
  Task_19754b4: {
    title: '新增SR关联流程',
    type: 1,
    flag: true,
    addChildRowFlag: (record: any) =>
      [1, 2].includes(currtMilepost?.value?.status) &&
      record.instanceId &&
      record.level === currtInfo?.value?.levelDataIndex &&
      (record?.level || (record?.level === 0 && record?.type === '研发类型')),
    editRowFlag: (record: any) => !record?.instanceId && record?.level === 1,
    deleteRowFlag: (record: any) => !record?.instanceId && record?.level === 1,
    selectRowFlag: (record: any) => !!record?.instanceId || record?.level !== 1,
    levelDataIndex: 0,
    baseParams: {
      processType: 2,
      ipdInstanceId: processId,
      ipdInstanceCode: processNo.value,
    },
    submitKeys: ['title', 'type', 'ownerUuid', 'endDate', 'isUrgent', 'file', 'describemsg', 'preId'],
    disabledDataKey: ['type', 'irProdModel', 'irVers'],
    optionsList: {
      typeOptions: typeOptions,
    },
    defaultValueData: {
      1: {
        type: '研发类型',
      },
    },
  },
  Task_6a89e8e: {
    title: '新增AR关联流程',
    type: 1,
    flag: true,
    addChildRowFlag: (record: any) =>
      [1, 2].includes(currtMilepost?.value?.status) &&
      record.instanceId &&
      record.level === currtInfo?.value?.levelDataIndex,
    editRowFlag: (record: any) => !record?.instanceId && record?.level === 2,
    deleteRowFlag: (record: any) => !record?.instanceId && record?.level === 2,
    selectRowFlag: (record: any) => !!record?.instanceId || record?.level !== 2,
    levelDataIndex: 1,
    baseParams: {
      processType: 3,
      ipdInstanceId: processId,
      ipdInstanceCode: processNo.value,
    },
    submitKeys: ['title', 'type', 'ownerUuid', 'endDate', 'isUrgent', 'file', 'describemsg', 'preId'],
    disabledDataKey: ['type', 'irProdModel', 'irVers'],
    optionsList: {
      typeOptions: typeOptions,
    },
    defaultValueData: {
      2: {
        type: '研发类型',
      },
    },
  },
  Task_97b89cf: {
    title: '新增SDV测试关联流程',
    type: 2,
    flag: true,
    addRowFlag: () => [1, 2].includes(currtMilepost?.value?.status),
    editRowFlag: (record: any) => !record?.instanceId && record?.level === 0,
    deleteRowFlag: (record: any) => !record?.instanceId && record?.level === 0,
    selectRowFlag: (record: any) => !!record?.instanceId || record?.level !== 0,
    baseParams: {
      preId: 0,
      processType: 4,
      ipdInstanceId: processId,
      ipdInstanceCode: processNo.value,
    },
    submitKeys: ['title', 'type', 'ownerUuid', 'endDate', 'isUrgent', 'file', 'describemsg'],
    disabledDataKey: ['type', 'irProdModel', 'irVers'],
    optionsList: {
      typeOptions: [{ label: 'SDV测试', value: 'SDV测试' }],
    },
    defaultValueData: {
      0: {
        type: 'SDV测试',
      },
    },
    filterTableDataFn: (data: any[]) => {
      return data.filter(item => item.type === 'SDV测试')
    },
  },
  Task_bac986f: {
    title: '新增SIT测试关联流程',
    type: 2,
    flag: true,
    addRowFlag: () => [1, 2].includes(currtMilepost?.value?.status),
    editRowFlag: (record: any) => !record?.instanceId && record?.level === 0,
    deleteRowFlag: (record: any) => !record?.instanceId && record?.level === 0,
    selectRowFlag: (record: any) => !!record?.instanceId || record?.level !== 0,
    baseParams: {
      preId: 0,
      processType: 4,
      ipdInstanceId: processId,
      ipdInstanceCode: processNo.value,
    },
    submitKeys: ['title', 'type', 'ownerUuid', 'endDate', 'isUrgent', 'file', 'describemsg'],
    disabledDataKey: ['type', 'irProdModel', 'irVers'],
    optionsList: {
      typeOptions: [{ label: 'SIT测试', value: 'SIT测试' }],
    },
    defaultValueData: {
      0: {
        type: 'SIT测试',
      },
    },
    filterTableDataFn: (data: any[]) => {
      return data.filter(item => item.type === 'SIT测试')
    },
  },
  Task_e05d698: {
    title: '新增SVT测试关联流程',
    type: 2,
    flag: true,
    addRowFlag: () => [1, 2].includes(currtMilepost?.value?.status),
    editRowFlag: (record: any) => !record?.instanceId && record?.level === 0,
    deleteRowFlag: (record: any) => !record?.instanceId && record?.level === 0,
    selectRowFlag: (record: any) => !!record?.instanceId || record?.level !== 0,
    baseParams: {
      preId: 0,
      processType: 4,
      ipdInstanceId: processId,
      ipdInstanceCode: processNo.value,
    },
    submitKeys: ['title', 'type', 'ownerUuid', 'endDate', 'isUrgent', 'file', 'describemsg'],
    disabledDataKey: ['irProdModel', 'irVers'],
    optionsList: {
      typeOptions: [
        { label: 'SVT测试', value: 'SVT测试' },
        { label: '实验局测试', value: '实验局测试' },
      ],
    },
    filterTableDataFn: (data: any[]) => {
      return data.filter(item => ['SVT测试', '实验局测试'].includes(item.type))
    },
  },
  default: {
    flag: false,
  },
})
const currtInfo = computed(
  () =>
    ([1, 2, 3, 4, 5].includes(currtMilepost?.value?.status) && info?.[currtMilepost?.value?.nodeId as string]) ||
    info.default
)

// 选项列表
const optionsList = reactive<Record<string, any>>({
  userOptions: store.state.user.allUser || [],
  urgentOptions: [
    {
      label: '一般',
      value: 0,
    },
    {
      label: '紧急',
      value: 2,
    },
    {
      label: '非常紧急',
      value: 1,
    },
  ],
})

// 表格列定义
const columns = computed(() => [
  {
    title: '事项名称',
    dataIndex: 'title',
    width: 230,
    className: 'cust-row-required',
    component: CustomInput,
    componentType: 'input',
    componentAttrs: {
      placeholder: '请输入任务名称',
    },
    rules: [{ required: true, message: '请输入任务名称' }],
    fixed: 'left',
  },
  {
    title: '流程编号',
    dataIndex: 'milepost',
    width: 180,
    component: ProcessDetail,
    componentType: 'customProcessDetail',
    componentAttrs: {
      showValueKey: 'instanceCode',
    },
  },
  {
    title: '需求类型',
    dataIndex: 'type',
    width: 180,
    className: 'cust-row-required',
    component: CustomSelect,
    componentType: 'select',
    componentAttrs: {
      options: currtInfo.value?.optionsList?.typeOptions || [],
      allowClear: true,
      showSearch: true,
      optionFilterProp: 'label',
      placeholder: '请选择需求类型',
    },
    rules: [{ required: true, message: '请选择需求类型' }],
  },
  {
    title: '负责人',
    dataIndex: 'ownerUuid',
    width: 180,
    className: 'cust-row-required',
    component: CustomSelect,
    componentType: 'select',
    submitValueFormatFn: (config: any, record: any) => {
      const valueData = getValueFn(config?.componentAttrs?.options ?? [], record?.ownerUuid, 'uuid')
      return {
        ownerName: valueData?.feiShuName,
        ownerUuid: valueData?.uuid,
      }
    },
    componentAttrs: {
      options: optionsList.userOptions,
      fieldNames: { label: 'feiShuName', value: 'uuid' },
      allowClear: true,
      showSearch: true,
      optionFilterProp: 'feiShuName',
      placeholder: '请选择负责人',
    },
    rules: [{ required: true, message: '请选择负责人' }],
  },
  {
    title: '期望完成时间',
    dataIndex: 'endDate',
    width: 180,
    className: 'cust-row-required',
    component: CustomDatePicker,
    componentType: 'datePicker',
    submitValueFormatFn: (config: any, record: any) => {
      return {
        endDate: (record?.endDate && dayjs(record?.endDate).format('YYYY-MM-DD') + ' 00:00:00') || undefined,
      }
    },
    valueFormatFn: (value: any) => {
      return value ? dayjs(value).format('YYYY-MM-DD') : '--'
    },
    componentAttrs: {
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      style: { width: '100%' },
      placeholder: '请选择期望完成时间',
    },
    rules: [{ required: true, message: '请选择期望完成时间' }],
  },
  // {
  //   title: '关联流程编号',
  //   dataIndex: 'syn_parent_number',
  //   width: 180,
  //   component: CustomInput,
  //   componentType: 'input',
  //   componentAttrs: {},
  //   isDetail: true,
  // },
  {
    title: '紧急程度',
    dataIndex: 'isUrgent',
    width: 180,
    className: 'cust-row-required',
    component: CustomSelect,
    componentType: 'select',
    componentAttrs: {
      options: optionsList.urgentOptions,
      allowClear: true,
      showSearch: true,
      optionFilterProp: 'label',
      placeholder: '请选择紧急程度',
    },
    rules: [{ required: true, message: '请选择紧急程度' }],
  },
  {
    title: '需求描述',
    dataIndex: 'describemsg',
    width: 230,
    component: CustomInputTextarea,
    componentType: 'textarea',
    componentAttrs: {
      placeholder: '请输入需求描述',
      rows: 4,
    },
    // rules: [{ required: true, message: '请输入需求描述' }],
  },
  {
    title: '产品型号',
    dataIndex: 'irProdModel',
    width: 230,
    component: CustomInput,
    componentType: 'input',
    componentAttrs: {
      placeholder: '请输入产品型号',
    },
    // rules: [{ required: true, message: '请输入产品型号' }],
  },
  {
    title: '版本号',
    dataIndex: 'irVers',
    width: 230,
    component: CustomInput,
    componentType: 'input',
    componentAttrs: {
      placeholder: '请输入版本号',
    },
    // rules: [{ required: true, message: '请输入版本号' }],
  },
  {
    title: '附件',
    dataIndex: 'file',
    width: 230,
    component: CustomUpload,
    componentType: 'upload',
    componentAttrs: {},
    submitValueFormatFn: (config: any, record: any) => {
      return {
        file:
          (record?.file && record?.file.map((item: any) => ({ name: item.name, size: item.size, url: item.url }))) ||
          undefined,
      }
    },
    rules: [{ required: true, message: '请上传附件' }],
  },
  { title: '操作', dataIndex: 'operate', width: 120, fixed: 'right' },
])

// 行选择变更处理
const onSelectChange = (keys: string[]) => {
  selectedRowKeys.value = keys
}

// 获取行选择属性
const getRowSelectionProps = (record: any) => ({
  disabled: currtInfo.value?.selectRowFlag && currtInfo.value?.selectRowFlag(record),
})

// 判断行是否可编辑
const isRowEditable = (record: any) => {
  return currtInfo.value?.editRowFlag && currtInfo.value?.editRowFlag(record)
}

// 判断行是否可删除
const isRowDeletable = (record: any) => {
  return currtInfo.value?.deleteRowFlag && currtInfo.value?.deleteRowFlag(record)
}

const clearValidate = (record: any, index: number) => {
  if (formRef.value) {
    try {
      const keys = columns.value.map(item => [index, item.dataIndex])
      formRef.value.clearValidate(keys)
    } catch (error) {
      console.error('清空校验信息失败:', error)
    }
  }
}

const validateFieldsFn = async (record: any, index: number) => {
  if (formRef.value) {
    const keys = columns.value.map(item => [index, item.dataIndex])
    return await formRef.value.validateFields(keys)
  }
}

const validatorKeys = {
  file: (rule: any, value: any, callback: any, column: any, record: any, index: number) => {
    if (record?.isEditing && Array.isArray(record?.[column.dataIndex]) && record?.[column.dataIndex].length) {
      if (record?.[column.dataIndex].some((item: any) => item?.status && item.status !== 'success')) {
        callback(new Error('请等待附件上传完成'))
      } else if (record?.[column.dataIndex].some((item: any) => item.status && item.status === 'error')) {
        callback(new Error('请检查附件上传是否失败'))
      } else {
        callback()
      }
    } else {
      callback()
    }
  },
}

const creatRules = (column: any, record: any, index: number) => {
  return (column?.rules ?? [])?.map((columnRule: any) => {
    return Object.assign(
      // { trigger: 'change' },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (validatorKeys?.[column.dataIndex]) {
            validatorKeys[column.dataIndex](rule, value, callback, column, record, index)
          } else {
            if (columnRule?.required && record.isEditing && isEmpty(record?.[column.dataIndex])) {
              callback(new Error(columnRule?.message || '请先编辑'))
            } else {
              callback()
            }
          }
        },
      }
    )
  })
}

// 生成唯一ID
const generateId = () => {
  return 'id_' + Date.now() + '_' + Math.floor(Math.random() * 1000)
}

// 添加新行
const addRow = () => {
  const newRow: Record<string, any> = {
    id: generateId(),
    isNew: true,
    isEditing: true,
    level: 0,
    // syn_parent_number: processNo.value,
    ...(currtInfo.value?.defaultValueData?.[0] ?? {}),
  }
  tableData.value.unshift(newRow)
}

// 添加子行
const addChildRow = (parentRecord: any) => {
  const childRow: Record<string, any> = {
    id: generateId(),
    _parentId: parentRecord.id,
    preId: parentRecord.id,
    isNew: true,
    isEditing: true,
    level: parentRecord.level + 1,
    // type: '研发类型',
    ...(currtInfo.value?.defaultValueData?.[parentRecord.level + 1] ?? {}),
    // syn_parent_number: parentRecord?.instanceCode,
  }
  parentRecord.children = parentRecord.children || []
  parentRecord.children.unshift(childRow)
  !expandedRowKeys.value.includes(parentRecord.id) && expandedRowKeys.value.push(parentRecord.id)
}

// 编辑行
const editRow = (record: any) => {
  record._originalData = JSON.parse(JSON.stringify(record))
  record.isEditing = true
}

// 取消编辑
const cancelEdit = (record: any, index: number) => {
  let currentIndex = index
  let currentData = tableData.value
  if (record?._parentId) {
    currentData = findNodeWithPath(tableData.value, record._parentId)?.children
    currentIndex = currentData.findIndex((item: any) => item.id === record.id)
  }
  if (record.isNew) {
    currentData.splice(currentIndex, 1)
  }

  // 恢复原始数据
  if (record._originalData) {
    Object.keys(record._originalData).forEach(key => {
      if (key !== '_originalData' && key !== 'isEditing') {
        record[key] = record._originalData[key]
      }
    })
    delete record._originalData
  }

  // 退出编辑状态
  record.isEditing = false
  clearValidate(record, index)
  tableData.value = cleanEmptyChildren(tableData.value)
}

// 批量删除
const batchDelete = async () => {
  if (!selectedRowKeys.value.length) return
  await deleteIpdSubProcess({ ids: selectedRowKeys.value })
  message.success('删除成功')
  fetchTableData()
  initNewIpdRelevanceProcessInfo()
}

// 保存行
const saveRow = async (record: any, index: number) => {
  try {
    await validateFieldsFn(record, index)

    const submitData = currtInfo.value.submitKeys.reduce((acc, key) => {
      const column = columns.value.find(item => item.dataIndex === key)
      if (column?.submitValueFormatFn) {
        console.log('column?.submitValueFormatFn(column, record) :>> ', column?.submitValueFormatFn(column, record))
        acc = { ...acc, ...column?.submitValueFormatFn(column, record) }
      } else {
        acc[key] = record[key]
      }
      return acc
    }, {})
    const params = {
      ...currtInfo.value.baseParams,
      ...submitData,
    }
    if (record?.isNew) {
      await saveIIpdSubProcess(params)
    } else {
      await updateIpdSubProcess({ ...params, id: record.id })
    }
    fetchTableData()
    initNewIpdRelevanceProcessInfo()

    message.success('保存成功')
  } catch (error) {
    console.error('保存失败:', error)
    error?.errorFields?.[0]?.errors?.[0] && message.warning(error.errorFields[0].errors[0] ?? '请先编辑')
  }
}

// 删除行
const removeRow = async (record: any) => {
  if (!record?.id) return
  await deleteIpdSubProcess({ ids: [record?.id] })
  message.success('删除成功')
  fetchTableData()
  initNewIpdRelevanceProcessInfo()
}

// 获取表格数据
const fetchTableData = async () => {
  tableData.value = []
  if (isEmpty(processId) || !currtInfo.value.flag) return
  loading.value = true
  try {
    const res = await getIpdSubProcessTable(processId, currtInfo.value.type)
    if (isEmpty(res) || isEmpty(res.data)) {
      console.log('API返回的数据为空')
      return
    }
    let initializedData = initializeData(res.data)
    initializedData = cleanEmptyChildren(initializedData)
    if (currtInfo.value.filterTableDataFn) {
      tableData.value = currtInfo.value.filterTableDataFn(initializedData)
    } else {
      tableData.value = initializedData
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    message.error('获取数据失败')
  } finally {
    loading.value = false
    selectedRowKeys.value = []
  }
}

// 监听当前里程碑变化，重新获取数据
watch(
  () => currtInfo.value,
  () => {
    fetchTableData()
  },
  { immediate: false, deep: true }
)

onMounted(() => {
  fetchTableData()
})
</script>

<style scoped lang="scss">
:deep(.fs-table-tbody .fs-table-cell) {
  padding: 20px 16px;
}
:deep(.fs-table-row-expand-icon) {
  margin-top: 8px;
}
:deep(.fs-form-item-control-input-content) {
  line-height: 32px;
  height: auto !important;
  min-height: 32px;
}
.form-item-no-margin {
  margin-bottom: 0 !important;
  :deep(.fs-form-item-explain-connected) {
    position: absolute;
    bottom: -26px;
  }
}
.fs-btn {
  width: 82px;
  height: 24px;
  font-size: 12px !important;
}
.hover-btn {
  color: #378eef;
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
  &:hover {
    background-color: #d8d8d8;
  }
}
.cust-row-required {
  &::before {
    display: inline-block;
    margin-right: 4px;
    color: #ff4d4f;
    font-size: 14px;
    content: '*';
  }
}
</style>
