<template>
  <div class="table-box-container">
    <div class="header">
      <span class="title">{{ i18n.t('产品停产下架列表') }}</span>
      <FButton class="export-btn" type="primary" @click="emits('onExportProcess')">
        <i class="icontubiao_xiazai iconfont"></i>{{ i18n.t('下载') }}
      </FButton>
    </div>
    <FTable
      :data-source="list"
      :loading="loading"
      :columns="columns"
      table-layout="fixed"
      :pagination="false"
      :scroll="{ x: '100%' }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'topicName'">
          <p style="margin: 0">{{ record.topicName || '--' }}</p>
          <p style="margin: 0; cursor: pointer; color: #378eef" @click="onJumpDemandDetial(record)">
            {{ record.processInstanceCode || '--' }}
          </p>
        </template>
        <template v-if="column.dataIndex === 'productStatus'">
          <span
            :class="[
              'product-status',
              record.productStatus === '下架' && 'off',
              record.productStatus === '特殊上架' && 'special',
            ]"
            >{{ record.productStatus }}</span
          >
        </template>
        <template v-if="column.dataIndex === 'reason'">
          <MoreTextTips v-if="record.reason">
            {{ record.reason }}
          </MoreTextTips>
        </template>
        <template v-if="column.dataIndex === 'updateOrNot'">
          <MoreTextTips v-if="record.updateOrNot" :lineClamp="1">
            {{ record.updateOrNot }}
          </MoreTextTips>
        </template>
      </template>
    </FTable>
  </div>
</template>

<script setup lang="ts">
import { ProductItem } from '@/types/productRemoveList'
import MoreTextTips from '@/components/MoreTextTips/index'
import { columns } from './tableConfig'
import { useI18n, jumpToDemand } from '@/utils'
const i18n = useI18n()

type propsType = {
  list: ProductItem[]
  loading: boolean
}
const props = withDefaults(defineProps<propsType>(), {
  list: () => [],
  loading: false,
})

const emits = defineEmits(['onExportProcess'])

const onJumpDemandDetial = (record: any) => {
  jumpToDemand(record.instanceId, record.processConfigId)
}
</script>

<style scoped lang="scss">
.table-box-container {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    .title {
      font-size: 14px;
      font-weight: 500;
      color: #333333;
    }
  }
  .text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  :deep(.fs-table-cell) {
    &:empty {
      &::after {
        content: '--';
      }
    }
  }
  :deep(.ant-table-tbody) {
    > tr:hover:not(.ant-table-expanded-row) > td,
    .ant-table-row-hover,
    .ant-table-row-hover > td {
      background: #f1f4f8 !important;
    }
    tr > td {
      background: #fff !important;
    }
    .table-striped > td {
      background: #fbfdff !important;
    }
  }
  :deep(.ant-table-container) {
    &::after {
      box-shadow: none;
    }
  }
  .fs-pagination {
    padding-top: 11px;
  }
  :deep(.popover-select-notes) {
    .ant-popover-inner-content {
      padding: 12px;
    }
  }
  .product-status {
    display: inline-block;
    padding: 0 3px;
    background: #eafaf2;
    border-radius: 2px;
    border: 1px solid #2fcc83;
    color: #2fcc83;
    line-height: 18px;
    &.off {
      background: #eeeeee;
      border: 1px solid #dddddd;
      color: #999999;
    }
    &.special {
      background: #fef4e9;
      border: 1px solid #fa8f23;
      color: #fa8f23;
    }
  }
  :deep(.color333) {
    color: #333;
  }
  :deep(.colorFA8F23) {
    color: #fa8f23;
  }
  :deep(.color2FCC83) {
    color: #2fcc83;
  }
  :deep(.color378EEF) {
    color: #378eef;
  }
  :deep(.colorF04141) {
    color: #f04141;
  }
}
</style>
