import { getAllUsers } from '@/api/handle'
import { IUser } from '@/types/handle'
import { Commit } from 'vuex'

export type StoreUserType = IUser & { name: string }
interface IUserStore {
  allUser: StoreUserType[]
  stateToken: string | undefined
}

const cache = new Map<string, unknown>()

const state: IUserStore = {
  allUser: [],
  stateToken: undefined,
}
const mutations = {
  SET_USERS: (state: IUserStore, allUser: StoreUserType[]) => {
    state.allUser = allUser
  },
  SET_STATETOKEN: (state: IUserStore, stateToken: string | undefined) => {
    state.stateToken = stateToken
  },
}

const getters = {
  getStateToken: (state: IUserStore) => state.stateToken,
}

const actions = {
  async getUsers({ commit }: { commit: Commit }) {
    if (cache.has('allUser')) return Promise.resolve(cache.get('allUser'))
    const { data = [] } = await getAllUsers()
    const userList = data.map(item => ({
      ...item,
      // name: `${item.nameCh}(${item.nameEn})`,
      name: item.feiShuName || `${item.nameCh}(${item.nameEn})`,
    }))
    cache.set('allUser', userList)
    commit('SET_USERS', userList)
    return userList
  },
  async setStateToken({ commit }: { commit: Commit }, stateToken: string) {
    if (stateToken) commit('SET_STATETOKEN', stateToken)
  },
}
export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
}
