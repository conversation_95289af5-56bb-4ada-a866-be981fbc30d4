<template>
  <div class="relecance-process-container">
    <FSpin tip="Loading..." :spinning="loading">
      <FCard :head-style="COMPONENT_CRAD_BODY_STYLE" :body-style="COMPONENT_CRAD_BODY_STYLE">
        <!-- <template #title>关联流程</template> -->
        <div class="mb20">
          <div class="fr m20 mr0">
            <FButton class="mr12" @click="operate(EmitType.createProcess, currtMilepost)">{{
              i18n.t('创建关联流程')
            }}</FButton>
            <FButton type="primary" @click="relateProcessFlag = true">{{ i18n.t('关联流程') }}</FButton>
          </div>
          <FTable :columns="columns" :data-source="tableData" :scroll="{ x: 1300 }">
            <template #bodyCell="{ column, record: process }">
              <template v-if="column.dataIndex === 'projectName'">
                <a class="ellipsis" :title="process.projectName" @click="handleProcessClick(process)">
                  {{ process.projectName }}
                </a>
              </template>
              <template v-if="column.dataIndex === 'statusName'">
                <span style="color: #3dcca6">{{ process.statusName }}</span>
              </template>
              <template v-if="column.dataIndex === 'currentStage'">
                <ProcessDetail
                  :value="process"
                  :component-config="{
                    currtRecord: process,
                    componentAttrs: { showValueKey: 'currentStage', hasTooltip: true },
                  }"
                />
              </template>
              <template v-if="column.dataIndex === 'operate'">
                <FPopconfirm :title="i18n.t('是否确认解除关联？')" @confirm="handleDeleteProcessRelateClick(process)">
                  <a class="pointer">{{ i18n.t('解除关联') }}</a>
                </FPopconfirm>
              </template>
            </template>
          </FTable>
        </div>

        <FModal
          :title="i18n.t('关联流程')"
          :confirm-loading="relateProcessLoading"
          @ok="handleRelateProcessSubmit"
          @cancel="handleRelateProcessCancel"
          v-model:visible="relateProcessFlag"
        >
          <div class="mb20">
            <FSelect
              v-model:value="relateQueryFormData.processConfigId"
              :press-line="i18n.t('流程类型')"
              :options="processTypes"
              style="width: 200px"
              allow-clear
              show-search
              option-filter-prop="label"
              :placeholder="i18n.t('请选择')"
              @change="queryProcessList"
            />
          </div>
          <FSelect
            mode="multiple"
            v-model:value="relateProcessFormData.processIds"
            style="width: 100%"
            :options="processList"
            show-search
            option-filter-prop="label"
            allow-clear
            :placeholder="i18n.t('请选择要关联的流程')"
          />
        </FModal>
      </FCard>
    </FSpin>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, onMounted, reactive, ref, Ref } from 'vue'
import { useRouter } from 'vue-router'
import { messageInstance as message } from '@fs/smart-design'
import { COMPONENT_CRAD_BODY_STYLE, RELATE_PROCESS_STATUS_NAME } from '../../config'
import {
  addProcessRelation,
  deleteProcessRelation,
  getClassList,
  getPermitInstanceList,
  getProcessRelateList,
  getAllClassList,
} from '@/api'
import { jumpToDemand, transformDate, useI18n } from '@/utils'
import type { IRelationProcess } from '@/types/request'
import type { IProcess } from '@/types/handle'
import { EmitType } from '@/views/process-detail/config'
import ProcessDetail from '@/views/process-operate/components/CustomComponents/BusinessComponent/ProcessDetail/index.vue'

interface IProps {
  processInfo: IProcess[]
  other: unknown
}

const i18n = useI18n()
const columns = computed(() => [
  { title: i18n.t('项目名称'), dataIndex: 'projectName', width: '180px' },
  { title: i18n.t('流程类型'), dataIndex: 'processType', width: '120px' },
  { title: i18n.t('项目编号'), dataIndex: 'projectNum', width: '160px' },
  { title: i18n.t('当前阶段'), dataIndex: 'currentStage', width: '210px' },
  { title: i18n.t('流程状态'), dataIndex: 'statusName', width: '80px' },
  { title: i18n.t('发起人'), dataIndex: 'sponsor', width: '180px' },
  { title: i18n.t('当前节点负责人'), dataIndex: 'nodeOwner', width: '180px' },
  { title: i18n.t('预计完成时间'), dataIndex: 'targetDate', width: '120px' },
  { title: i18n.t('实际完成时间'), dataIndex: 'actualDate', width: '120px' },
  { title: i18n.t('操作'), dataIndex: 'operate', fixed: 'right', width: '80px' },
])

const router = useRouter()
const porps = defineProps<IProps>()
const loading = ref(true)
const instanceId = computed(() => porps.processInfo[0]?.instanceId)
const instanceCode = computed(() => porps.processInfo[0]?.processInstanceCode)
const tableData = ref<IRelationProcess[]>([])
const processList = ref<{ label: string; value: number }[]>([])
const processTypes = ref<{ label: string; value: number }[]>([])
const relateQueryFormData = reactive<{ processConfigId: number | null }>({ processConfigId: null })
const relateProcessFormData = reactive<{ processIds: number[] }>({ processIds: [] })
const relateProcessFlag = ref(false)
const relateProcessLoading = ref(false)

const setRlevanceNumber = inject('setRlevanceNumber') as (n: number) => void
const operate = inject('operate') as (key: EmitType, data: IProcess) => void
const currtMilepost = inject('currtMilepost') as Ref<IProcess> // 当前里程碑信息

onMounted(() => {
  queryData()
  requestIdleCallback(queryProcessTypes)
  requestIdleCallback(queryProcessList)
})

const handleProcessClick = (process: IRelationProcess) => {
  jumpToDemand(process.instanceId, process.processConfigId, true)
}

const handleDeleteProcessRelateClick = async (process: IRelationProcess) => {
  await deleteProcessRelation(process.id)
  await queryData()
  message.success(i18n.t('删除成功'))
}

const handleRelateProcessSubmit = async () => {
  relateProcessLoading.value = true
  const ids = relateProcessFormData.processIds
  try {
    await addProcessRelation(instanceId.value, instanceCode.value, ids)
    await queryData()
    message.success(i18n.t('添加成功'))
  } finally {
    relateProcessLoading.value = false
    handleRelateProcessCancel()
  }
}

const handleRelateProcessCancel = () => {
  relateProcessFlag.value = false
  relateProcessFormData.processIds = tableData.value.map(item => item.instanceId)
}

const queryData = async () => {
  !loading.value && (loading.value = true)
  try {
    const { data = [] } = await getProcessRelateList(instanceId.value)
    tableData.value = (data || []).map(item => ({
      ...item,
      statusName: RELATE_PROCESS_STATUS_NAME[item.processStatus],
      targetDate: transformDate(item.targetDate, 'YYYY-MM-DD'),
      actualDate: transformDate(item.actualDate, 'YYYY-MM-DD'),
    }))
    relateProcessFormData.processIds = tableData.value.map(item => item.instanceId)
    setRlevanceNumber(tableData.value.length)
  } finally {
    loading.value = false
  }
}
const queryProcessTypes = async () => {
  const { data = [] } = await getAllClassList()
  processTypes.value = (data ?? []).map(item => ({
    label: item.processName,
    value: item.id,
  }))
}

const queryProcessList = async () => {
  const { data = [] } = await getPermitInstanceList(relateQueryFormData.processConfigId)
  processList.value = (data ?? [])
    .filter(item => item.id !== instanceId.value)
    .map(item => ({
      label: `${item.processInstanceCode} - ${item.topicName}`,
      value: item.id,
    }))
}

const filterOption = (inputValue: string, option: { label: string }) => {
  return option.label.toLowerCase().includes(inputValue.toLowerCase())
}
</script>

<style lang="scss" scoped>
.relecance-process-container {
  margin-top: 2px;
}
.fr {
  float: right;
}

.m20 {
  margin: 20px;
}
.mb20 {
  margin-bottom: 20px;
}

.mr0 {
  margin-right: 0;
}
.mr12 {
  margin-right: 12px;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
