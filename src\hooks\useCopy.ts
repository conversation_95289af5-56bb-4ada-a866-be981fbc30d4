import Clipboard from 'clipboard'
import { messageInstance as message } from '@fs/smart-design'
import i18n from '@/i18n'

export default function useCopy(selector: string) {
  const clipboard = new Clipboard(selector)
  clipboard.on('success', e => {
    message.success(i18n.t('复制成功'))
    clipboard.destroy()
  })
  clipboard.on('error', e => {
    message.warning(i18n.t('复制失败,稍后再试'))
    clipboard.destroy()
  })
}
