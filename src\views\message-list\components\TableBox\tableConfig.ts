import type { TableColumnsType } from 'ant-design-vue'
import { VNode, h, computed } from 'vue'
import { transformDate } from '@/utils'
import i18n from '@/i18n'

export const columns = computed<TableColumnsType>(() => [
  {
    title: i18n.t('流程类型'),
    dataIndex: 'processType',
    width: 132,
  },
  {
    title: i18n.t('流程名称'),
    dataIndex: 'topicName',
    width: 132,
  },
  {
    title: i18n.t('流程编码'),
    dataIndex: 'processInstanceCode',
    width: 132,
  },
  {
    title: i18n.t('当前节点'),
    dataIndex: 'nodeName',
    width: 132,
  },
  {
    title: i18n.t('消息内容'),
    dataIndex: 'content',
    width: 210,
  },
  {
    title: i18n.t('发送人'),
    dataIndex: 'sendBy',
    width: 132,
  },
  {
    title: i18n.t('接收人'),
    dataIndex: 'receiver',
    width: 132,
    customRender: ({ text, value, record }): VNode => {
      return h(
        'div',
        (text || '').split(',').map((item: string) => {
          return h('p', item)
        })
      )
    },
  },
  {
    title: i18n.t('是否已读'),
    dataIndex: 'isRead',
    width: 132,
    customRender: ({ text, value, record }): VNode => {
      return h('span', text === '0' ? i18n.t('是') : text === '1' ? i18n.t('否') : '--')
    },
  },
  {
    title: i18n.t('是否为最新消息'),
    dataIndex: 'isNew',
    width: 132,
    customRender: ({ text, value, record }): VNode => {
      return h('span', text === '0' ? i18n.t('否') : text === '1' ? i18n.t('是') : '--')
    },
  },
  {
    title: i18n.t('发送时间'),
    dataIndex: 'sendDate',
    width: 169,
    customRender: ({ text, value, record }): VNode => {
      return h('span', transformDate(text, 'YYYY-MM-DD HH:mm:ss'))
    },
  },
  {
    title: i18n.t('操作'),
    dataIndex: 'handle',
    width: 80,
  },
])
