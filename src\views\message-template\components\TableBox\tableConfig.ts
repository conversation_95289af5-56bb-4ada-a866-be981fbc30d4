import type { TableColumnsType } from 'ant-design-vue'
import { computed } from 'vue'
import i18n from '@/i18n'

export const columns = computed<TableColumnsType>(() => [
  {
    title: i18n.t('模板名称'),
    dataIndex: 'templateName',
    width: 132,
  },
  {
    title: i18n.t('模板编码'),
    dataIndex: 'templateCode',
    width: 120,
  },
  {
    title: i18n.t('通知类型'),
    dataIndex: 'type',
    width: 120,
  },
  {
    title: i18n.t('飞书模板ID'),
    dataIndex: 'feishuId',
    width: 120,
  },
  {
    title: i18n.t('启用状态'),
    dataIndex: 'status',
    width: 84,
  },
  {
    title: i18n.t('模板说明'),
    dataIndex: 'remarks',
    width: 140,
  },
  {
    title: i18n.t('创建人/创建时间'),
    dataIndex: 'created',
    width: 308,
  },
  {
    title: i18n.t('修改人/修改时间'),
    dataIndex: 'update',
    width: 308,
  },
  {
    title: i18n.t('操作'),
    dataIndex: 'handle',
    fixed: 'right',
    width: 96,
  },
])
