<template>
  <div class="action-list-container">
    <div class="card-base marginB16">
      <div class="fontSize14 fontWeight600">{{ i18n.t('执行动作列表') }}</div>
    </div>
    <div class="gray1 marginB24"></div>
    <FTable :columns="columns" :data-source="props.dataSource" :pagination="false" :scroll="{ x: 'min-content' }">
      <template #headerCell="{ column }"
        ><span v-if="column.required" class="action-star">*</span>{{ column.title }}</template
      >
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex == 'eventName'">
          <div v-if="editableData[record.id]">
            <FInput
              v-model:value="editableData[record.id][column.dataIndex]"
              :placeholder="i18n.t('请输入事件名称')"
              style="width: 140px"
            ></FInput>
          </div>
          <template v-else>{{ text ? text : '--' }}</template>
        </template>
        <template v-else-if="column.dataIndex == 'processConfigId'">
          <FSelect
            v-model:value="editableData[record.id][column.dataIndex]"
            v-if="editableData[record.id]"
            :placeholder="i18n.t('请选择')"
            :options="props.processTypeList.map(item => ({ value: item.id, label: item.processName }))"
            option-filter-prop="label"
            show-search
            @change="onChangeProcessConfig(record.id)"
            style="width: 140px"
          >
            <template #suffixIcon>
              <i class="cursor iconfont colorBBB fontSize12">&#xe799;</i>
            </template>
          </FSelect>
          <template v-else>{{ transformProcessText(text) }}</template>
        </template>
        <template v-else-if="column.dataIndex == 'eventPriority'">
          <div v-if="editableData[record.id]">
            <FInput
              v-model:value="editableData[record.id][column.dataIndex]"
              style="width: 140px; height: 32px"
              :placeholder="i18n.t('请输入序号')"
            ></FInput>
          </div>
          <template v-else>{{ text }}</template>
        </template>
        <template v-else-if="column.dataIndex == 'condition'">
          <div v-if="editableData[record.id]">
            <FTextarea
              v-model:value="editableData[record.id][column.dataIndex]"
              :placeholder="i18n.t('请输入表达式')"
              class="cursor"
              style="width: 264px; height: 32px"
            />
          </div>
          <template v-else>
            <FPopover trigger="hover" word-wrap placement="bottomLeft">
              <div class="text-row-3">{{ text }}</div>
              <template #content>
                <div style="width: 400px">{{ text }}</div>
              </template>
            </FPopover>
          </template>
        </template>
        <template v-else-if="column.dataIndex == 'eventType'">
          <div v-if="editableData[record.id]">
            <FSelect
              v-model:value="editableData[record.id][column.dataIndex]"
              style="width: 140px; height: 32px"
              :options="triggerList"
              :placeholder="i18n.t('请选择')"
            >
              <template #suffixIcon>
                <i class="cursor iconfont colorBBB fontSize12">&#xe799;</i>
              </template>
            </FSelect>
          </div>
          <template v-else>
            <div class="text-row-3">{{ transformTriggetText(text) }}</div>
          </template>
        </template>
        <template v-else-if="column.dataIndex == 'eventContent'">
          <div v-if="editableData[record.id]" class="action-flex">
            <FInput
              v-if="editableData[record.id].eventType == TypeActionEnum.UPDATE"
              v-model:value="editableData[record.id].updateField"
              class="cursor node"
              :placeholder="i18n.t('请输入字段名')"
              style="width: 140px; height: 32px"
            >
            </FInput>
            <FTextarea
              v-if="editableData[record.id].eventType == TypeActionEnum.UPDATE"
              v-model:value="editableData[record.id].updateCondition"
              :placeholder="i18n.t('请输入值表达式')"
              class="cursor update-condition"
              style="width: 264px; height: 32px"
            />
            <FInput
              v-if="editableData[record.id].eventType == TypeActionEnum.CALL_INTERFACE"
              v-model:value="editableData[record.id].url"
              class="cursor update-condition"
              :placeholder="i18n.t('请输入接口地址')"
              style="width: 264px; height: 32px"
            >
            </FInput>
            <FSelect
              v-if="
                editableData[record.id]?.eventType &&
                [TypeActionEnum.NODE_FININSH, TypeActionEnum.NODE_REJECT].includes(editableData[record.id].eventType)
              "
              v-model:value="editableData[record.id].milepostNodeId"
              :placeholder="i18n.t('请选择节点')"
              class="cursor update-condition"
              :options="nodeList"
              :field-names="{ label: 'milepostName', value: 'nodeId' }"
              @change="changeNode(record.id)"
              style="width: 140px; height: 32px"
            >
              <template #suffixIcon>
                <i class="cursor iconfont colorBBB fontSize12">&#xe799;</i>
              </template>
            </FSelect>
            <FSelect
              v-if="
                [
                  TypeActionEnum.TASK_FINISH,
                  TypeActionEnum.TASK_MESSAGE_NOTIFICATION,
                  TypeActionEnum.GROUP_TASK_MESSAGE_NOTIFICATION,
                ].includes(editableData[record.id].eventType)
              "
              v-model:value="editableData[record.id].milepostNodeId"
              :placeholder="i18n.t('请选择子流程')"
              class="cursor node"
              :options="nodeList"
              :field-names="{ label: 'milepostName', value: 'nodeId' }"
              @change="changeNode(record.id)"
              style="width: 140px; height: 32px"
            >
              <template #suffixIcon>
                <i class="cursor iconfont colorBBB fontSize12">&#xe799;</i>
              </template>
            </FSelect>
            <FSelect
              v-if="
                [
                  TypeActionEnum.TASK_FINISH,
                  TypeActionEnum.TASK_MESSAGE_NOTIFICATION,
                  TypeActionEnum.GROUP_TASK_MESSAGE_NOTIFICATION,
                ].includes(editableData[record.id].eventType)
              "
              v-model:value="editableData[record.id].processDefineKey"
              :options="processList"
              :field-names="{ label: 'processName', value: 'id' }"
              :placeholder="i18n.t('请选择流程id')"
              option-filter-prop="processName"
              style="width: 140px; margin-right: 24px"
              show-search
              allow-clear
              @change="changeProcess"
            />
            <FSelect
              v-if="
                [
                  TypeActionEnum.TASK_FINISH,
                  TypeActionEnum.TASK_MESSAGE_NOTIFICATION,
                  TypeActionEnum.GROUP_TASK_MESSAGE_NOTIFICATION,
                ].includes(editableData[record.id].eventType)
              "
              v-model:value="editableData[record.id].taskNodeId"
              :options="getTaskNodeList()"
              :field-names="{ label: 'name', value: 'id' }"
              show-search
              option-filter-prop="name"
              :placeholder="i18n.t('请选择任务')"
              class="node update-condition"
              style="width: 140px; height: 32px"
            >
              <template #suffixIcon>
                <i class="cursor iconfont colorBBB fontSize12">&#xe799;</i>
              </template>
            </FSelect>
            <div v-if="editableData[record.id].eventType == TypeActionEnum.ADD_TASK" class="add-task">
              <FSelect
                v-model:value="editableData[record.id].processDefineKey"
                :options="processList"
                :field-names="{ label: 'processName', value: 'id' }"
                :placeholder="i18n.t('请选择流程id')"
                option-filter-prop="processName"
                style="width: 180px; margin-right: 24px"
                show-search
                allow-clear
              />
              <FInput
                v-model:value="editableData[record.id].fields"
                class="cursor update-condition"
                :placeholder="i18n.t('请输入生成字段')"
                style="width: 140px; height: 32px"
              >
              </FInput>
            </div>
            <FSelect
              v-if="
                [
                  TypeActionEnum.NODE_MESSAGE_NOTIFICATION,
                  TypeActionEnum.TASK_MESSAGE_NOTIFICATION,
                  TypeActionEnum.GROUP_NODE_MESSAGE_NOTIFICATION,
                  TypeActionEnum.GROUP_TASK_MESSAGE_NOTIFICATION,
                ].includes(editableData[record.id].eventType)
              "
              v-model:value="editableData[record.id].templateCode"
              :options="allCodeList"
              show-search
              option-filter-prop="label"
              :placeholder="i18n.t('请选择通知模板')"
              class="node update-condition"
              style="width: 140px; height: 32px"
            >
              <template #suffixIcon>
                <i class="cursor iconfont colorBBB fontSize12">&#xe799;</i>
              </template>
            </FSelect>
            <FSelect
              v-if="
                [TypeActionEnum.TASK_MESSAGE_NOTIFICATION, TypeActionEnum.NODE_MESSAGE_NOTIFICATION].includes(
                  editableData[record.id].eventType
                )
              "
              v-model:value="editableData[record.id].roleCode"
              :options="roleAllCodeList"
              show-search
              option-filter-prop="label"
              :placeholder="i18n.t('请选择角色')"
              mode="multiple"
              allow-clear
              max-tag-count="responsive"
              class="node update-condition"
              style="width: 140px; height: 32px"
            >
              <template #suffixIcon>
                <i class="cursor iconfont colorBBB fontSize12">&#xe799;</i>
              </template>
            </FSelect>
            <FInput
              v-if="
                [
                  TypeActionEnum.GROUP_TASK_MESSAGE_NOTIFICATION,
                  TypeActionEnum.GROUP_NODE_MESSAGE_NOTIFICATION,
                ].includes(editableData[record.id].eventType)
              "
              v-model:value="editableData[record.id].chatsId"
              class="cursor update-condition"
              :placeholder="i18n.t('请输入群ID')"
              style="width: 264px; height: 32px"
            />
          </div>
          <template v-else>
            <div class="action-flex">
              <div class="node" v-if="record.eventType == TypeActionEnum.UPDATE">
                {{ record.updateField }}
              </div>
              <FPopover trigger="hover" word-wrap placement="bottomLeft">
                <div class="text-row-3 update-condition" v-if="record.eventType == TypeActionEnum.UPDATE">
                  {{ record.updateCondition }}
                </div>
                <template #content>
                  <div style="width: 400px">
                    {{ record.updateCondition }}
                  </div>
                </template>
              </FPopover>
              <div class="text-row-3 update-condition" v-if="record.eventType == TypeActionEnum.CALL_INTERFACE">
                {{ record.url }}
              </div>
              <div
                class="text-row-3 update-condition"
                v-if="[TypeActionEnum.NODE_FININSH, TypeActionEnum.NODE_REJECT].includes(record.eventType)"
              >
                {{ record.milepostNodeName }}
              </div>
              <div
                class="node"
                v-if="
                  [
                    TypeActionEnum.TASK_FINISH,
                    TypeActionEnum.TASK_MESSAGE_NOTIFICATION,
                    TypeActionEnum.GROUP_TASK_MESSAGE_NOTIFICATION,
                  ].includes(record.eventType)
                "
              >
                {{ record.milepostNodeName }}
              </div>
              <div
                class="node text-row-3 update-condition"
                v-if="
                  [
                    TypeActionEnum.TASK_FINISH,
                    TypeActionEnum.TASK_MESSAGE_NOTIFICATION,
                    TypeActionEnum.GROUP_TASK_MESSAGE_NOTIFICATION,
                  ].includes(record.eventType)
                "
              >
                {{ record.taskNodeName }}
              </div>
              <div v-if="record.eventType == TypeActionEnum.ADD_TASK" class="add-task-action">
                <div class="node" v-if="record.processDefineName">{{ record.processDefineName }}</div>
                <div class="text-row-3 update-condition" v-if="record.fields">
                  {{ record.fields }}
                </div>
              </div>
              <div
                class="node"
                v-if="
                  [
                    TypeActionEnum.NODE_MESSAGE_NOTIFICATION,
                    TypeActionEnum.GROUP_NODE_MESSAGE_NOTIFICATION,
                    TypeActionEnum.TASK_MESSAGE_NOTIFICATION,
                    TypeActionEnum.GROUP_TASK_MESSAGE_NOTIFICATION,
                  ].includes(record.eventType)
                "
              >
                {{ (allCodeList.find((item: any) => record.templateCode === item.value) || {})?.label || '--' }}
              </div>
              <div
                class="node"
                v-if="
                  [TypeActionEnum.TASK_MESSAGE_NOTIFICATION, TypeActionEnum.NODE_MESSAGE_NOTIFICATION].includes(
                    record.eventType
                  )
                "
              >
                {{ getRoleCodeFn(record.roleCode) }}
              </div>
              <div
                class="node"
                v-if="
                  [
                    TypeActionEnum.GROUP_TASK_MESSAGE_NOTIFICATION,
                    TypeActionEnum.GROUP_NODE_MESSAGE_NOTIFICATION,
                  ].includes(record.eventType)
                "
              >
                {{ record.chatsId }}
              </div>
            </div>
          </template>
        </template>
        <template v-else-if="column.dataIndex == 'operation'">
          <div class="editable-row-operations">
            <span v-if="editableData[record.id]">
              <FTooltip :title="i18n.t('保存')" placement="top">
                <a @click="save(record.id)" class="icon iconfont icontubiao_baocun1"></a>
              </FTooltip>
            </span>
            <span v-else>
              <FTooltip :title="i18n.t('编辑')" placement="top">
                <a class="icon iconfont cursor icontubiao_xietongbianji" @click="edit(record, true)"></a>
              </FTooltip>
              <FTooltip :title="i18n.t('删除')" placement="top">
                <a class="icon iconfont icontubiao_xietongshanchu" @click="deleteAction(record.id)"></a>
              </FTooltip>
            </span>
          </div>
        </template>
      </template>
    </FTable>
    <div class="add">
      <FButton type="link" @click="addAction">
        <template #icon>
          <PlusOutlined />
        </template>
        {{ i18n.t('添加动作') }}
      </FButton>
    </div>
  </div>
</template>
<script lang="ts" setup>
import lodash from 'lodash'
import { computed, onMounted, reactive, ref } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { messageInstance as message } from '@fs/smart-design'
import type { UnwrapRef } from 'vue'
import { actionTypeList, event, nodeType, processTye, taskType, TypeActionEnum } from '@/types/excutionListModel'
import { transformFieldIdToLabel } from '@/utils/filter'
import { getResNodeList, getTaskList } from '@/api/automotion'
import { ProcessModel } from '@/types/handle'
import { getProcess, getAllCode, getRoleAllCode } from '@/api'
import { deepClone, useI18n } from '@/utils'
const emits = defineEmits(['deleteAction', 'addAction', 'add'])
const i18n = useI18n()
const columns = computed(() => [
  {
    title: i18n.t('事件名称'),
    dataIndex: 'eventName',
    required: true,
    width: 200,
  },
  {
    title: i18n.t('流程类型'),
    dataIndex: 'processConfigId',
    required: true,
    width: 240,
  },
  {
    title: i18n.t('执行序号'),
    dataIndex: 'eventPriority',
    required: true,
    width: 200,
  },
  {
    title: i18n.t('附加过滤条件'),
    dataIndex: 'condition',
    required: false,
    width: 400,
  },
  {
    title: i18n.t('动作类型'),
    dataIndex: 'eventType',
    required: true,
    width: 160,
  },
  {
    title: i18n.t('动作内容'),
    dataIndex: 'eventContent',
    required: true,
    width: 600,
  },
  {
    title: i18n.t('操作'),
    class: 'operation',
    dataIndex: 'operation',
    required: false,
    width: 100,
    fixed: 'right',
  },
])
const triggerList = actionTypeList
const nodeList = ref<nodeType[]>([])
const taskNodeList = ref<taskType[]>([])
let processDefineNodeKey = ref<string>('')
const processList = ref<ProcessModel[]>([])
const processTaskNodeList = ref<taskType[]>([])
interface IProps {
  dataSource: event[]
  processTypeList: processTye[]
}
const props = defineProps<IProps>()

const allCodeList = ref<any[]>([])
const roleAllCodeList = ref<any[]>([])
const editableData: UnwrapRef<Record<string, event>> = reactive({})

const getRoleCodeFn = (value: any) => {
  if (!value) return '--'
  return value
    .split(',')
    .map((selectItem: any) => {
      return (roleAllCodeList.value.find((item: any) => selectItem === item.value) || {})?.label || '--'
    })
    .join('；')
}

const onChangeProcessConfig = async (id: number | string, isEdit = false) => {
  const curNode = editableData[id]
  // 节点完成单选
  if (!isEdit) {
    curNode.milepostNodeId = ''
    curNode.taskNodeId = ''
    nodeList.value = []
    taskNodeList.value = []
  }
  // 获取里程碑节点
  if (curNode.processConfigId != '' && curNode.processConfigId != undefined) {
    await getMilePostList(curNode)
  }
}
const getMilePostList = async (curNode: event) => {
  if (curNode.processConfigId != '' && curNode.processConfigId != undefined) {
    const resNode = await getResNodeList(curNode.processConfigId)
    nodeList.value = resNode.data
  } else {
    curNode.milepostNodeId = ''
    nodeList.value = []
  }
}
const changeProcess = async (value: any, option: any[]) => {
  if (!value || value === processDefineNodeKey.value) {
    processTaskNodeList.value = []
    return
  }
  const res = await getTaskList(value)
  if (res.code === 200) {
    processTaskNodeList.value = res.data || []
  } else {
    processTaskNodeList.value = []
  }
}
const getTaskNodeList = () => {
  return [...taskNodeList.value, ...processTaskNodeList.value].reduce((list: taskType[], cur: taskType) => {
    !list.find((item: taskType) => item.id === cur.id) && list.push(cur)
    return list
  }, [])
}
const changeNode = async (id: number | string, isEdit = false) => {
  const curNode = editableData[id]
  if (!isEdit) {
    curNode.taskNodeId = ''
    taskNodeList.value = []
  }
  if (curNode.milepostNodeId != '' && curNode.milepostNodeId != undefined) {
    await getTask(curNode)
  }
}
const getTask = async (curNode: event) => {
  let node = nodeList.value.filter(item => item.nodeId == curNode.milepostNodeId)
  if (node.length > 0) {
    processDefineNodeKey.value = node[0].processDefineNodeKey
    await getTaskNode(curNode)
  }
}
const getTaskNode = async (curNode: event) => {
  if (processDefineNodeKey.value != '' && processDefineNodeKey.value != undefined) {
    // 获取任务节点
    const taskNode = await getTaskList(processDefineNodeKey.value)
    taskNodeList.value = taskNode.data
  } else {
    curNode.taskNodeId = ''
    taskNodeList.value = []
  }
}
const edit = async (record: event, isEdit = true) => {
  let id = record.id
  editableData[id] = lodash.cloneDeep(props.dataSource.filter(item => id == item.id)[0])
  // 编辑回显
  if (isEdit) {
    if ([7, 9].includes(editableData[id]?.eventType) && editableData[id]?.roleCode) {
      editableData[id].roleCode = editableData[id].roleCode.split(',')
    }
    await onChangeProcessConfig(id, true)
    await changeNode(id, true)
  }
}
const save = (id: number | string) => {
  let obj = Object.assign(props.dataSource.filter(item => id == item.id)[0], editableData[id])
  // 保存当前事件的节点名称和任务名称
  let processNode = nodeList.value.find(item => item.nodeId == obj.milepostNodeId)
  let taskNode = getTaskNodeList().find(item => item.id == obj.taskNodeId)
  let processDefineNode = processList.value.find(item => item.id == obj.processDefineKey)
  if (processNode) {
    obj.milepostNodeName = processNode.milepostName
  }
  if (taskNode) {
    obj.taskNodeName = taskNode.name
  }
  if (processDefineNode) {
    obj.processDefineName = processDefineNode.processName
  }
  if ([7, 9].includes(obj.eventType) && obj.roleCode) {
    obj.roleCode = obj.roleCode.join()
  } else {
    delete obj.roleCode
  }
  emits('addAction', obj, (id: number | string) => {
    cancel(id)
  })
}
const cancel = (id: number | string) => {
  delete editableData[id]
}
const deleteAction = (id: number | string) => {
  // 触发父组件的emit
  emits('deleteAction', id, (id: number) => {
    cancel(id)
  })
}
const addAction = () => {
  if (Object.keys(editableData).length > 0) {
    message.info(i18n.t('请先保存动作再添加'))
    return
  }
  // 添加列表项
  emits('add', (obj: event) => {
    // 新添加的自动变为编辑状态
    edit(obj, false)
  })
}
const transformProcessText = (text: number | string) => {
  return transformFieldIdToLabel(props.processTypeList, 'id', 'processName', text)
}
const transformTriggetText = (text: number | string) => {
  return transformFieldIdToLabel(triggerList.value, 'value', 'label', text)
}

const getAllCodeFn = async () => {
  const res = await getAllCode()
  if (res.code !== 200) throw new Error(res.msg)
  allCodeList.value = res?.data || []
}

const getRoleAllCodeFn = async () => {
  const res = await getRoleAllCode()
  if (res.code !== 200) throw new Error(res.msg)
  roleAllCodeList.value = res?.data || []
}

onMounted(() => {
  // 初始化
  getProcess({ currPage: 1, pageSize: 9999, processName: '' }).then(res => {
    processList.value = deepClone(res.data.list)
    processList.value.forEach(item => {
      item.id = item.id.toString()
    })
  })
  requestIdleCallback(getAllCodeFn)
  requestIdleCallback(getRoleAllCodeFn)
})
</script>

<style lang="scss" scoped>
.editable-row-operations a {
  margin-right: 8px;
}
.card-base {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 14px;
}
.gray1 {
  display: block;
  background-color: #eee;
  height: 1px;
  margin-left: -24px;
  margin-right: -24px;
  margin-bottom: 24px;
}
.marginB16 {
  margin-bottom: 16px;
}
.action-star {
  color: #f04141;
  margin-right: 2px;
}
.width290 {
  width: 290px;
}
:deep(.operation) {
  min-width: 100px;
  white-space: nowrap;
}
.action-list-container {
  .add {
    margin: 8px 0 24px 0;
    font-size: 12px;
    :deep(.fs-btn) {
      span {
        font-size: 12px;
      }
    }
  }
  :deep(.fs-table-wrapper) {
    .text-row-3 {
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
    }
    .fs-table-tbody {
      .fs-table-cell {
        height: 32px;
        line-height: 16px;
        .add-task {
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
    .action-flex {
      display: flex;
      flex-direction: row;
      align-items: center;
      .node {
        width: 140px;
        margin-right: 16px;
      }
      .update-condition {
        flex: 1;
      }
    }
    .add-task-action {
      display: flex;
    }
  }
  .add-action {
    color: #378eef;
    margin-top: 8px;
  }
}
</style>
