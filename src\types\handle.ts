import { Dayjs } from 'dayjs'
export interface IProcessRes {
  code: number
  msg: string
  data: IProcess[]
}
export interface IProcess {
  attachmentData: any
  completeTime: string
  contentData: any
  createdTime: string
  creator: string
  duration: number
  formData: any
  id: number
  instanceId: number
  instanceMilepostId: number
  instanceMilepostKey: string
  isOverdue: number
  milepostProcesskey: number
  nodeInstanceProcessKey: number
  nodeStartTime: string
  order: number
  status: number
  superviser: string
  superviserUuid: string
  updatedTime: string
  forcastTime: string
  updatedUser: string
  milepostRole: number
  formKey: string
  topicName: string
  processKey: string
  key?: number
  instanceTopicName: string
  processInstanceCode: string
  prefixTaskName: string
  processType: string
  planTime?: number
  invalid: number
  children?: ITask[]
  [key: string]: any
  rejectMsg: string
}
export interface ITask {
  approver: string | null
  approverRole: number | string
  approverRoleCode: string | null
  attachmentData: any
  childTaskFlag: 0 | 1
  completed: number
  contentData: unknown | string
  createdTime: string
  creator: string
  creatorRole: number | string
  duration: number
  forcastTime: string | null
  formData: any
  formKey: string
  gatewayFlag: number
  id: number
  isOverdue: number
  milepostId: number
  nextTask: number
  preTask: number
  preconditions: string
  status: number
  superviser: string | null
  superviserRole: number | string
  superviserRoleCode: string | null
  taskCompletedTime: string
  taskName: string
  taskStartTime: string
  updatedTime: string
  updatedUser: string
  order: number
  taskCompletedTimeStr: string
  prefixTaskName: string
  key?: number
  isSys: 0 | 1
  children?: ITask[]
  taskType?: string
  sort: number
  [key: string]: unknown
}
export type SortITask = Pick<ITask, 'id' | 'sort'>
export interface IQueryParams {
  instanceId: number | string
  isTree?: number
}

export interface PoolParams {
  formData: any
  milepostId: number
}

export interface ICreateParams {
  taskName: string
  superviser: null | string
  difficulty: null | string
  approver: null | string
  preconditions?: string | string[]
  forcastTime: string | Dayjs
  completed?: number
  isSys?: number
  preTask?: number
  contentData: any
  milepostId: null | number
  childTaskFlag?: number
  id?: null | number
}
export interface IResData {
  code: number
  data: any
  msg: string
  traceId: string
}
export interface SuccessIRes {
  code: number
  msg: string
  traceId: string
  success: boolean
  data: any
}

export interface IUserRes {
  code: number
  data: IUser[]
  msg: string
  traceId: string
}

export interface IUser {
  name?: string
  nameCh: string
  nameEn: string
  feiShuName: string
  nameLocal: string
  organizationsName: string
  uuid: string
}

export interface IPreTaskRes {
  code: number
  data: IPreTask[]
  msg: string
  traceId: string
}
export interface IPreTask {
  milepostId: number
  taskName: string
}

export interface IPreParams {
  milepostId: number
  preTaskId: number
}

export interface IMakeParams {
  milepostId?: number
  taskId?: number
}

export interface IDeleteParams {
  taskId: number
}
export interface ITransferParams {
  instanceId: number | string
  milepostId: number | string
  makeUuidList?: string[]
}

export interface ITurnParams {
  superviser: string
  milepostId: number | string
}

export interface IRejectParams {
  instanceId: number | string
  msg: string
  sourceMilepostId: number | string
  targetMilepostId: number | string
}

export interface ISubmitParams {
  milepostInfoId: number | string
  nextMilepostId?: any
  makeUuidList?: string[]
}

export interface ISaveParams {
  attachmentData: []
  contentData: any
  duration: number
  formData: any
  id: number
  instanceProcessKey: string
  milepostProcessKey: number
  superviser: string
}

export interface IPage {
  pageNum: number // 当前页
  pageSize: number // 每页条数
  total: number // 总条数
}
export interface IRes<T = null> {
  code: number
  msg: string
  traceId: string
  data: T
}
export interface IOperationRes<T = null> {
  pageNum: number
  pageSize: number
  total: number
  totalCount: number
  list: T[]
}
export interface IOperation {
  content: string
  createTime: string
  createUser: string
  type: number
}

export interface IProcessModelRes<T = null> {
  currPage: number
  pageSize: number
  total: number
  list: T[]
}

export interface ProcessModel {
  context: string
  id: number | string
  processName: string
}

export interface IPermitInstance {
  id: number
  topicName: string
  processInstanceCode: string
}

export interface IProcessClassTypeMilepost {
  id: number
  nodeId: string
  parentId: number
  milepostName: string
  nodeMode: number
}

export interface IProcessClassTypeTag {
  id: number
  field: string
  name: string
  value: string
  parentId: number
  status: number
  isDel: number
  isDirect: number
  children: IProcessClassTypeTag[]
}

export interface IProcessClassType {
  id: number
  processDefineKey: string
  processName: string
  dictionarys?: IProcessClassTypeTag[]
  nodes: IProcessClassTypeMilepost[]
}

export interface ITabOption {
  key: string
  tab: string
  tabComponent?: string
}
