<template>
  <div
    class="tiling-body"
    v-show="props.arrow == 'expand'"
    v-if="props.currtMilepostChildren && Array.isArray(taskList)"
  >
    <Draggable
      v-if="taskList.length > 0"
      :list="taskList"
      :force-fallback="true"
      :disabled="isDisableSort"
      item-key="id"
      handle=".move"
      ghost-class="ghost"
      chosen-class="chosenClass"
      animation="300"
      @end="onTaskDraggableEnd"
    >
      <template #item="{ element: item }">
        <div class="list-group-item">
          <div class="task-list">
            <div class="form-cell">
              <FCollapse v-model:activeKey="activeKey" expand-icon-position="left">
                <FCollapsePanel :key="item.id" :show-arrow="false">
                  <div class="collapse-box">
                    <div v-if="!item.formKey && [2, 3, 5].includes(item.status)">
                      <!--兼容历史数据展示-->
                      <HistoryInfo :task-record="item"></HistoryInfo>
                    </div>
                    <!--已审核驳回状态-->
                    <div
                      class="review-suggestion"
                      v-if="item.contentData && JSON.parse(item.contentData as string).verifyDesc"
                    >
                      <span class="color999 fontSize12 marginR10">{{ i18n.t('审核意见') }}：</span>
                      <div class="review-viewer-tiling">
                        <div class="ql-container-tiling ql-snow color333">
                          <div
                            class="ql-editor-tiling"
                            v-html="item.contentData ? JSON.parse(item.contentData as string).verifyDesc : ''"
                          ></div>
                        </div>
                      </div>
                    </div>
                    <div v-if="item.approverRole == 1 && item.status == 6">
                      <VerifyForm
                        :task="item"
                        :role="props.role"
                        :verify-desc="item.contentData ? JSON.parse(item.contentData as string).verifyDesc : ''"
                      ></VerifyForm>
                    </div>
                    <!--非审核人-->
                    <div v-else-if="!!item.formKey">
                      <TaskForm :task="item"></TaskForm>
                    </div>
                  </div>
                  <template #header>
                    <div class="customize-header fontSize12">
                      <span
                        v-if="checkEmptyFlag(item)"
                        :class="[
                        'icon',
                        'iconfont',
                        'center-middle',
                        'marginR4',
                        activeKey.includes(`${item.id}` as never) ? 'icontubiao_xiajiang' : 'icontubiao_shangsheng',
                      ]"
                      >
                      </span>
                      <span
                        class="icon iconfont icontubiao_xijie_mian collapse-header-left-icon marginR7 center-middle move"
                      ></span>
                      <span class="center-middle fontWeight500 marginR4 fontSize14">{{ item.taskName }}</span>
                      <span
                        class="center-middle fontWeight500 marginR4 align-icon in-block"
                        :style="{
                          color: TFTaskStatusColor(item.status as keyof typeof TASK_STATUS_COLOR),
                          backgroundColor: TFTaskStatusBackgropundColor(item.status as keyof typeof TASK_STATUS_COLOR),
                        }"
                      >
                        {{ TASK_STATUS_NAME[item.status as unknown as keyof typeof TASK_STATUS_NAME] }}
                      </span>
                      <!--有审核说明，并且是进行中任务就认为是驳回任务-->
                      <span
                        class="center-middle align-icon fontWeight500 marginR4 reject in-block"
                        v-if="item.contentData && JSON.parse(item.contentData as string).verifyDesc && item.status == 1"
                        >{{ i18n.t('驳回') }}
                      </span>
                      <!--快速编辑负责人-->
                      <div
                        class="font333 user-click marginL20"
                        @click.stop
                        v-if="editableData[item.id] && editableData[item.id]['userClick']"
                      >
                        <FSelect
                          style="width: 141px"
                          dropdown-class-name="user-down"
                          :placeholder="i18n.t('请选择')"
                          v-model:value="editableData[item.id]['superviserUuid']"
                          :options="superviserUserList"
                          :field-names="{ label: 'name', value: 'uuid' }"
                          :filter-option="filterOption"
                          :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
                          @select="handleUserSelect(item.id)"
                          show-search
                        >
                        </FSelect>
                      </div>
                      <!--正常显示状态-->
                      <div v-else class="font333 user-hover marginL20" @click="userClick(item, $event)">
                        <img src="@/assets/images/process-detail/user.png" class="im marginR4" />
                        <span :class="['user-name', 'center-middle', !item.superviserRoleCode ? 'marginR16' : '']">
                          <span v-if="item.superviser == '--'" class="color999">{{ i18n.t('待填') }}</span>
                          <span v-else>{{ item.superviser }}</span>
                        </span>
                      </div>
                      <span class="marginR24 center-middle user-height" v-if="item.superviserRoleCode"
                        >&nbsp;-&nbsp;&nbsp;{{ transformRoleText(item.superviserRoleCode) }}
                      </span>

                      <HandleTime
                        v-model:editableData="editableData"
                        :id="item.id"
                        :currt-milepost-children="currtMilepostChildren"
                        :forcast-time="item.forcastTime"
                        :task-start-time="item.taskStartTime"
                        :content-data="item.contentData"
                      />
                      <!-- 快速编辑时间 -->
                      <!-- <div class="user-height" @click="timeClick(item, $event)">
                        <span class="icon iconfont iconkaoqingongshiguanli marginR4 fontSize16 color999"></span>
                        <span class="color999" style="vertical-align: top">{{ i18n.t('预计完成时间') }}: </span>
                      </div>
                      <FDatePicker
                        v-if="editableData[item.id] && editableData[item.id]['timeClick']"
                        format="YYYY-MM-DD"
                        show-time
                        style="width: 120px; margin-right: 16px"
                        :disabled-date="disabledDate"
                        @click.stop
                        @change="(date: Dayjs) => timePickerChange(date, item.id)"
                      />
                      <span class="center-middle user-hover marginR16" @click="timeClick(item, $event)" v-else>
                        <span v-if="transformDate(item.forcastTime, 'YYYY-MM-DD') == '--'" class="color999">{{
                          i18n.t('待填')
                        }}</span>
                        <span v-else>{{ transformDate(item.forcastTime, 'YYYY-MM-DD') }}</span>
                      </span> -->
                      <span
                        v-if="item.realityDuration && [3].includes(item.status)"
                        class="center-middle fontWeight500 marginR4 align-icon in-block"
                        :style="{
                          color: TFTaskStatusColor(item.status as keyof typeof TASK_STATUS_COLOR),
                          backgroundColor: TFTaskStatusBackgropundColor(item.status as keyof typeof TASK_STATUS_COLOR),
                        }"
                      >
                        <!-- {{ TASK_STATUS_NAME[item.status as unknown as keyof typeof TASK_STATUS_NAME] }} -->
                        {{ i18n.t('总耗时') }}
                        {{ item.realityDuration }}
                        {{ i18n.t('天') }}
                      </span>
                      <NoticeInfo
                        v-if="[0, 1, 4, 6].includes(item.status)"
                        style="margin-left: -14px"
                        class="marginR24"
                        key-id="taskId"
                        :value-id="item.id"
                      />
                      <FPopover
                        trigger="hover"
                        placement="bottom"
                        :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
                        overlay-class-name="tiling-more-overlay-class"
                      >
                        <template #content>
                          <div class="more-container">
                            <ul class="ul-more">
                              <li class="fontSize12">
                                <span class="more-left color999">{{ i18n.t('派发人') }}：</span>
                                <span class="more-right font333">{{ item.superviser }}</span>
                              </li>
                              <li class="fontSize12" v-if="item.approver">
                                <span class="more-left color999">{{ i18n.t('审核人') }}：</span>
                                <span class="more-right font333">{{ item.approver }}</span>
                              </li>
                              <li class="fontSize12">
                                <span class="more-left color999">{{ i18n.t('前置任务') }}：</span>
                                <span class="more-right font333">{{ item.prefixTaskName }}</span>
                              </li>
                              <li class="fontSize12">
                                <span class="more-left color999">{{ i18n.t('任务描述') }}：</span>
                                <span
                                  class="more-right font333"
                                  v-if="JSON.parse(item.contentData as string) && JSON.parse(item.contentData as string).taskDesc"
                                  v-html="JSON.parse(item.contentData as string).taskDesc"
                                >
                                </span>
                                <span class="more-right font333" v-else>--</span>
                              </li>
                              <li class="fontSize12" @click.stop>
                                <span class="more-left color999">{{ i18n.t('附件') }}：</span>
                                <div class="more-right-file font333" v-if="item.formKey">
                                  <div v-if="item.formData && item.formData?.file">
                                    <div v-for="file in item.formData?.file" :key="file.url" style="margin-bottom: 5px">
                                      <a class="file-ellipsis fontS12" @click.prevent="download(file.url, file.name)">
                                        {{ file.name }}
                                      </a>
                                    </div>
                                  </div>
                                  <div v-if="!item.formData?.file || !item.formData?.file?.length">--</div>
                                </div>
                                <div class="more-right-file font333" v-if="!item.formKey">
                                  <div v-if="item && JSON.parse(item.attachmentData)">
                                    <div
                                      v-for="file in JSON.parse(item.attachmentData)"
                                      :key="file.resourseKey"
                                      style="margin-bottom: 5px"
                                    >
                                      <a
                                        class="file-ellipsis fontS12"
                                        @click.prevent="download(file.resourseKey, file.fileName)"
                                      >
                                        {{ file.fileName }}
                                      </a>
                                    </div>
                                  </div>
                                  <div v-if="!item.attachmentData || !JSON.parse(item.attachmentData)?.length">--</div>
                                </div>
                              </li>
                              <li v-if="item.delayReason" class="fontSize12">
                                <span class="more-left color999">{{ i18n.t('延期原因') }}：</span>
                                <span class="more-right font333">{{ item.delayReason }}</span>
                              </li>
                              <li v-if="item.taskCompletedTime" class="fontSize12">
                                <span class="more-left color999">{{ i18n.t('实际完成时间') }}：</span>
                                <span class="more-right font333">{{
                                  (item.taskCompletedTime && transformDate(item.taskCompletedTime, 'YYYY-MM-DD')) ||
                                  '--'
                                }}</span>
                              </li>
                              <li
                                v-if="JSON.parse(item.contentData as string) && JSON.parse(item.contentData as string)?.taskTimeHour"
                                class="fontSize12"
                              >
                                <span class="more-left color999">{{ i18n.t('时效& 时效计算预计完成时间') }}：</span>
                                <span class="more-right font333"
                                  >{{ JSON.parse(item.contentData as string)?.taskTimeHour + 'H'
                                  }}{{
                                    item.forcastTime && '-' + transformDate(item.forcastTime, 'YYYY-MM-DD HH:mm:ss')
                                  }}</span
                                >
                              </li>
                              <li
                                v-if="processConfigInfo?.value?.isUpdateCompleteSend === 1 && JSON.parse(item.contentData as string) && JSON.parse(item.contentData as string)?.forcastTimeRemark"
                                class="fontSize12"
                              >
                                <span class="more-left color999">{{ i18n.t('预计完成时间变更原因') }}：</span>
                                <span class="more-right font333">{{
                                  JSON.parse(item.contentData as string) &&
                                  JSON.parse(item.contentData as string)?.forcastTimeRemark
                                }}</span>
                              </li>
                            </ul>
                          </div>
                        </template>
                        <span class="blue-link center-middle user-height">{{ i18n.t('更多信息') }}</span>
                      </FPopover>
                    </div>
                  </template>
                  <template #extra>
                    <span class="custom-header-right fontSize12 marginR16" v-if="item.isSys == 1" @click.stop>
                      {{ i18n.t('当前节点完成') }}
                    </span>
                    <FButton
                      style="display: inline-block; margin-right: 16px"
                      type="primary"
                      size="small"
                      v-if="item.superviserRole == 1 && !item.formKey && [0, 1, 4, 6].includes(item.status)"
                      @click="onSubmit(item)"
                      >{{ i18n.t('提交') }}
                    </FButton>
                    <div class="tiling-more-edit-parent" @click.stop>
                      <FPopover
                        trigger="click"
                        placement="bottom"
                        overlay-class-name="tiling-more-edit-overlay-class"
                        :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
                      >
                        <template #content>
                          <!-- 任务状态，0未开始，1进行中，2按时完成，3逾期完成，4待指派，5办结，6待审核 -->
                          <div
                            class="more-item cursor"
                            @click.stop="editTaskModal(item)"
                            v-if="
                              (item.creatorRole == 1 || item.taskEditConfig == 1) && [0, 1, 4, 6].includes(item.status)
                            "
                          >
                            <span class="icon iconfont marginR8">&#xe76e;</span>
                            <span>{{ i18n.t('编辑') }}</span>
                          </div>
                          <div
                            class="more-item cursor"
                            @click.stop="deleteTaskModal(item)"
                            v-if="
                              (item.creatorRole == 1 || item.taskEditConfig == 1) && [0, 1, 4, 6].includes(item.status)
                            "
                          >
                            <span class="icon iconfont marginR8">&#xe7a4;</span>
                            <span>{{ i18n.t('删除') }}</span>
                          </div>
                          <div
                            @click.stop="revokeTaskModal(item)"
                            class="more-item cursor"
                            v-if="[2, 3, 5].includes(item.status)"
                          >
                            <span class="icon iconfont marginR8">&#xe815;</span>
                            <span>{{ i18n.t('回滚') }}</span>
                          </div>
                        </template>
                        <span class="icon iconfont icongengduo_ fontSize12 color333"></span>
                      </FPopover>
                    </div>
                  </template>
                </FCollapsePanel>
              </FCollapse>
            </div>
          </div>
        </div>
      </template>
    </Draggable>
    <NotificationModal
      v-model:value="notificationModel.flag"
      v-model:loading="notificationModel.loading"
      :title="(notificationModel.title as ProcessModalTilte)"
      :role="(processRoleInfo as IProcessRoleAndUser[])"
      default-role-key="taskId"
      :default-role-value="notificationModel.data.id"
      :is-delay-status="isDelayStatus"
      @submit="handleNotificationMilepostTask"
    />
  </div>
</template>

<script lang="ts" setup>
import TaskForm from './TaskForm.vue'
import {
  EmitType,
  TASK_STATUS_BACKGROUND_COLOR,
  TASK_STATUS_COLOR,
  TASK_STATUS_NAME,
  ProcessModalTilte,
} from '@/views/process-detail/config'
import { computed, inject, reactive, ref, UnwrapRef, watch } from 'vue'
import { ITask, IUser, SortITask, IProcess } from '@/types/handle'
import { useStore } from 'vuex'
import dayjs, { Dayjs } from 'dayjs'
import { deepClone, getUserInfo, transformDate, useI18n, download } from '@/utils'
import { IProcessRoleAndUser } from '@/types/request'
import { transformFieldIdToLabel } from '@/utils/filter'
import { submitMission, resortTask } from '@/api'
import { messageInstance as message } from '@fs/smart-design'
import { IFormQueryData } from '../interface'
import NoticeInfo from '@/components/NoticeInfo/index.vue'
import HistoryInfo from './HistoryInfo.vue'
import Draggable from 'vuedraggable'
import VerifyForm from './VerifyForm.vue'
import HandleTime from './HandleTime.vue'
import NotificationModal from '@/components/NotificationModal/index.vue'

type UserType = IUser & { name: string }
interface IProps {
  currtMilepostChildren: any
  arrow: string
  role?: IProcessRoleAndUser[]
  formQueryData: IFormQueryData
  isDisableSort?: boolean
}

interface IModel<T = ITask> {
  flag: boolean
  loading?: boolean
  data: T
  [key: string]: unknown
}
const notificationModel = reactive<IModel<IProcess>>({ flag: false, data: {} as IProcess, title: '', loading: false })
const processRoleInfo = inject<IProcessRoleAndUser[]>('processRoleInfo') // 流程角色信息
const processConfigInfo = inject<Record<string, any>>('processConfigInfo')
const i18n = useI18n()
const store = useStore()
const props = defineProps<IProps>()

const superviserUserList = ref<UserType[]>([])
const activeKey = ref([])

const editableData: UnwrapRef<Record<string, ITask>> = reactive({})
const userData = computed<UserType[]>(() => store.state.user.allUser || [])
const taskList = ref<ITask[]>(JSON.parse(JSON.stringify(props.currtMilepostChildren ?? [])))
const paramsWrapper = inject('paramsWrapper') as (data: any) => any
const initToDoList = inject('initToDoList') as () => void
const userInfo = getUserInfo()
// eslint-disable-next-line prettier/prettier
const quickEditHandleTaskSubmit = inject('quickEditHandleTaskSubmit') as (
  task: ITask,
  callback: (id: string | number) => void
) => void // 快速编辑
const disabledDate = (current: Dayjs) => current && current < dayjs().subtract(1, 'days').endOf('day')
const filterOption = (input: string, option: UserType) => option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
const milestoneOperate = inject('milestoneOperate') as (key: keyof typeof EmitType, data: unknown) => void
const refresh = inject('refresh') as () => void
const TFTaskStatusColor = (status: keyof typeof TASK_STATUS_COLOR) => TASK_STATUS_COLOR[status]
const TFTaskStatusBackgropundColor = (status: keyof typeof TASK_STATUS_BACKGROUND_COLOR) =>
  TASK_STATUS_BACKGROUND_COLOR[status]
const checkEmptyFlag = (item: any) => {
  if (item.formKey) return true
  if (!item.formKey && [2, 3, 5].includes(item.status)) return true
  if (item.contentData && JSON.parse(item.contentData as string).verifyDesc) return true
  if (item.approverRole == 1 && item.status == 6) return true
  return false
}

const filterTask = (newValue: IFormQueryData) => {
  taskList.value = props.currtMilepostChildren as ITask[]
  if (!taskList.value || taskList.value.length <= 0) {
    return
  }
  newValue.incompleteChecked &&
    (taskList.value = taskList.value.filter(
      (item: ITask) => item.superviserUuid === userInfo.uuid || item.approverUuid === userInfo.uuid
    ))
  newValue.responsibleChecked &&
    (taskList.value = taskList.value.filter((item: ITask) => ![2, 3, 5].includes(item.status)))
  if (newValue.isSure) {
    // 确定查询
    newValue.superviser &&
      (taskList.value = taskList.value.filter((item: ITask) => item.superviserUuid == newValue.superviser))
    newValue.approver &&
      (taskList.value = taskList.value.filter((item: ITask) => item.approverUuid == newValue.approver))
    newValue.status && (taskList.value = taskList.value.filter((item: ITask) => item.status == newValue.status))
    if (newValue.isSys == 0 || newValue.isSys == 1) {
      taskList.value = taskList.value.filter((item: ITask) => item.isSys == newValue.isSys)
    }
    if (newValue.startTime && newValue.endTime) {
      taskList.value = taskList.value.filter(
        (item: ITask) =>
          item.taskCompletedTimeStr >= newValue.startTime && item.taskCompletedTimeStr <= newValue.endTime
      )
    }
  }
}

const cancel = (id: number | string) => {
  delete editableData[id]
}
const timeClick = (task: ITask, event: MouseEvent) => {
  event.stopPropagation()
  if (Array.isArray(props.currtMilepostChildren) && props.currtMilepostChildren.length > 0) {
    editableData[task.id] = deepClone(props.currtMilepostChildren.find(item => task.id == item.id) as ITask)
    editableData[task.id].timeClick = true
  }
}
const userClick = (task: ITask, event: MouseEvent) => {
  event.stopPropagation()
  if (Array.isArray(props.currtMilepostChildren) && props.currtMilepostChildren.length > 0) {
    editableData[task.id] = deepClone(props.currtMilepostChildren.find(item => task.id == item.id) as ITask)
    editableData[task.id].userClick = true
  }
}
const handleUserSelect = (id: string | number) => {
  if (Array.isArray(props.currtMilepostChildren) && props.currtMilepostChildren.length > 0) {
    const currTask = deepClone(props.currtMilepostChildren.find(item => id == item.id) as ITask)
    const obj = Object.assign({}, currTask, editableData[id])
    obj.forcastTime = obj.forcastTime ? dayjs(obj.forcastTime).format('YYYY-MM-DD HH:mm:ss') : null
    // 发送编辑请求
    quickEditHandleTaskSubmit(obj, (id: string | number) => {
      cancel(id)
    })
  }
}
const timePickerChange = (date: any, id: number | string) => {
  if (Array.isArray(props.currtMilepostChildren) && props.currtMilepostChildren.length > 0) {
    const currTask = deepClone(props.currtMilepostChildren.find(item => id == item.id) as ITask)
    const obj = Object.assign({}, currTask, editableData[id])
    obj.forcastTime = date ? dayjs(date).format('YYYY-MM-DD HH:mm:ss') : null
    // 发送编辑请求
    quickEditHandleTaskSubmit(obj, (id: string | number) => {
      cancel(id)
    })
  }
}
const isDelayStatus = ref<boolean>(false)
const onSubmit = (task: ITask) => {
  // 有审核人情况不显示选择抄送人
  const data: Record<string, unknown> = { id: task.id }
  if (task.approverUuid) {
    submitMission(paramsWrapper(data)).then(async res => {
      if (res.code == 200) {
        message.success(i18n.t('提交成功'))
        await refresh()
        initToDoList && (await initToDoList())
      } else {
        message.error(res.msg)
      }
    })
  } else {
    isDelayStatus.value =
      task?.invalid === 1 && task?.isDelayReason === 1 && task?.status === 1 && dayjs().isAfter(task?.forcastTime)
    notificationModel.flag = true
    notificationModel.title = ProcessModalTilte.submit
    notificationModel.data = paramsWrapper(data)
  }
}

const handleNotificationMilepostTask = async (data: string[]) => {
  try {
    const res = await submitMission({ ...notificationModel.data, ...data })
    if (res.code == 200) {
      message.success(i18n.t('提交成功'))
      await refresh()
      initToDoList && (await initToDoList())
      notificationModel.flag = false
    } else {
      message.error(res.msg)
    }
  } finally {
    notificationModel.loading = false
  }
}

const editTaskModal = (item: ITask) => {
  // 取消快速编辑
  cancel(item.id)
  milestoneOperate(EmitType.editTask, item)
}
const deleteTaskModal = (item: ITask) => {
  cancel(item.id)
  milestoneOperate(EmitType.delTask, item)
}
const revokeTaskModal = (item: ITask) => {
  cancel(item.id)
  milestoneOperate(EmitType.revokeTask, item)
}
const transformRoleText = (text: number | string) => {
  return transformFieldIdToLabel(props.role as Array<any>, 'roleCode', 'roleName', text)
}
const onTaskDraggableEnd = () => {
  const sortArr = taskList.value.map((item, index) => ({ id: item.id, sort: index + 1 })) as unknown as SortITask
  resortTask(sortArr).then(res => {
    if (res.code == 200) {
      message.success(res.msg)
    }
  })
}

watch(
  () => userData.value,
  () => (superviserUserList.value = userData.value),
  { immediate: true }
)

watch(
  () => props.currtMilepostChildren,
  () => filterTask(props.formQueryData)
)

watch(props.formQueryData, newValue => filterTask(newValue))
</script>

<style lang="scss" scoped>
.tiling-body {
  .ghost {
    border: solid 1px green !important;
  }
  .chosenClass {
    background-color: #eee;
    opacity: 1;
  }
  .fallbackClass {
    background-color: aquamarine;
  }
  transition: all 0.5s;
  .task-list {
    .form-cell {
      margin-bottom: 16px;
      .review-suggestion {
        padding-bottom: 16px;
        .review-viewer-tiling {
          display: inline-block;
          :deep(.ql-container-tiling) {
            .ql-editor-tiling {
              font-size: 12px;
              color: #333;
            }
            .fs-image {
              display: none;
            }
          }
        }
      }
      .suggestion-popover {
        display: inline-block;
        width: 600px;
      }
      :deep(.fs-collapse-content) {
        background-color: #fff;
        border: none;
        &:hover {
          background-color: #fbfdff;
        }
      }
      .collapse-header-left-icon {
        color: #378eef;
        font-size: 24px;
      }
      :deep(.fs-collapse) {
        border: 1px dashed #f3f3f3;
      }
      :deep(.fs-collapse-item) {
        background-color: #f5f7fa;
        border: none;
        .fs-collapse-header {
          padding: 4px 16px 4px 16px;
        }
      }
      :deep(.custom-header-right) {
        color: #999;
      }
      :deep(.fs-collapse-content-box) {
        padding: 0;
      }
      .collapse-box {
        padding: 16px 16px 0 16px;
      }
      .collapse-box:empty {
        padding: 0;
      }
      .tiling-more-edit-parent {
        display: inline-block;
        width: 18px;
        height: 18px;
        margin-top: 8px;
        :deep(.tiling-more-edit-overlay-class) {
          width: 104px;
          height: 74px;
          .fs-popover-inner {
            padding: 0;
          }
          .fs-popover-inner-content {
            padding: 4px;
            .more-item {
              padding: 7px 12px 7px 12px;
              white-space: nowrap;
            }
            .more-item:hover {
              border-radius: 3px;
              background-color: #f1f4f8;
            }
          }
        }
      }
      :deep(.customize-header) {
        display: flex;
        align-items: center;
        justify-content: center;
        .tiling-more-overlay-class {
          min-width: 400px;
          max-width: 800px;
          .more-container {
            .ul-more {
              list-style: none;
              margin: 0;
              padding: 0;
              .more-left {
                margin-right: 16px;
                min-width: 60px;
                float: left;
              }
              li {
                margin-bottom: 8px;
                .more-right-file {
                  display: inline-block;
                }
              }
              li:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
        .im {
          width: 20px;
          border-radius: 16px;
          border: 1px solid #eeeeee;
        }
        .role-name {
          vertical-align: middle;
        }
        .user-hover {
          background-color: #f5f7fa;
          display: inline-block;
          vertical-align: middle;
          height: 32px;
          line-height: 32px;
          text-align: center;
          padding-left: 8px;
          padding-right: 8px;
        }
        .user-height {
          display: inline-block;
          vertical-align: middle;
          height: 32px;
          line-height: 32px;
          text-align: center;
        }
        .user-hover:hover {
          background-color: #fbfdff;
          border-radius: 3px;
        }
        .user-click {
          background-color: #f5f7fa;
          display: inline-block;
          vertical-align: middle;
          height: 32px;
          line-height: 32px;
          text-align: center;
          .im {
            width: 20px;
          }
        }
      }
    }
  }
  .add-task:hover {
    color: #5fa4f2;
  }
}
.blue-link {
  color: #378eef;
}
.marginR8 {
  margin-right: 8px;
}
.center-middle {
  vertical-align: middle;
}
.reject {
  color: #f04141;
  background-color: rgba(240, 65, 65, 0.1);
  padding: 1px;
}
.marginL20 {
  margin-left: 20px;
}
.font333 {
  color: #333333;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
}
.fontWeight500 {
  font-weight: 500;
}
.in-block {
  display: inline-block;
}
.align-icon {
  padding: 0 4px;
  border-radius: 2px;
  line-height: 18px;
}
.marginR4 {
  margin-right: 4px;
}
</style>
