import { IResData, SuccessIRes } from '@/types/handle'
import { ProcessListParams, ProcessListResponse } from '@/types/processBoard'
import { request } from '@/utils'

export function getTaskSituationList(data: ProcessListParams): Promise<ProcessListResponse> {
  return request.post('/api/baseReportForms/taskSituation', data)
}

export function getTaskSituationExport(data: ProcessListParams): Promise<any> {
  return request.post('/api/baseReportForms/download', data, { responseType: 'blob' })
}

export function getProcessSituation(data: ProcessListParams): Promise<SuccessIRes> {
  return request.post('/api/baseReportForms/processSituation', data)
}

export function getTaskSituationChart(data: ProcessListParams): Promise<SuccessIRes> {
  return request.post('/api/baseReportForms/taskSituationChart', data)
}

export function getTaskDetailChart(data: ProcessListParams): Promise<SuccessIRes> {
  return request.post('/api/baseReportForms/taskDetailIndex', data)
}

export function getTaskDetailExport(data: ProcessListParams): Promise<any> {
  return request.post('/api/baseReportForms/downloadTaskDetailIndex', data, { responseType: 'blob' })
}

export function getProcessDetailChart(data: ProcessListParams): Promise<SuccessIRes> {
  return request.post('/api/baseReportForms/projectDetailIndex', data)
}

export function getProcessDetailExport(data: ProcessListParams): Promise<any> {
  return request.post('/api/baseReportForms/downloadProjectDetailIndex', data, { responseType: 'blob' })
}
