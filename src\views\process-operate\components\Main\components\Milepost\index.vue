<template>
  <div class="process-operate-milepost">
    <MilepostHeader />
    <div class="milepost-container">
      <BaseInfo />
      <component v-if="componentUrl" :is="custComponent" />
      <TaskListView v-if="currentView === ProcessViewType.table" @change-view="changeView" />
      <TilingListContainer
        v-show="currentView == ProcessViewType.tiling"
        :currt-milepost="currtMilepost"
        :role="processRoleInfo"
        :data="(taskModel.data as ITask)"
        @change-view="changeView"
        @submit="handleTaskSubmit"
      />
    </div>
    <TaskModal
      v-model="taskModel.flag"
      :title="(taskModel['title'] as typeof TaskModalTilte)"
      :data="(taskModel.data as ITask)"
      :role="processRoleInfo"
      @submit="handleTaskSubmit"
    />
    <NotificationModal
      v-model:value="notificationModel.flag"
      v-model:loading="notificationModel.loading"
      :title="(notificationModel.title as string)"
      :role="(processRoleInfo as IProcessRoleAndUser[])"
      defaultRoleKey="taskId"
      :defaultRoleValue="notificationModel.data.id"
      :isDelayStatus="isDelayStatus"
      @submit="handleNotificationMilepostTask"
    />
  </div>
</template>

<script setup lang="ts">
import {
  Ref,
  inject,
  ref,
  createVNode,
  reactive,
  provide,
  ComputedRef,
  onBeforeMount,
  markRaw,
  defineAsyncComponent,
  computed,
} from 'vue'
import { IProcess, ITask, PoolParams } from '@/types/handle'
import { IProcessRoleAndUser } from '@/types/request'
import { messageInstance as message, FModal } from '@fs/smart-design'
import { EmitType, ProcessViewType, TaskModalTilte, ProcessModalTilte } from '@/views/process-detail/config'
import { addTask, batchEditTask, editTask, removeTask, submitTaskPool, editTaskTime } from '@/api/handle'
import MilepostHeader from './components/MilepostHeader/index.vue'
import BaseInfo from './components/BaseInfo/index.vue'
import TaskListView from './components/TaskListView/index.vue'
import TilingListContainer from './components/tiling/index.vue'
import TaskModal from '@/views/process-operate/components/TaskModal/index.vue'
import { deepClone, useI18n } from '@/utils'
import { submitMission, saveMission, rollingBack } from '@/api/handle'
import { IModel } from '@/types/common'
import NotificationModal from '@/components/NotificationModal/index.vue'
import { useStore } from 'vuex'

const store = useStore()
const i18n = useI18n()
const formRenderRef = ref<any>()
const notificationModel = reactive<IModel<IProcess>>({ flag: false, data: {} as IProcess, title: '', loading: false })
const processId = inject<number>('processId') // 流程 id
const processInfo = inject<ComputedRef<IProcess[]>>('processInfo') // 流程信息
const processRoleInfo = inject<IProcessRoleAndUser[]>('processRoleInfo') // 流程角色信息
const paramsWrapper = inject('paramsWrapper') as (data: any) => any
const initToDoList = inject('initToDoList') as () => void
const initAddTaskList = inject('initAddTaskList') as () => void
const currtMilepost = inject<Ref<IProcess>>('currtMilepost')
const getProcessInfo = inject('getProcessInfo') as (callBack?: any) => void
const taskModel = reactive<IModel<ITask | ITask[] | IProcess>>({ flag: false, data: {} as ITask, title: '' })
const currentView = ref<ProcessViewType>(ProcessViewType.tiling) // 默认表格视图
const setCurrtMilepost = inject('setCurrtMilepost') as (data: IProcess) => void // 设置当前里程碑信息
const setLayoutView = inject('setLayoutView') as (data: ProcessViewType) => void // 设置当前里程碑信息
const changeView = async () => {
  const layoutView = currentView.value == ProcessViewType.table ? ProcessViewType.tiling : ProcessViewType.table
  setLayoutView(layoutView)
  await getProcessInfo(async (data: IProcess[]) => {
    await initAddTaskList()
    const process =
      data.find(item => item.id === currtMilepost?.value?.id) ||
      ((data.find(item => [2, 4].includes(item.status)) || data.at(-1)) as IProcess)
    setCurrtMilepost(process)
  })
  store.commit('local/SET_LOCAL_USER_DATA', { processDetailLayoutView: layoutView })
  currentView.value = layoutView
}
const componentUrl = computed(() => currtMilepost?.value?.nodeConfig?.formComponent || undefined)
const custComponent = markRaw(
  defineAsyncComponent(() => import(`@/views/process-operate/components/CustomComponents/${componentUrl.value}.vue`))
)
onBeforeMount(() => {
  const localUserCache = store.getters['local/getLocalUserData']
  if (localUserCache !== undefined && localUserCache !== null && localUserCache.processDetailLayoutView !== undefined) {
    currentView.value = localUserCache.processDetailLayoutView
  }
})
const handleTaskSubmit = async (data: ITask, callBack?: () => void) => {
  let action
  let formData = deepClone(data)
  const currTask = taskModel.data as ITask
  formData.milepostId = currTask.milepostId as number
  if (taskModel.title === TaskModalTilte.edit) {
    action = editTask
    formData.id = currTask.id
    formData.preTask = currTask.childTaskFlag ? (currTask.preTask as number) : formData.preTask
  } else if (taskModel.title === TaskModalTilte.batchEdit) {
    action = batchEditTask
    formData = {
      isSys: data.isSys,
      superviserRoleCode: data.superviserRoleCode,
      superviser: data.superviser,
      approverRoleCode: data.approverRoleCode,
      approver: data.approver,
      forcastTime: data.forcastTime,
      instanceId: processId,
      taskIdList: (currTask as unknown as ITask[]).map(item => item.id),
    } as unknown as ITask
  } else {
    action = addTask
    formData.preTask = formData.preTask ?? 0
    formData.milepostId = (currTask as unknown as IProcess).id // 新增的时候 data 为 里程碑数据
  }
  try {
    const { code } = await action(paramsWrapper(formData))
    if (code !== 200) return
    await refresh()
    initToDoList && (await initToDoList())
    message.success(i18n.t('操作成功'))
    callBack && callBack()
  } finally {
    taskModel.flag = false
  }
}
const isDelayStatus = ref<boolean>(false)
// 操作方法
const milestoneOperate = {
  // 任务
  [EmitType.createTask]: (data: IProcess) => {
    taskModel.data = data
  },
  [EmitType.editTask]: (data: ITask) => {
    taskModel.data = data
    taskModel.flag = true
    taskModel.title = TaskModalTilte.edit
  },
  [EmitType.batchEditTask]: (data: ITask[]) => {
    taskModel.data = data
    taskModel.flag = true
    taskModel.title = TaskModalTilte.batchEdit
  },
  [EmitType.revokeTask]: (data: ITask) => {
    rollingBack({ id: data.id }).then(async res => {
      if (res.code == 200) {
        message.success(i18n.t('回滚成功'))
        await refresh()
        initToDoList && (await initToDoList())
      } else {
        message.error(res.msg)
      }
    })
  },
  [EmitType.handleTask]: (data: Record<string, unknown>) => {
    if (data.approverUuid) {
      delete data.approverUuid
      submitMission(paramsWrapper(data)).then(async res => {
        if (res.code == 200) {
          message.success(i18n.t('提交成功'))
          await refresh()
          initToDoList && (await initToDoList())
        } else {
          message.error(res.msg)
        }
      })
    } else {
      isDelayStatus.value = data.isDelayStatus as boolean
      delete data.isDelayStatus
      notificationModel.flag = true
      notificationModel.title = ProcessModalTilte.submit
      notificationModel.data = paramsWrapper(data)
    }
  },
  [EmitType.saveTask]: (data: Record<string, unknown>) => {
    saveMission(paramsWrapper(data)).then(async res => {
      if (res.code == 200) {
        message.success(i18n.t('保存成功'))
        await refresh()
        initToDoList && (await initToDoList())
      } else {
        message.error(res.msg)
      }
    })
  },
  [EmitType.delTask]: (data: ITask) => {
    FModal.confirm({
      title: i18n.t('确认删除当前子节点吗？'),
      content: i18n.t('删除后不可恢复，请谨慎操作！'),
      okText: i18n.t('确认'),
      cancelText: i18n.t('取消'),
      icon: createVNode(
        'span',
        { class: 'icon iconfont iconicon_tishi', style: 'float: left;margin-right: 14px;color: #FA8F23;' },
        null
      ),
      onOk: async () => {
        await removeTask(paramsWrapper({ taskId: data.id }))
        message.success(i18n.t('删除成功'))
        await refresh()
        initToDoList && (await initToDoList())
      },
    })
  },
  [EmitType.batchDelTask]: (data: { milepost: IProcess; tasks: ITask[] }) => {
    const { milepost, tasks = [] } = data
    console.log(milepost, tasks, i18n.t('批量删除'))
  },
  [EmitType.createPoolTask]: (data: PoolParams) => {
    submitTaskPool(data).then(async res => {
      if (res.code == 200 || res.success) {
        message.success(i18n.t('提交任务池表单成功'))
        await refresh()
        initToDoList && (await initToDoList())
      } else {
        message.error(res.msg)
      }
    })
  },
}

const handleNotificationMilepostTask = async (data: string[]) => {
  try {
    delete notificationModel.data.approverUuid
    const res = await submitMission({ ...notificationModel.data, ...data })
    if (res.code == 200) {
      message.success(i18n.t('提交成功'))
      await refresh()
      initToDoList && (await initToDoList())
      notificationModel.flag = false
    } else {
      message.error(res.msg)
    }
  } finally {
    notificationModel.loading = false
  }
}

const quickEditHandleTaskTime = async (data: ITask, callback: (id: string | number) => void) => {
  let formData = {
    forcastTime: data.forcastTime,
    taskStartTime: data.taskStartTime,
    contentData: JSON.parse(data.contentData as string) ?? {},
    taskId: data.id,
  }
  const { code } = await editTaskTime(formData)
  if (code !== 200) return
  await refresh()
  initToDoList && (await initToDoList())
  callback(formData.taskId)
  message.success(i18n.t('操作成功'))
}

const quickEditHandleTaskSubmit = async (data: ITask, callback: (id: string | number) => void) => {
  let formData = {
    taskName: data.taskName,
    superviser: data.superviserUuid,
    superviserRoleCode: data.superviserRoleCode,
    approver: data.approverUuid,
    approverRoleCode: data.approverRoleCode,
    forcastTime: data.forcastTime,
    isSys: data.isSys,
    contentData: JSON.parse(data.contentData as string) ?? {},
    preTask: data.preTask,
    taskType: data.taskType,
    id: data.id,
    milepostId: data.milepostId,
  }
  const { code } = await editTask(paramsWrapper(formData))
  if (code !== 200) return
  await refresh()
  initToDoList && (await initToDoList())
  callback(formData.id)
  message.success(i18n.t('操作成功'))
}
const refresh = async () => {
  await getProcessInfo((processInfo: IProcess[]) => {
    const currMilepost = processInfo.find(item => item.id === currtMilepost?.value?.id)
    currMilepost && setCurrtMilepost(currMilepost)
  })
}

provide('formRenderRef', formRenderRef)
provide('setFormRenderRef', (ref: any) => (formRenderRef.value = ref))
provide('refresh', refresh)
provide('milestoneOperate', (key: keyof typeof milestoneOperate, data: any) => milestoneOperate[key](data)) // 新增弹框
provide('quickEditHandleTaskSubmit', quickEditHandleTaskSubmit) // 设置当前激活的里程碑
provide('quickEditHandleTaskTime', quickEditHandleTaskTime) // 修改预计完成时间
</script>

<style scoped lang="scss">
.process-operate-milepost {
  width: 100%;
  border-radius: 4px;
  box-sizing: border-box;
  box-shadow: 0px 2px 8px 0px #58626e14;
  background-color: white;

  .milepost-container {
    padding: 16px 24px 24px 24px;
  }
}
.milepost-container {
  :deep(.fs-collapse) {
    .fs-collapse-content-box {
      padding-bottom: 0;
    }
  }
}
</style>
