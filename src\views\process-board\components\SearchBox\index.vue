<template>
  <div class="search-box-container">
    <div class="left search-config-box">
      <template v-for="item of search.options" :key="item.componentValueKey">
        <component :is="item.componentName" v-bind="item.componentAttrs" v-model:value="item.componentValue" />
      </template>
    </div>
    <div class="right">
      <FButton class="export-btn" @click="onClearSearchDataFn">
        {{ i18n.t('重置') }}
      </FButton>
      <FButton class="export-btn" type="primary" @click="onGetSearchDataFn">
        {{ i18n.t('查询') }}
      </FButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Search } from '@/views/message-template/components/SearchContent/search'
import { onMounted, computed, ref } from 'vue'
import { useStore } from 'vuex'
import { useI18n, transformDate } from '@/utils'
import { message } from '@fs/smart-design'
import { GetTagAndNode, getDepartment, getSearchConfigById } from '@/api'
const i18n = useI18n()
const emits = defineEmits(['onGetSearchData'])
const store = useStore()
const search = new Search()
const processLists = ref([])
const roleLists = ref([])
const setSearchConfig = async (processConfigId: number) => {
  const clearKeys = Object.values(search.options)
    .filter(item => item.hasProcessConfigId && item.hasProcessConfigId !== processConfigId)
    .map(item => item.componentValueKey)
  search.deteleOptions(clearKeys)
  const boardSearchWhite = new Function(`return ${process.env.VUE_APP_BOARD_SEARCH_WHITE}`)()
  if (boardSearchWhite.includes(processConfigId)) {
    const res = await getSearchConfigById(processConfigId)
    if (res.code === 200 && res?.data.length) {
      const searchConfigList = res.data
        .filter(item => item.moduleType === 'SELECT')
        .map(item => {
          return {
            componentName: 'FSelect',
            componentValueKey: item.field,
            hasProcessConfigId: processConfigId,
            componentAttrs: {
              class: 'width120 marginR12 marginB12',
              pressLine: computed(() => item.name),
              placeholder: computed(() => i18n.t('请选择')),
              showSearch: true,
              optionFilterProp: 'label',
              allowClear: true,
              options: computed(() => item.valueList),
            },
            setComponentValueFormat: (data: any) => {
              search.setOptions(`${item.field}.componentValue`, (data || {})?.componentValue || undefined)
            },
          }
        })
      search.addOptions(searchConfigList)
    }
  }
}
const handleTree = (node: any, users: any = []) => {
  return node.reduce((users: any, cur: any) => {
    if (cur.departmentChildrens) {
      const data = JSON.parse(JSON.stringify(cur))
      data.departmentChildrens = JSON.parse(JSON.stringify(data.uuidAndNames || []))
      !((cur.uuidAndNames || []).length + (cur.departmentChildrens || []).length) && (data.disabled = true)
      users.push(data)
      cur.departmentChildrens.length && handleTree(cur.departmentChildrens, data.departmentChildrens)
    }
    return users
  }, users)
}

const checkChildren = (node: any, users: any = []) => {
  return node.reduce((users: any, cur: any) => {
    if (cur.uuid && cur.name && Object.keys(cur).length === 2) {
      users.push(cur.uuid)
    } else if (cur.departmentChildrens && cur.departmentChildrens.length) {
      checkChildren(cur.departmentChildrens, users)
    }
    return users
  }, users)
}
const filterOption = (input: string, option: any) => option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
const configList = [
  {
    componentName: 'FSelect',
    componentValueKey: 'processConfigId',
    componentAttrs: {
      class: 'width120 marginR12 marginB12',
      pressLine: computed(() => i18n.t('流程类型')),
      placeholder: computed(() => i18n.t('请选择')),
      fieldNames: { label: 'processName', value: 'id' },
      showSearch: true,
      optionFilterProp: 'processName',
      allowClear: true,
      options: computed(() => processLists?.value),
      onChange: (value: any, option: any) => {
        const list = (option?.dictionarys ?? []).map((tag: any) => ({
          ...tag,
          label: tag.name,
          value: `$.${tag.field}:${tag.value}`,
        }))
        search.setOptions('tag.componentValue', undefined)
        search.setOptions('tag.componentAttrs.options', list)
        search.setOptions('processConfigId.componentArgsValue', {
          typeOptions: list,
        })
        setSearchConfig(value)
      },
    },
    setComponentValueFormat: async (data: any) => {
      search.setOptions('processConfigId.componentValue', (data || {})?.componentValue || undefined)
      search.setOptions('processConfigId.componentArgsValue', (data || {})?.componentArgsValue || undefined)
      search.setOptions('tag.componentAttrs.options', (data || {})?.componentArgsValue?.typeOptions || [])
    },
    clearComponentValueFn: () => {
      search.setOptions('tag.componentAttrs.options', [])
      setSearchConfig(-1)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'tag',
    componentAttrs: {
      class: 'width120 marginR12 marginB12',
      pressLine: computed(() => i18n.t('需求类型')),
      placeholder: computed(() => i18n.t('请选择')),
      showSearch: true,
      optionFilterProp: 'label',
      allowClear: true,
      options: [],
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('tag.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FCascader',
    componentValueKey: 'projectUuidList',
    componentArgsValue: {},
    componentAttrs: {
      class: 'width120 marginR12 marginB12',
      multiple: true,
      showArrow: true,
      placeholder: i18n.t('请选择'),
      pressLine: i18n.t('项目成员'),
      fieldNames: { label: 'name', value: 'uuid', children: 'departmentChildrens' },
      maxTagCount: 'responsive',
      showSearch: { filterOption },
      showCheckedStrategy: 'SHOW_CHILD',
      allowClear: true,
      options: computed(() => roleLists?.value),
      onChange: (value: any, selectedOptions: any) => {
        const selectLists =
          selectedOptions.map((item: any) => {
            return item[item.length - 1]
          }) || []
        search.setOptions(
          'projectUuidList.componentArgsValue',
          Object.assign(search.getOptions('projectUuidList.componentArgsValue'), { selectLists })
        )
      },
    },
    getComponentValueFormat: (value: any, argsValue: any) => {
      const projectUuidList = checkChildren(argsValue?.selectLists ?? [])
      if (projectUuidList.length) {
        return { projectUuidList: projectUuidList }
      } else {
        search.setOptions('projectUuidList.componentValue', undefined)
        return undefined
      }
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('projectUuidList.componentValue', (data || {})?.componentValue || undefined)
      search.setOptions('projectUuidList.componentArgsValue', (data || {})?.componentArgsValue || undefined)
    },
  },
  {
    componentName: 'FSelect',
    componentValueKey: 'status',
    componentAttrs: {
      class: 'width120 marginR12 marginB12',
      pressLine: computed(() => i18n.t('项目状态')),
      placeholder: computed(() => i18n.t('请选择')),
      showSearch: true,
      optionFilterProp: 'label',
      allowClear: true,
      options: computed(() => [
        { label: i18n.t('进行中'), value: 0 },
        { label: i18n.t('已完成'), value: 1 },
        { label: i18n.t('已办结'), value: 2 },
      ]),
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('status.componentValue', (data || {})?.componentValue || undefined)
    },
  },
  {
    componentName: 'FRangePicker',
    componentValueKey: 'time',
    componentAttrs: {
      class: 'width240 marginR12 marginB12',
      pressLine: computed(() => i18n.t('创建时间')),
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    getComponentValueFormat: (value: any) => {
      if (!value || value.length !== 2) return undefined
      return {
        startTime: transformDate(value[0], 'YYYY-MM-DD') + ' 00:00:00',
        endTime: transformDate(value[1], 'YYYY-MM-DD') + ' 23:59:59',
      }
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions(
        'time.componentValue',
        ((data || {})?.componentValue && [data.componentValue[0], data.componentValue[1]]) || undefined
      )
    },
  },
  {
    componentName: 'FInput',
    componentValueKey: 'queryInput',
    componentAttrs: {
      class: 'width240 marginR12 marginB12',
      pressLine: computed(() => i18n.t('快速搜索')),
      placeholder: computed(() => i18n.t('项目名称/项目编号')),
      allowClear: true,
      type: 'search-clear',
    },
    setComponentValueFormat: (data: any) => {
      search.setOptions('queryInput.componentValue', (data || {})?.componentValue || undefined)
    },
  },
]
search.initOptions(configList)

const getProcessLists = async () => {
  const res = await GetTagAndNode()
  let data: any = []
  if (res.code === 200) {
    data = res.data || []
  } else {
    message.warn(res.msg)
  }
  processLists.value = data
}

const getRoleLists = async () => {
  const res = await getDepartment()
  if (res.code !== 200) throw new Error(res.msg)
  let data: any = []
  data = handleTree(res.data)
  roleLists.value = data
}

const onGetSearchDataFn = (data: any = null) => {
  emits('onGetSearchData', search.getParams())
  store.commit('local/SET_LOCAL_BOARD_SEARCH_DATA', search.getCacheSearch())
}

const onClearSearchDataFn = () => {
  search.clear()
  onGetSearchDataFn()
}

onMounted(async () => {
  requestIdleCallback(getProcessLists)
  requestIdleCallback(getRoleLists)
  const cachData = store.getters['local/getLocalBoardUuidData'] || {}
  cachData.processConfigId && (await setSearchConfig((cachData.processConfigId || {})?.componentValue || undefined))
  search.setDefaultSearch(cachData)
  onGetSearchDataFn()
})
</script>

<style lang="scss" scoped>
.search-box-container {
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
  padding: 24px 24px 12px 24px;
  background: #ffffff;
  box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
  border-radius: 4px;
  color: #999;
  .search-config-box {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
  .right {
    display: flex;
    .export-btn {
      width: 80px;
      margin-left: 12px;
    }
  }
  :deep(.fs-select-selection-placeholder) {
    color: #bbb !important;
  }
}
</style>
