// 吸顶指令
export const sticky = {
  mounted(el, binding) {
    const { container, zIndex = 1, type = 'fixed', stickyClass = 'fs-sticky-directive-el' } = binding.value
    const scrollContainer = document.querySelector(container) || window
    let isSticky = false

    const handleScroll = () => {
      const scrollY = scrollContainer === window ? window.scrollY : scrollContainer.scrollTop
      if (scrollY >= binding.value.offset && !isSticky) {
        el.style.position = type
        el.style.top = (binding.value.top ?? binding.value.offset) + 'px'
        el.style.zIndex = zIndex
        isSticky = true
        el.classList.add(stickyClass)
      } else if (scrollY < binding.value.offset && isSticky) {
        el.style.position = 'static'
        isSticky = false
        el.classList.remove(stickyClass)
      }
    }

    scrollContainer.addEventListener('scroll', handleScroll)

    // 在指令元素销毁时移除滚动事件监听器
    el.__handleScroll = handleScroll
  },
  beforeUnmount(el, binding) {
    const { container } = binding.value
    const scrollContainer = document.querySelector(container) || window

    // 移除滚动事件监听器
    scrollContainer.removeEventListener('scroll', el.__handleScroll)
  },
}
