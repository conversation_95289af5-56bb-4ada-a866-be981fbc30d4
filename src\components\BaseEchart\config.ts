import * as Echarts from 'echarts/core'
import {
  DatasetComponent,
  DatasetComponentOption,
  TooltipComponent,
  TooltipComponentOption,
  GridComponent,
  GridComponentOption,
  LegendComponent,
  LegendComponentOption,
  TitleComponent,
  TimelineComponentOption,
  DataZoomComponent,
  DataZoomComponentOption,
} from 'echarts/components'
import { <PERSON><PERSON><PERSON>, Pie<PERSON>hart, PieSeriesOption, BarSeriesOption, LineChart, LineSeriesOption } from 'echarts/charts'
import { LabelLayout } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'

Echarts.use([
  DatasetComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>vasRenderer,
  LabelLayout,
  DataZoomComponent,
])

export type EChartsOption = Echarts.ComposeOption<
  | DatasetComponentOption
  | TooltipComponentOption
  | GridComponentOption
  | LegendComponentOption
  | TimelineComponentOption
  | BarSeriesOption
  | PieSeriesOption
  | LineSeriesOption
  | DataZoomComponentOption
>

export default Echarts
