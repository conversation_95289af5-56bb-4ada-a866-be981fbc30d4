<template>
  <div class="excution-list-container shadow-radius">
    <div class="content-box">
      <div class="card-btn">
        <div class="fei-su-title">{{ i18n.t('流程自动化列表') }}</div>
        <FButton type="primary" class="add-button" @click="addTrigger">
          <i class="iconxinzeng iconfont marginR5 fontSize14"></i>{{ i18n.t('添加触发器') }}
        </FButton>
      </div>
      <FTable
        :data-source="dataList"
        :scroll="{ x: 'min-content' }"
        :columns="columns"
        :pagination="false"
        class="table-container"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'processConfigId'">{{
            transformProcessText(record.processConfigId)
          }}</template>
          <template v-if="column.key === 'triggerName'">{{ record.triggerName }}</template>
          <template v-if="column.key === 'triggerType'">{{ transformTrigger(record.triggerType) }}</template>
          <template v-if="column.key === 'status'">
            <span v-if="record.status == 1" class="enable">{{ i18n.t('启用') }}</span>
            <span v-if="record.status == 0" class="disable">{{ i18n.t('禁用') }}</span>
          </template>
          <template v-if="column.key === 'isSyn'">
            <FTag
              size="small"
              :color="(isSynList.find(item => item.value === record.isSyn) ?? {})?.status ?? 'default'"
              >{{ (isSynList.find(item => item.value === record.isSyn) ?? {})?.label ?? '--' }}</FTag
            >
          </template>
          <template v-if="column.key === 'eventNum'">{{ record.eventNum ?? '--' }}</template>
          <template v-if="column.key === 'remarks'">
            <FPopover trigger="hover" word-wrap overlay-class-name="trigger-list-popover" placement="bottomLeft">
              <span class="content-remark">
                {{ record.remarks ? record.remarks : '--' }}
              </span>
              <template #content>
                <span class="remark-popover">
                  {{ record.remarks }}
                </span>
              </template>
            </FPopover>
          </template>
          <template v-if="column.key === 'createdTime'">
            {{ dayjs(record.createdTime).format('YYYY-MM-DD HH:mm:ss') }}
            <br />
            {{ record.createdUserName }}
          </template>
          <template v-if="column.key === 'updateTime'">
            <div v-if="record.updateTime">
              {{ dayjs(record.updateTime).format('YYYY-MM-DD HH:mm:ss') }}
              <br />
              {{ record.updateUserName }}
            </div>
            <div v-else>--</div>
          </template>
          <template v-if="column.key === 'action'">
            <FTooltip :title="i18n.t('编辑触发器')" placement="top">
              <span
                class="iconfont-hover iconfont cursor color4677C7 icontubiao_xietongbianji marginR12"
                @click="editTrigger(record)"
              >
              </span>
            </FTooltip>
            <FTooltip :title="i18n.t('拷贝触发器')" placement="top">
              <span
                class="iconfont-hover iconfont cursor color4677C7 icontubiao_fuzhi marginR12"
                @click="copyTriggerFn(record)"
              >
              </span>
            </FTooltip>
            <FTooltip :title="i18n.t('测试触发器执行')" placement="top" v-if="record.triggerType === 1">
              <span
                class="iconfont-hover iconfont cursor color4677C7 icontubiao_ceshi marginR12"
                @click="testTriggerFn(record)"
              >
              </span>
            </FTooltip>
            <FPopconfirm
              :ok-text="i18n.t('确定')"
              :cancel-text="i18n.t('取消')"
              @confirm="deleteExecute(record)"
              placement="topRight"
              arrow-point-at-center
              :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode?.parentNode?.parentNode?.parentNode}"
            >
              <template #title>
                <p class="color333 fontWeight600 fontSize14 marginB5">{{ i18n.t('确定删除吗？') }}</p>
                <div class="color666 fontSize12">{{ i18n.t('删除后不可恢复,请谨慎操作') }}</div>
              </template>

              <FTooltip :title="i18n.t('删除')" placement="top">
                <span class="iconfont icon iconfont-hover cursor color4677C7">&#xe7a4;</span>
              </FTooltip>
            </FPopconfirm>
          </template>
        </template>
        <template #emptyText>
          <img :src="noImgUrl" />
          <p class="colorBBB fontSize12 fs-tip">{{ i18n.t('暂无流程类型~') }}~</p>
        </template>
      </FTable>
      <div class="fei-su-pagination">
        <span class="fontSize12">{{ i18n.t('共') }} {{ paging.total }} {{ i18n.t('条') }}</span>
        <FPagination
          v-model:current="paging.pageNum"
          @change="onChange"
          v-model:page-size="paging.pageSize"
          :total="paging.total"
          show-size-changer
        />
      </div>
    </div>
    <CopyTriggerModal v-model:visible="copyTriggerVisible" :id="copyTriggerId" @fetchData="fetchData" />
    <TestTriggerModal v-model:visible="testTriggerVisible" :id="testTriggerId" @fetchData="fetchData" />
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue'
import noImgUrl from '@/assets/images/no-data.png'
import dayjs from 'dayjs'
import { TriggerData, IPage, event, processTye } from '@/types/excutionListModel'
import { TableColumnsType } from '@fs/smart-design/dist/ant-design-vue_es'
import { queryTriggerList, deleteTrigger, testTrigger } from '@/api/automotion'
import { transformFieldIdToLabel, transformTrigger } from '@/utils/filter'
import { useRouter } from 'vue-router'
import { messageInstance as message } from '@fs/smart-design'
import CopyTriggerModal from './CopyTriggerModal.vue'
import { useI18n } from '@/utils'
import { useStore } from 'vuex'
import TestTriggerModal from './TestTriggerModal.vue'
const dataList = ref<TriggerData<event>[]>([])
const paging = ref<IPage>({ pageNum: 1, pageSize: 10, total: 0 })
const i18n = useI18n()
const columns = computed(() => [
  { title: i18n.t('流程类型'), dataIndex: 'processConfigId', key: 'processConfigId', width: 100 },
  { title: i18n.t('触发器名称'), dataIndex: 'triggerName', key: 'triggerName', width: 100 },
  { title: i18n.t('触发器类型'), dataIndex: 'triggerType', key: 'triggerType', width: 100 },
  { title: i18n.t('状态'), dataIndex: 'status', key: 'status' },
  { title: i18n.t('是否同步'), dataIndex: 'isSyn', key: 'isSyn', width: 100 },
  { title: i18n.t('事件数量'), dataIndex: 'eventNum', key: 'eventNum' },
  { title: i18n.t('备注说明'), dataIndex: 'remarks', key: 'remarks', width: 180 },
  { title: i18n.t('创建时间/创建人'), dataIndex: 'createdTime', key: 'createdTime', width: 180 },
  { title: i18n.t('修改时间/修改人'), dataIndex: 'updateTime', key: 'updateTime', width: 180 },
  { title: i18n.t('操作'), dataIndex: 'action', key: 'action', fixed: 'right', width: 168 },
])
interface IProps {
  searchParams: object
  processTypeList: processTye[]
}
const props = defineProps<IProps>()
const router = useRouter()
const store = useStore()
const copyTriggerVisible = ref(false)
const copyTriggerId = ref()
const testTriggerVisible = ref(false)
const testTriggerId = ref()
const isSynList = ref([
  { value: 0, label: i18n.t('异步执行'), status: 'success' },
  { value: 1, label: i18n.t('同步执行'), status: 'warning' },
])
onMounted(() => {
  setDefaultSearch()
  // fetchData()
})
watch(
  () => props.searchParams,
  () => {
    fetchData()
  }
)

const copyTriggerFn = record => {
  copyTriggerVisible.value = true
  copyTriggerId.value = record.id
}

const setCache = data => {
  const cacheValue = { ...data }
  store.commit('local/SET_LOCAL_AUTOMATION_SEARCH_DATA', { localAutomationSearchData: cacheValue })
}

const setDefaultSearch = () => {
  const cache = store.getters['local/getLocalAutomationData']
  if (cache !== undefined && cache !== null) {
    const localSearchData = cache?.localAutomationSearchData ?? {}
    localSearchData.pageNum && (paging.value.pageNum = localSearchData.pageNum)
    localSearchData.pageSize && (paging.value.pageSize = localSearchData.pageSize)
  }
}

const fetchData = async () => {
  const parmas = {
    pageNum: paging.value.pageNum,
    pageSize: paging.value.pageSize,
    ...props.searchParams,
  }
  setCache(parmas)
  const res = await queryTriggerList(parmas)
  if (res.code == 200) {
    dataList.value = res.data.list
    paging.value.total = res.data.totalCount
  }
}
const onChange = (current: number, pageSize: number) => {
  paging.value.pageSize = pageSize
  paging.value.pageNum = current
  fetchData()
}
const addTrigger = () => {
  router.push({
    name: 'ExecutionEdit',
  })
}
const editTrigger = (record: TriggerData<event>) => {
  // 编辑触发器
  router.push({
    name: 'ExecutionEdit',
    query: { id: record.id },
  })
}
const deleteExecute = async (record: TriggerData<event>) => {
  // 删除触发器
  const res = await deleteTrigger(record.id)
  if (res.code == 200) {
    message.success(i18n.t('删除成功'))
    await fetchData()
  } else {
    message.warn(res.msg)
  }
}

const testTriggerFn = async (record: TriggerData<event>) => {
  testTriggerVisible.value = true
  testTriggerId.value = record.id
}

const transformProcessText = (text: number | string) => {
  return transformFieldIdToLabel(props.processTypeList, 'id', 'processName', text)
}
</script>

<style lang="scss" scoped>
.excution-list-container {
  background-color: #fff;
  .content-box {
    padding: 24px;
    .content-remark {
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
    }
    .enable {
      display: inline-block;
      width: 30px;
      height: 18px;
      font-size: 12px;
      color: #2fcc83;
      border: 1px solid #2fcc83;
      background-color: #eafaf2;
      text-align: center;
      border-radius: 2px;
    }
    .disable {
      display: inline-block;
      width: 30px;
      height: 18px;
      color: #f04141;
      border: 1px solid #f04141;
      background-color: #fdecec;
      text-align: center;
      border-radius: 2px;
    }
  }
}
:deep(.fei-su-title) {
  margin-bottom: 0;
}
.card-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}
.trigger-list-popover :deep(.remark-popover) {
  display: inline-block;
  width: 400px;
}
.table-container {
  :deep(.fs-table) {
    .fs-table-tbody > tr.fs-table-row:hover > td,
    .fs-table-tbody > tr > td.fs-table-cell-row-hover {
      background-color: #f1f4f8 !important;
    }
  }
}
.shadow-radius {
  background: #ffffff;
  box-shadow: 0 2px 8px 0 rgba(88, 98, 110, 0.08);
  border-radius: 4px;
}
.fei-su-pagination {
  display: flex;
  justify-content: flex-end;
  padding: 16px 0 0 0 !important;
  > span {
    line-height: 32px;
    color: #bbb;
    margin-right: 10px;
  }
}
</style>
