<template>
  <div class="expandable-item" :style="{ paddingLeft: (level && `${indent}px`) || 0 }">
    <div v-if="item?.children" class="header" @click="toggle">
      <i v-show="item?.children?.length" :class="isOpen ? iconClassOpen : iconClassClosed" class="iconfont" />
      <span class="label">
        <slot name="label" :item="item">
          {{ item.label }}
        </slot>
      </span>
    </div>

    <div v-else class="custom-content">
      <slot :item="item" />
    </div>

    <!-- <transition name="fade-slide"> -->
    <div v-show="isOpen && item.children?.length" class="children">
      <ExpandableTreeItem
        v-for="child in item.children"
        :key="child.id"
        :item="child"
        :level="level + 1"
        :icon-class-open="iconClassOpen"
        :icon-class-closed="iconClassClosed"
        :indent="indent"
        :expanded-ids="expandedIds"
        ref="childRefs"
      >
        <template #label="{ item }">
          <slot name="label" :item="item" />
        </template>
        <template #default="{ item }">
          <slot :item="item" />
        </template>
      </ExpandableTreeItem>
    </div>
    <!-- </transition> -->
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineProps, defineExpose, defineComponent } from 'vue'

defineComponent({
  name: 'ExpandableTreeItem',
})

interface TreeItem {
  id: string | number
  label: string
  children?: TreeItem[]
  [key: string]: any
}

const props = defineProps<{
  item: TreeItem
  level: number
  indent?: number
  iconClassOpen?: string
  iconClassClosed?: string
  expandedIds?: (string | number)[]
}>()

const isOpen = ref(false)
const toggle = () => {
  if (props?.item?.children?.length) {
    isOpen.value = !isOpen.value
  }
}

const indent = props.indent ?? 24
const iconClassOpen = props.iconClassOpen ?? 'iconjiantouxia'
const iconClassClosed = props.iconClassClosed ?? 'iconjiantouyou-xiaohao'

const childRefs = ref<InstanceType<any>[]>([])

onMounted(() => {
  if (props.expandedIds?.includes(props.item.id)) {
    isOpen.value = true
  }
})

function expandAll() {
  isOpen.value = true
  childRefs.value.forEach(child => child.expandAll?.())
}

function collapseAll() {
  isOpen.value = false
  childRefs.value.forEach(child => child.collapseAll?.())
}

defineExpose({
  expandAll,
  collapseAll,
})
</script>

<style scoped>
.expandable-item {
  margin: 4px 0;
}
.header {
  cursor: pointer;
  display: flex;
  align-items: center;
  user-select: none;
}
.iconfont {
  margin-right: 8px;
  text-align: center;
  line-height: 18px;
}
.label {
  flex: 1;
}
.custom-content {
  margin-top: 4px;
  font-size: 12px;
  color: #666;
}
/* .fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
}
.fade-slide-enter-from,
.fade-slide-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-4px);
}
.fade-slide-enter-to,
.fade-slide-leave-from {
  opacity: 1;
  max-height: 1000px;
  transform: translateY(0);
} */
</style>
