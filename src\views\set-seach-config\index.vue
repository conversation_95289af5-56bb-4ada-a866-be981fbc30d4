<template>
  <FContainer class="set-search-config-container" :routes="routes">
    <template #content>
      <FCard class="f-card marginB16">
        <FInput
          :placeholder="i18n.t('请输入搜索关键词')"
          :title="i18n.t('快速搜索')"
          v-model:value="form.remarks"
          :allow-clear="true"
          class="width240"
          @press-enter="onSearch"
        >
        </FInput>
      </FCard>
      <FCard class="f-card cust-table-card" :title="i18n.t('配置筛选项列表')">
        <template #extra>
          <FButton type="primary" @click="openConfigModal(null)"
            ><i class="icon-tubiao_daiwancheng iconfont marginR4"></i>{{ i18n.t('新建筛选项') }}</FButton
          >
        </template>
        <FTable
          table-layout="fixed"
          :loading="loading"
          row-key="id"
          :columns="columns"
          :data-source="dataList"
          :scroll="{ x: true }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'searchType'">
              {{ IsearchType[record.searchType as keyof typeof IsearchType] }}
            </template>
            <template v-if="column.key === 'moduleType'">
              {{ ImoduleType[record.moduleType as keyof typeof ImoduleType] }}
            </template>
            <template v-if="column.key === 'valueType'">
              {{ IvalueType[record.valueType as keyof typeof IvalueType] }}
            </template>
            <template v-if="column.key === 'status'">
              <FSwitch
                v-model:checked="record.status"
                @change="changeStatus(record)"
                :checkedValue="1"
                :unCheckedValue="0"
              />
            </template>
            <template v-if="column.key === 'remarks'">
              <FTooltip
                overlay-class-name="cust-remark-tip"
                color="#fff"
                :title="record.remarks || '--'"
                placement="topLeft"
                :get-popup-container="popConfirmAttribute.getPopupContainer"
              >
                <div class="remark">{{ record.remarks }}</div>
              </FTooltip>
            </template>
            <template v-if="column.key === 'action'">
              <span
                class="iconfont-hover iconfont cursor color4677C7 icontubiao_xietongbianji marginR5"
                :title="i18n.t('编辑')"
                @click="openConfigModal(record)"
              ></span>
              <FPopconfirm
                overlay-class-name="z-pover"
                :ok-text="i18n.t('确定')"
                :cancel-text="i18n.t('取消')"
                @confirm="deleteList(record.id)"
              >
                <template #title>
                  <p class="color333 fontSize14 marginB5">{{ i18n.t('确认删除该筛选条件字段吗？') }}</p>
                  <div class="color999 fontSize12">{{ i18n.t('删除后不可恢复,请谨慎操作') }}</div>
                </template>
                <span
                  class="iconfont iconfont-hover cursor color4677C7 icontubiao_shanchu1 marginR5"
                  :title="i18n.t('删除')"
                ></span>
              </FPopconfirm>
            </template>
          </template>
          <template #emptyText>
            <img :src="noImgUrl" />
            <p class="colorBBB fontSize12 fs-tip">{{ i18n.t('暂无配置筛选项~') }}</p>
          </template>
        </FTable>
        <div class="fei-su-pagination">
          <FPagination
            v-model:current="paging.pageNum"
            @change="onChange"
            v-model:page-size="paging.pageSize"
            :total="paging.total"
            show-size-changer
            show-quick-jumper
            :show-total="() => `${i18n.t('共')} ${paging.total} ${i18n.t('条')}`"
          />
        </div>
      </FCard>
    </template>
  </FContainer>
  <AddSearchConfig :title="title" :show="show" :record="recordList" @submit="submitClass" @popup-close="close" />
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import noImgUrl from '@/assets/images/no-data.png'
import type { TableColumnsType } from '@fs/smart-design/dist/ant-design-vue_es'
import { messageInstance as message } from '@fs/smart-design'
import AddSearchConfig from './components/AddSearchConfig.vue'
import {
  getProcessSearchPage,
  updateProcessSearchPage,
  deleteProcessSearch,
  saveProcessSearchPage,
} from '@/api/searchList'
import type { ISearchData } from '@/types/searchList'
import { ITitle, IsearchType, ImoduleType, IvalueType } from '@/types/searchList'
import { useRoute } from 'vue-router'
import { useI18n } from '@/utils'

const i18n = useI18n()

interface Route {
  path: string
  breadcrumbName: string
  children?: Array<{
    path: string
    breadcrumbName: string
  }>
}

interface IQuery {
  id?: number
}

type IpForm = {
  remarks: string | undefined
}

interface IPage {
  pageNum: number // 当前页
  pageSize: number // 每页条数
  total: number // 总条数
}

const popConfirmAttribute = reactive({
  getPopupContainer: () => document.querySelector('.set-search-config-container') as HTMLElement,
})
const route = useRoute()
const { id: processConfigId } = route.query as IQuery
const form = reactive<IpForm>({
  remarks: undefined,
})
const routes = computed<Route[]>(() => [
  {
    path: '/',
    breadcrumbName: i18n.t('首页'),
  },
  {
    path: '/process/class',
    breadcrumbName: i18n.t('流程类型'),
  },
  {
    path: '/process/setSeachConfig',
    breadcrumbName: i18n.t('配置筛选项列表'),
  },
])
const paging = ref<IPage>({ pageNum: 1, pageSize: 10, total: 100 })
const columns = computed<TableColumnsType>(() => [
  { title: i18n.t('字段标识'), dataIndex: 'field', key: 'field', width: '160px' },
  { title: i18n.t('字段名称'), dataIndex: 'name', key: 'name', width: '160px' },
  { title: i18n.t('检索类型'), dataIndex: 'searchType', key: 'searchType', width: '160px' },
  { title: i18n.t('组件类型'), dataIndex: 'moduleType', key: 'moduleType', width: '160px' },
  { title: i18n.t('值类型'), dataIndex: 'valueType', key: 'valueType', width: '160px' },
  { title: i18n.t('是否启用'), dataIndex: 'status', key: 'status', width: '80px' },
  { title: i18n.t('备注'), dataIndex: 'remarks', key: 'remarks', width: '320px' },
  { title: i18n.t('操作'), dataIndex: 'action', key: 'action', width: '120px' },
])
const dataList = ref<ISearchData[]>()
const loading = ref<boolean>(false)
const title = ref<string>('')
const show = ref(false)
const recordList = reactive({})

onMounted(() => {
  fetchData()
})

const fetchData = async () => {
  try {
    loading.value = true
    const { remarks } = form
    const parmas = {
      remarks,
      processConfigId: processConfigId,
      pageNum: paging.value.pageNum,
      pageSize: paging.value.pageSize,
    }
    const res = await getProcessSearchPage(parmas)
    if (res.code == 200) {
      dataList.value = res.data.list
      paging.value.total = res.data.totalCount
    }
  } finally {
    loading.value = false
  }
}

const close = () => {
  show.value = false
}

const deleteList = async (id: number) => {
  try {
    loading.value = true
    const res = await deleteProcessSearch(id)
    if (res.code == 200) {
      message.success(i18n.t('删除成功'))
      fetchData()
    }
  } finally {
    loading.value = false
  }
}

const submitClass = async (data: any, title: string) => {
  let res
  if (title == ITitle.edit) {
    res = await updateProcessSearchPage({ ...data, ...{ processConfigId: processConfigId } })
  } else {
    delete data.id
    res = await saveProcessSearchPage({ ...data, ...{ processConfigId: processConfigId } })
  }
  if (res.code == 200) {
    title == ITitle.edit ? message.success(i18n.t('编辑成功')) : message.success(i18n.t('新增成功'))
    show.value = false
    fetchData()
  } else {
    message.error(res.msg)
  }
}

const changeStatus = async (record: any) => {
  try {
    loading.value = true
    const res = await updateProcessSearchPage(record)
    if (res.code == 200) {
      message.success(`${record.status ? i18n.t('启用') : i18n.t('关闭')}${i18n.t('成功')}`)
      fetchData()
    } else {
      record.status = record.status ? 0 : 1
    }
  } finally {
    loading.value = false
  }
}

const onChange = (current: number, pageSize: number) => {
  paging.value.pageSize = pageSize
}

const onSearch = () => {
  fetchData()
}

const openConfigModal = (record: any) => {
  show.value = true
  title.value = record ? ITitle.edit : ITitle.add
  record && Object.assign(recordList, record)
}
</script>

<style lang="scss" scoped>
.set-search-config-container {
  width: calc(100% + 40px);
  transform: translate(-20px, -20px);
  :deep(.main) {
    background-color: #f0f2f5 !important;
  }
  .stasus {
    display: inline-block;
    padding: 0 3px;
    background: #eafaf2;
    border-radius: 2px;
    border: 1px solid #2fcc83;
    font-weight: 400;
    color: #2fcc83;
    line-height: 18px;
    font-size: 12px;
    &.no-status {
      border: 1px solid #f04141;
      background: #fdecec;
      color: #f04141;
    }
  }
  .remark {
    width: 100%;
    white-space: normal;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  :deep(.cust-remark-tip .fs-tooltip-inner) {
    color: #333333;
  }
  .fei-su-pagination {
    padding-bottom: 0;
  }
}
</style>
