import { transformDate, getUserInfo } from '@/utils'
import { GetTagAndNode, getDepartment } from '@/api'
import { messageInstance } from '@fs/smart-design'
import { ProcessListParams, IOptions } from '@/types/pgbDataBoard'
import { reactive } from 'vue'
import i18n from '@/i18n'
import { getDefaultValue } from '@/views/localizationSystem/lib/utils'

const userInfo = getUserInfo()

const handleTree = (node: any, users: any = []) => {
  return node.reduce((users: any, cur: any) => {
    if (cur.departmentChildrens) {
      const data = JSON.parse(JSON.stringify(cur))
      data.departmentChildrens = JSON.parse(JSON.stringify(data.uuidAndNames || []))
      !((cur.uuidAndNames || []).length + (cur.departmentChildrens || []).length) && (data.disabled = true)
      users.push(data)
      cur.departmentChildrens.length && handleTree(cur.departmentChildrens, data.departmentChildrens)
    }
    return users
  }, users)
}

const checkChildren = (node: any, users: any = []) => {
  return node.reduce((users: any, cur: any) => {
    if (cur.uuid && cur.name && Object.keys(cur).length === 2) {
      users.push(cur.name)
    } else if (cur.departmentChildrens && cur.departmentChildrens.length) {
      checkChildren(cur.departmentChildrens, users)
    }
    return users
  }, users)
}

const roleLists = await (async () => {
  const res = await getDepartment()
  if (res.code !== 200) throw new Error(res.msg)
  let data: any = []
  data = handleTree(res.data)
  return data
})()

const processLists = await (async () => {
  const res = await GetTagAndNode()
  let data: any = []
  if (res.code === 200) {
    data = res.data || []
  } else {
    messageInstance.warn(res.msg)
  }
  return data
})()

const filterOption = (input: string, option: any) => option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0

export class Search {
  options: IOptions;
  [key: string]: any
  constructor(callback: any) {
    this.callback = callback
    this.options = reactive({
      processConfigId: {
        componentName: 'FSelect',
        componentValueKey: 'processConfigIds',
        componentValue: undefined,
        componentAttrs: {
          style: {
            width: '120px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          placeholder: i18n.t('请选择'),
          pressLine: i18n.t('流程类型'),
          fieldNames: { label: 'processName', value: 'id' },
          showSearch: true,
          optionFilterProp: 'processName',
          options: processLists,
          allowClear: true,
          mode: 'multiple',
          maxTagCount: 'responsive',
          onChange: (value: any, option: any) => {
            this.options.nodeId.componentValue = undefined
            this.options.nodeId.componentAttrs.options = (option || []).reduce((nodes: any, cur: any) => {
              nodes.push(...(cur?.nodes ?? []))
              return nodes
            }, [])
            this.callback(this.submit())
          },
        },
      },
      isNew: {
        componentName: 'FSelect',
        componentValueKey: 'isNew',
        componentValue: undefined,
        componentAttrs: {
          style: {
            width: '120px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          placeholder: i18n.t('请选择'),
          pressLine: i18n.t('是否为最新消息'),
          options: [
            { label: i18n.t('否'), value: 0 },
            { label: i18n.t('是'), value: 1 },
          ],
          allowClear: true,
          onChange: () => {
            this.callback(this.submit())
          },
        },
      },
      nodeId: {
        componentName: 'FSelect',
        componentValueKey: 'nodeId',
        componentValue: undefined,
        componentAttrs: {
          style: {
            width: '120px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          placeholder: i18n.t('请选择'),
          pressLine: i18n.t('当前节点'),
          fieldNames: { label: 'milepostName', value: 'nodeId' },
          options: [],
          allowClear: true,
          onChange: () => {
            this.callback(this.submit())
          },
        },
      },
      sendBy: {
        componentName: 'FCascader',
        componentValueKey: 'sendBy',
        componentValue: undefined,
        componentArgsValue: {},
        componentAttrs: {
          style: {
            width: '120px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          multiple: true,
          showArrow: true,
          placeholder: i18n.t('请选择'),
          pressLine: i18n.t('发送人'),
          fieldNames: { label: 'name', value: 'uuid', children: 'departmentChildrens' },
          maxTagCount: 'responsive',
          options: roleLists,
          showSearch: { filterOption },
          showCheckedStrategy: 'SHOW_CHILD',
          allowClear: true,
          onChange: (value: any, selectedOptions: any) => {
            const selectLists =
              selectedOptions.map((item: any) => {
                return item[item.length - 1]
              }) || []
            Object.assign(this.options.sendBy.componentArgsValue, { selectLists })
            this.callback(this.submit())
          },
        },
        getComponentValueFormat: (value: any, argsValue: any) => {
          const projectUuidList = checkChildren(argsValue.selectLists)
          if (projectUuidList.length) {
            return { sendBy: projectUuidList }
          } else {
            this.options.sendBy.componentValue = undefined
            return undefined
          }
        },
      },
      receiver: {
        componentName: 'FCascader',
        componentValueKey: 'receiver',
        componentValue:
          getDefaultValue(roleLists, {}, 'departmentChildrens', '', userInfo.adminId, 'uuid', 'uuid')[
            userInfo.adminId
          ] || undefined,
        componentArgsValue: {
          selectLists: [{ uuid: userInfo.adminId, name: userInfo.feiShuName }],
        },
        componentAttrs: {
          style: {
            width: '120px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          multiple: true,
          showArrow: true,
          placeholder: i18n.t('请选择'),
          pressLine: i18n.t('接收人'),
          fieldNames: { label: 'name', value: 'uuid', children: 'departmentChildrens' },
          maxTagCount: 'responsive',
          options: roleLists,
          showSearch: { filterOption },
          showCheckedStrategy: 'SHOW_CHILD',
          allowClear: true,
          onChange: (value: any, selectedOptions: any) => {
            const selectLists =
              selectedOptions.map((item: any) => {
                return item[item.length - 1]
              }) || []
            Object.assign(this.options.receiver.componentArgsValue, { selectLists })
            this.callback(this.submit())
          },
        },
        getComponentValueFormat: (value: any, argsValue: any) => {
          const projectUuidList = checkChildren(argsValue.selectLists)
          if (projectUuidList.length) {
            return { receiver: projectUuidList }
          } else {
            this.options.receiver.componentValue = undefined
            return undefined
          }
        },
      },
      time: {
        componentName: 'FRangePicker',
        componentValueKey: 'time',
        componentValue: undefined,
        componentLabel: i18n.t('发送时间'),
        componentAttrs: {
          style: {
            width: '240px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          pressLine: i18n.t('发送时间'),
          placeholder: [i18n.t('开始日期'), i18n.t('结束日期')],
          onChange: () => {
            this.callback(this.submit())
          },
        },
        getComponentValueFormat: (value: any) => {
          if (!value || value.length !== 2) return undefined
          return {
            startDate: transformDate(value[0], 'YYYY-MM-DD HH:mm:ss'),
            endDate: transformDate(value[1], 'YYYY-MM-DD HH:mm:ss'),
          }
        },
      },
      search: {
        componentName: 'FInput',
        componentValueKey: 'search',
        componentValue: undefined,
        componentLabel: i18n.t('快速搜索'),
        componentAttrs: {
          style: {
            width: '240px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          pressLine: i18n.t('快速搜索'),
          placeholder: i18n.t('流程编码/流程名称/消息内容'),
          onPressEnter: () => {
            this.callback(this.submit())
          },
        },
      },
    })
    this.init()
  }

  init() {
    this.callback(this.submit())
  }

  submit() {
    const params: ProcessListParams = {}
    Object.values(this.options).forEach(value => {
      ;(value.componentValue &&
        value.getComponentValueFormat &&
        Object.assign(
          params,
          value.getComponentValueFormat(value.componentValue, value?.componentArgsValue || null)
        )) ||
        (params[value.componentValueKey] = value.componentValue)
    })
    return params
  }
}
