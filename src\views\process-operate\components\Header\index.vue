<template>
  <div class="process-operate-header">
    <div class="process-oprate-header-back" @click="handleBackCLick">
      <Icon class="__rotate" icon="iconjiantouyou-xiaohao" :size="16" />
      <span class="ml4">{{ i18n.t('返回') }}</span>
    </div>
    <div class="process-oprate-header-title">
      <span class="__title">{{ props.title }}</span>
      <span class="__sub-title">{{ props.subTitle }}</span>
    </div>
    <div class="process-oprate-header-extend">
      <span class="__copy" :data-clipboard-text="processLink" @click="useCopy('.__copy')">{{
        i18n.t('复制链接')
      }}</span>
      <FDropdown :get-popup-container="(triggerNode: any) => triggerNode.parentNode">
        <Icon class="__more" icon="icongengduo21" :size="16" />
        <template #overlay>
          <FMenu>
            <FMenuItem v-if="currtMilepost.isRecall" @click="operate(EmitType.revoke, currtMilepost)">
              <Icon icon="icontubiao_chehui" :size="16" />{{ i18n.t('撤回流程') }}
            </FMenuItem>
            <FMenuItem
              v-if="currtMilepost.milepostRole == 1 && !isLtc"
              @click="operate(EmitType.finish, currtMilepost)"
            >
              <Icon icon="icontubiao_chenggong" :size="16" />终止流程
            </FMenuItem>
            <FMenuItem
              v-if="currtMilepost.milepostRole == 1 && isLtc"
              @click="operate(EmitType.lostSingle, currtMilepost)"
            >
              <Icon icon="icontubiao_chenggong" :size="16" />{{ i18n.t('失单') }}
            </FMenuItem>
            <FMenuItem @click="operate(EmitType.createProcess, currtMilepost)">
              <Icon icon="icontubiao_guanlian1" :size="16" />{{ i18n.t('创建关联流程') }}
            </FMenuItem>
            <FMenuItem @click="operate(EmitType.deleteProcess, currtMilepost)">
              <Icon icon="icontubiao_xietongshanchu" :size="16" />{{ i18n.t('删除流程') }}
            </FMenuItem>
          </FMenu>
        </template>
      </FDropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ComputedRef, Ref, computed, inject } from 'vue'
import { useRouter } from 'vue-router'
import Icon from '@/components/Icon/index.vue'
import { IProcess } from '@/types/handle'
import { EmitType } from '@/views/process-detail/config'
import { BAESURL, useI18n } from '@/utils'
import useCopy from '@/hooks/useCopy'

interface IProps {
  title: string
  subTitle: string
}

const i18n = useI18n()
const router = useRouter()
const props = defineProps<IProps>()

const processId = inject<number>('processId') // 流程 id
const processNo = inject<ComputedRef<string>>('processNo') // 流程编号
const processName = inject<ComputedRef<string>>('processName') // 流程名称
const currtMilepost = inject('currtMilepost') as Ref<IProcess> // 当前里程碑信息
const operate = inject('operate') as (key: EmitType, data: IProcess) => void
const processConfigInfo = inject('processConfigInfo') as Record<string, any> // 当前流程配置信息
const black = new Function(`return ${process.env.VUE_APP_PROCESS_DETAIL_BACK_WHITE}`)() || []
const ltcLostSingle = new Function(`return ${process.env.VUE_APP_PROCESS_LTC_LOST_SINGLE}`)() || []
const isLtc = computed(() => ltcLostSingle.includes(processConfigInfo?.value?.id))

const processLink = computed(
  () => `${processName?.value} ${processNo?.value} ${BAESURL}/bpm-manage/process/detail/${processId}`
)

const handleBackCLick = () => {
  if (black.includes(processConfigInfo?.value?.id)) {
    router.push({
      name: 'processManagement',
      params: {
        id: processConfigInfo?.value?.id,
        processDefineKey: processConfigInfo?.value?.processDefineKey,
      },
    })
  } else {
    router.push({ name: 'ProcessList' })
  }
}
</script>

<style scoped lang="scss">
.ml4 {
  margin-left: 4px;
}

.process-operate-header {
  display: flex;
  height: 54px;
  padding: 0 24px;
  margin: -20px -20px 0 -20px;
  align-items: center;
  background: white;

  > .process-oprate-header-back {
    display: flex;
    line-height: 24px;
    color: #333;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      color: #5fa4f2;
    }

    > .__rotate {
      transform: rotate(180deg);
    }
  }
  > .process-oprate-header-title {
    margin: 0 24px;
    display: flex;
    align-items: center;

    > .__title {
      color: #333;
      font-size: 16px;
      font-weight: 500;
    }
    > .__sub-title {
      margin-left: 8px;
      color: #999;
      font-size: 12px;
    }
  }
  > .process-oprate-header-extend {
    display: flex;
    margin-left: auto;
    line-height: 24px;

    > .__copy {
      margin-right: 16px;
      color: #333;
      font-size: 12px;
      cursor: pointer;

      &:hover {
        color: #5fa4f2;
      }
    }

    .__more {
      margin-top: 2px;
      width: 20px !important;
      height: 20px !important;
      line-height: 20px !important;
      text-align: center !important;
      border-radius: 2px;
      cursor: pointer;
      color: #333;
      &:hover {
        background-color: #f1f4f8;
      }
    }

    :deep(.fs-dropdown) {
      .fs-dropdown-menu {
        padding: 4px;

        .fs-dropdown-menu-item {
          display: flex;
          align-items: center;
          padding: 8px 12px;
          color: #333;
          font-size: 12px;
          height: 32px;
          &:hover {
            background-color: #f1f4f8;
          }

          .fs-dropdown-menu-title-content {
            line-height: 16px;
          }
        }
      }

      .fs-dropdown-menu-item-selected {
        background-color: white;
      }

      .iconfont {
        margin-right: 8px;
        width: 16px !important;
        height: 16px !important;
        line-height: 16px !important;
        vertical-align: bottom;
      }
    }
  }
}
</style>
