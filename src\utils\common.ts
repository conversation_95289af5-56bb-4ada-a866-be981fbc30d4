/* eslint-disable @typescript-eslint/no-explicit-any */
import { unref, Ref } from 'vue'
import router from '@/router'
import dayjs from 'dayjs'
import mitt from 'mitt'
import { downloadFileList } from '@/api/loadFiles'
import { message } from '@fs/smart-design'

export const BAESURL = process.env.VUE_APP_API_URL
export const S3URL = process.env.VUE_APP_S3_URL
export const BASESTATEURL = process.env.VUE_APP_STATE_URL
export const ERPURL = process.env.VUE_APP_ERP_URL
export const GOTO_WHITE = new Function(`return ${process.env.VUE_APP_GOTO_WHITE}`)()
export const CRMURL = process.env.VUE_APP_CRM_URL
export const MOMURL = process.env.VUE_APP_MOM_URL
export const APP_ENV = process.env.VUE_APP_ENV
export const BPM_MESSAGE_EDITOR = 'BPM_MESSAGE_EDITOR'
export const SUPERSET_URL = process.env.VUE_APP_SUPERSET_URL
export const WORKFLOW_URL = process.env.VUE_APP_WORKFLOW_URL
export const NEW_IPD_CONFIG_ID = process.env.VUE_APP_PROCESS_NEW_IPD_CONFIG_ID
export const IR_CONFIG_ID = process.env.VUE_APP_PROCESS_IR_CONFIG_ID
export const SR_CONFIG_ID = process.env.VUE_APP_PROCESS_SR_CONFIG_ID
export const AR_CONFIG_ID = process.env.VUE_APP_PROCESS_AR_CONFIG_ID
export const MM_CONFIG_ID = process.env.VUE_APP_PROCESS_MM_CONFIG_ID

export const emitter = mitt()
// 时间转换器
export const transformDate = (
  date: number | string | dayjs.Dayjs | null | undefined,
  format = 'YYYY-MM-DD HH:mm:ss'
) => {
  if (!date) return '--'
  return dayjs(date).format(format)
}

// 获取 html 片段中的文本
export const getHTMLSpingtext = (str: string) => {
  const $div = document.createElement('div')
  $div.innerHTML = str
  return $div.textContent || $div.innerText || '--'
}

// 深拷贝
export const deepClone = <T extends Record<string, any>>(obj: T): T => {
  if (!obj || typeof obj !== 'object') return obj

  let clonedObj: T
  if (Array.isArray(obj)) {
    clonedObj = obj.map(item => deepClone(item)) as unknown as T
  } else {
    clonedObj = {} as T
    for (const key in obj) {
      clonedObj[key] = deepClone(obj[key])
    }
  }
  return clonedObj
}

// 防抖
type FunctionArgs<Args extends any[] = any[], Return = void> = (...args: Args) => Return
type MaybeRef<T> = T | Ref<T>

//防抖函数
export const debounce = <T extends FunctionArgs>(fn: T, timeout: MaybeRef<number> = 200, immediate = false) => {
  let timmer: any
  const wait = unref(timeout)
  return (...args: unknown[]) => {
    timmer && clearTimeout(timmer)
    if (immediate) {
      if (!timmer) fn(...args)
      timmer = setTimeout(() => (timmer = null), wait)
    } else {
      timmer = setTimeout(() => fn(...args), wait)
    }
  }
}

// 文件预览
export const previewFile = (url: string, name: string) => {
  const httpUrl = `${S3URL}/api/s3/getFileByUrl?url=${encodeURIComponent(url)}&filename=${encodeURIComponent(
    name
  )}&type=1`
  window.open(httpUrl, '_blank')
}

const limitFileTypes = ['PDF', 'JPG', 'JPEG', 'PNG', 'DOC', 'DOCX', 'PPT', 'PPTX', 'XLS', 'XLSX', 'TXT', 'ZIP', 'RAR']

export const fileLimit = (file: any) => {
  const fileType = file.name.replace(/.+\./, '')
  const allowTypes = limitFileTypes
  const allowSize = 2 * 1024
  if (allowTypes.indexOf(fileType.toUpperCase()) < 0) {
    message.error('文件类型错误')
    return false
  }
  if (file.size / (1024 * 1024) > allowSize) {
    message.error('最大文件2G')
    return false
  } else {
    return true
  }
}

// 文件下载
export const download = (url: string, name: string) => {
  const httpUrl = `${S3URL}/api/s3/getFileByUrl?url=${encodeURIComponent(url)}&filename=${encodeURIComponent(name)}`
  window.open(httpUrl, '_blank')
}

export const batchDownload: any = async (fileList: any[]) => {
  const res = await downloadFileList(fileList)
  const blob = new Blob([res as any], { type: 'application/zip' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `file${new Date()}.zip`
  a.click()
  URL.revokeObjectURL(url)
}

// 跳转到 需求处理 页面的通用方法
export const jumpToDemand = (id: string | number, processTypeId: string | number, targer = false) => {
  // const options = GOTO_WHITE.includes(processTypeId)
  //   ? { name: 'ProcessDetail', params: { id } }
  //   : { name: 'DemandHandle', params: { id } }

  const options = { name: 'ProcessDetail', params: { id } }

  if (!targer) router.push(options)
  else window.open(router.resolve(options).href, '_blank')
}
