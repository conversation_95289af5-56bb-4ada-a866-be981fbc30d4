import createRequest from '@fs/request'
import { messageInstance } from '@fs/smart-design'
import { getMicroGlobalData, getToken, getUserInfo, isMicroApp, logout, updateToken } from '@/utils'
import store from '@/store'
import { BAESURL } from './common'

export const request = createRequest({ baseURL: BAESURL, timeout: 600000 }, { message: messageInstance })

request.interceptors.request.use(async (config: any) => {
  // await updateToken()
  const token = getToken()
  const userInfo = getUserInfo()
  const stateToken = store.getters['user/getStateToken']
  if (token && userInfo) {
    config?.headers?.set('token', `${token}`)
    config?.headers?.set('admin-id', `${userInfo?.adminId}`)
    config?.headers?.set('admin-name', `${userInfo?.adminName}`)
  }
  if (isMicroApp) {
    const micorGlobalData = getMicroGlobalData()
    const language = micorGlobalData.methods.getLanguage()
    language && config?.headers?.set('Accept-Language', language)
  }
  stateToken && config?.headers?.set('stats-token', `${stateToken}`)
  return config
})

request.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    // code error 的标识
    if (error?.isCodeError) {
      const { code } = error
      switch (code) {
        case 401:
        case 1002:
        case 1004:
        case 1009:
          messageInstance.error(`登录失效: ${error?.message}`)
          logout()
          break
        default:
      }
    }
    return Promise.reject(error)
  }
)
