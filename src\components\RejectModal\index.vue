<template>
  <FModal v-model:visible="value" :title="i18n.t('驳回')" :width="400" :z-index="1001">
    <div class="reject-modal-wrapper">
      <FForm ref="formRef" :model="formState" :rules="rules" layout="vertical">
        <FFormItem :label="i18n.t('请选择驳回节点')" name="targetMilepostId">
          <FSelect
            :placeholder="i18n.t('请选择驳回节点')"
            v-model:value="formState.targetMilepostId"
            :field-names="{ label: 'topicName', value: 'id' }"
            :options="rejectList"
            allow-clear
            show-search
          />
        </FFormItem>
        <FFormItem :label="i18n.t('备注说明：')" name="msg">
          <FTextarea v-model:value="formState.msg" :rows="4" :placeholder="i18n.t('请输入')" />
        </FFormItem>
      </FForm>
    </div>
    <template #footer>
      <FButton key="back" @click="value = false">{{ i18n.t('取消') }}</FButton>
      <FButton key="submit" type="primary" @click="handleOk(formRef)">{{ i18n.t('确定') }}</FButton>
    </template>
  </FModal>
</template>
<script lang="ts" setup>
import { ref, reactive, computed, watch } from 'vue'
import type { Rule } from '@fs/smart-design/dist/ant-design-vue_es/form'
import type { FormInstance } from '@fs/smart-design/dist/ant-design-vue_es'
import { useI18n } from '@/utils'
interface FormState {
  targetMilepostId: number | string
  msg: string
}
const emit = defineEmits(['submit', 'update:value'])
const i18n = useI18n()
const props = defineProps({
  value: {
    type: Boolean,
    default: false,
  },
  rejectList: {
    type: Object,
    default: () => ({}),
  },
})
watch(
  () => props.rejectList,
  newValue => {
    formState.targetMilepostId = newValue[newValue.length - 1].id
  }
)

const value = computed({
  get() {
    return props.value
  },
  set(val: boolean) {
    emit('update:value', val)
  },
})
const rules: Record<string, Rule[]> = {
  targetMilepostId: [{ required: true, message: i18n.t('请选择驳回节点'), trigger: 'change' }],
  msg: [{ required: true, message: i18n.t('请填写备注说明'), trigger: 'blur' }],
}
const formRef = ref<FormInstance>()
const formState = reactive<FormState>({
  targetMilepostId: '',
  msg: '',
})

const handleOk = async (formRef: FormInstance | undefined) => {
  if (!formRef) return
  await formRef.validate()
  emit('submit', formState)
}
</script>
<style lang="scss">
.reject-modal-wrapper {
  .fs-form-item {
    margin-bottom: 22px !important;
  }
  div.fs-form-item-control-input-content,
  .fs-form-item-control-input-content textarea.fs-input {
    height: auto !important;
  }

  .fs-modal-footer .fs-btn {
    display: inline-block;
  }
}
</style>
