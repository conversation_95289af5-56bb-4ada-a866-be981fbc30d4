import { IClassData, IProcessCollect } from '@/types/class'
import { IRes, IResDataList } from '@/types/request'
import { request } from '@/utils'

export interface IProcessModel {
  id: number // 流程模型 id
  type: number // 流程模型类型 流程类型 1主流程 2子流程
  source: number // 流程模型来源 所属系统 1.BPM系统 2.合同系统 3.权限平台
  processName: string // 流程模型名称
  processConfigId: number // 流程配置 id
  processConfigName: string // 流程配置名称
  processConfigType: string // 流程配置类型
  remarks: string // 流程模型描述
  versionId: number // 流程模型版本 id
  versionNum: number // 流程模型版本号
  context: string // 流程模型内容
  createUser: string // 创建人
  createTime: string // 创建时间
  updateUser: string // 更新人
  updateTime: string // 更新时间
}

export interface UserList {
  completeTime: string
  status: number
  userName: string
  uuid: string
}

export interface HandleNodeList {
  completeTime: string
  createdTime: string
  duration: number
  forcastTime: string
  formData: any
  formKey: string
  id: number
  instanceId: number
  mobileFormKey: string
  nextMilepostId: any
  nodeId: string
  nodeStartTime: string
  properties: any
  role: string
  status: number
  topicName: string
  userList: UserList[]
}

export interface ApprovalInfoRes {
  completeTime: string
  configSource: number
  createdTime: string
  creator: string
  creatorName: string
  formData: FormData
  handleNodeList: HandleNodeList[]
  id: number
  isUrgent: number
  nodeList: FormData[]
  processConfigId: number
  processInstanceCode: string
  processType: string
  status: number
  topicName: string
}

export const selectApprovalByInstanceId = async (instanceId: string) => {
  const res = await request.get<IRes<ApprovalInfoRes>>(`/api/approval/instance/selectByInstanceId/${instanceId}`)
  return res as unknown as IRes<ApprovalInfoRes>
}

export interface ApplyFormInfoRes {
  content?: string
  id?: number
  name?: string
  status?: number
  formData?: any
}

export const getApplyFormByInstanceId = async (instanceId: string) => {
  const res = await request.get<IRes<ApplyFormInfoRes>>(`/api/approval/instance/getApplyForm/${instanceId}`)
  return res as unknown as IRes<ApplyFormInfoRes>
}

export const readApplyByInstanceId = async (instanceId: string) => {
  const res = await request.get<IRes>(`/api/approval/instance/read/${instanceId}`)
  return res as unknown as IRes
}

export interface FlowChartInfoRes {
  actualNodeList: ApprovalInfoRes[]
  context: any
}

export const getFlowChartByInstanceId = async (instanceId: string) => {
  const res = await request.get<IRes<FlowChartInfoRes>>(`/api/approval/instance/flowChart/${instanceId}`)
  return res as unknown as IRes<FlowChartInfoRes>
}

export interface SubmitNodeParams {
  files?: {
    fileName: string
    size: number
    url: string
    [key: string]: any
  }[]
  formData?: any
  isAutomatic?: number
  milepostId?: number
  instanceId?: number
  makeUuidList?: string[]
  msg?: string
}

export const submitApprovalNode = async (data: SubmitNodeParams) => {
  const res = await request.post<IRes>(`/api/approval/node/submit`, data)
  return res as unknown as IRes
}

export interface RefuseNodeParams {
  files?: {
    fileName: string
    size: number
    url: string
    [key: string]: any
  }[]
  instanceId?: number
  msg?: string
  currentSuperviserUuid?: string
  superviser?: any
  superviserUuid?: string
  userList?: any
  targetMilepostId?: number
  sourceMilepostId?: number
}

export const refuseApprovalNode = async (data: RefuseNodeParams) => {
  const res = await request.post<IRes>(`/api/approval/node/refuse`, data)
  return res as unknown as IRes
}

export const transferApprovalNode = async (data: RefuseNodeParams) => {
  const res = await request.post<IRes>(`/api/approval/node/transfer`, data)
  return res as unknown as IRes
}

export const countersignApprovalNode = async (data: RefuseNodeParams) => {
  const res = await request.post<IRes>(`/api/approval/node/countersign`, data)
  return res as unknown as IRes
}

export const subtractionApprovalNode = async (data: RefuseNodeParams) => {
  const res = await request.post<IRes>(`/api/approval/node/subtraction`, data)
  return res as unknown as IRes
}

export const rejectApprovalNode = async (data: RefuseNodeParams) => {
  const res = await request.post<IRes>(`/api/approval/node/reject`, data)
  return res as unknown as IRes
}

export interface PageApprovalNodeListParams {
  instanceId?: any
  milepostId?: number
  pageNum?: number
  pageSize?: number
  type?: number
  uuid?: string
}

export interface PageApprovalNodeListRes {
  completeTime: string
  createdTime: string
  feishuName: string
  id: string
  files: {
    fileName: string
    size: number
    url: string
    [key: string]: any
  }[]
  instanceId: number
  milepostId: number
  msg: string
  readTime: string
  status: number
  topicName: string
  type: number
  uuid: string
}

export const pageApprovalNodeList = async (data: PageApprovalNodeListParams) => {
  const res = await request.post<IRes<IResDataList<PageApprovalNodeListRes>>>('/api/approval/node/query', data)
  return res as unknown as IRes<IResDataList<PageApprovalNodeListRes>>
}

export interface FlowChartIndicatorsRes {
  meStartAll: number
  meStartMonth: number
  processedAll: number
  processedMonth: number
  unread: number
  waitDeal: number
}

export const getFlowChartIndicators = async () => {
  const res = await request.get<IRes<FlowChartIndicatorsRes>>(`/api/approval/instance/getIndicators`)
  return res as unknown as IRes<FlowChartIndicatorsRes>
}

export interface FlowChartProcessAllRes extends IClassData {
  businessType: string
  createdTime: string
  dictionaryId: number
  id: number
  invalid: number
  isDelayReason: number
  isDeleteMake: string
  isNotice: number
  isSendmsg: number
  isUpdateCompleteSend: number
  prefix: string
  processDefineKey: string
  processDefineKeyName: string
  processName: string
  readMark: string
  remark: string
  sort: number
  source: number
  tabConfig: string
  tag: string
  taskEdit: number
  updateTime: string
  writeMark: string
}

export const getFlowChartProcessAll = async (configName?: string) => {
  const res = await request.get<IRes<FlowChartProcessAllRes[]>>(`/api/approval/config/getProcessAll`, {
    params: { configName },
  })
  return res as unknown as IRes<FlowChartProcessAllRes[]>
}

export interface PageApprovalProcessParams {
  creator?: string
  endTime?: string
  isUrgent?: number
  pageNum?: number
  pageSize?: number
  total?: number
  processConfigId?: number
  queryInput?: string
  startTime?: string
  statusList?: any[]
  type?: number
  uuid?: string
  workbenchType?: number
}

export interface BasicPageParams {
  pageNum?: number
  pageSize?: number
  total?: number
}

export interface PageApprovalProcessRes {
  completeTime?: string
  createdTime?: string
  creator?: string
  creatorName?: string
  id?: number
  isUrgent?: number
  processConfigId?: number
  processInstanceCode?: string
  processType?: string
  status?: number
  topicName?: string
  handleNodeList?: HandleNodeList[]
  [key: string]: any
}

export const getApprovalProcess = async (data: PageApprovalProcessParams) => {
  const res = await request.post<IRes<IResDataList<PageApprovalProcessRes>>>(`/api/approval/instance/query`, data)
  return res as unknown as IRes<IResDataList<PageApprovalProcessRes>>
}

export interface PageApprovalProcessCountRes {
  make?: number
  meStart?: number
  processed?: number
  unread?: number
  waitDeal?: number
}

export const getApprovalProcessCount = async () => {
  const res = await request.get<IRes<PageApprovalProcessCountRes>>(`/api/approval/instance/count`)
  return res as unknown as IRes<PageApprovalProcessCountRes>
}

export const getApprovalCreateForm = async (processConfigId: number | string) => {
  const res = await request.get<IRes<ApplyFormInfoRes>>(`/api/approval/instance/getCreateForm/${processConfigId}`)
  return res as unknown as IRes<ApplyFormInfoRes>
}

// 用户收藏审批流
export const getCollectApprovalProcess = async () => {
  const res = await request.get<IRes<IProcessCollect>>(`/api/approval/config/getCollectProcess`)
  return res as unknown as IRes<IProcessCollect>
}

// 用户常用审批流
export const getCommonApprovalProcess = async () => {
  const res = await request.get<IRes<FlowChartProcessAllRes>>(`/api/approval/config/getCommonProcess?size=3`)
  return res as unknown as IRes<FlowChartProcessAllRes>
}
export interface PageApprovalQueryLogParams {
  pageNum?: number
  pageSize?: number
  total?: number
  instanceId?: string
  type?: number
}

export interface PageApprovalQueryLogRes {
  content: string
  createUser: string
  createUuid: string
  createdTime: string
  files: {
    fileName: string
    size: number
    url: string
    [key: string]: any
  }[]
  id: number
  instanceId: number
  milepostId: number
  recipient: {
    userName: string
    uuid: string
  }[]
  topicName: string
  type: number
  [key: string]: any
}

export const getApprovalQueryLog = async (data: PageApprovalQueryLogParams) => {
  const res = await request.post<IRes<IResDataList<PageApprovalQueryLogRes>>>(`/api/approval/instance/queryLog`, data)
  return res as unknown as IRes<IResDataList<PageApprovalQueryLogRes>>
}

export interface PageApprovalDiagnosisParams {
  endTime?: string
  startTime?: string
}

export interface PageApprovalDiagnosisRes {
  auditingAveRatio: number
  auditingAveTime: number
  complete: number
  completeRatio: number
  longTimeout: number
  processAveTime: number
  processAveTimeRatio: number
  processed: number
  timeout: number
  waitDeal: number
  [key: string]: any
}

export const getApprovalDiagnosis = async (data: PageApprovalDiagnosisParams) => {
  const res = await request.post<IRes<PageApprovalDiagnosisRes>>(`/api/approval/instance/getDiagnosis`, data)
  return res as unknown as IRes<PageApprovalDiagnosisRes>
}

export const approvalNodeHurryMessage = async (params: { recordId: string }) => {
  const res = await request.get<IRes>(`/api/approval/node/hurryMessage`, { params })
  return res as unknown as IRes
}
