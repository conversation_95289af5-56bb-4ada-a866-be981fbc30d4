<template>
  <Container>
    <FConfigProvider :locale="locale">
      <router-view />
    </FConfigProvider>
  </Container>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useStore } from 'vuex'
import Container from '@/components/Container/index.vue'
import { useI18n } from '@/utils'
import zhCN from '@fs/smart-design/dist/ant-design-vue_es/locale-provider/zh_CN'
import enUS from '@fs/smart-design/dist/ant-design-vue_es/locale-provider/en_US'

const i18n = useI18n()
const store = useStore()
const locale = ref(i18n?.language === 'zh-CN' ? zhCN : enUS)

// 初始化用户数据
store?.dispatch('user/getUsers')

// 微前端环境下，国际化语言变化监听
if (window.__MICRO_APP_ENVIRONMENT__) {
  // 监听基座下发的数据变化
  window.microApp.addDataListener((data: { path: string; language: string }) => {
    console.log('data', data)
    if (data.language) {
      locale.value = data.language === 'zh-CN' ? zhCN : enUS
    }
  })
}
</script>
