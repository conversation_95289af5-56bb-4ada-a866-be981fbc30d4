import { request } from '@/utils'
import type {
  IRes,
  IDictionatyData,
  IDictionatyDataList,
  ISearchDictionatyData,
  IDictionaryConfigId,
  IDictionaryConfigIdData,
  SearchDictionary,
} from '@/types/dictionary'

interface IPageParmas {
  pageNum: number // 当前页
  pageSize: number // 每页条数
}
interface IQueryParams extends IPageParmas {
  name: null | string
}
export function createDictionary(data: IDictionatyData): Promise<IRes<IDictionatyData>> {
  return request.post('/api/dictionary/save', data)
}
export function updateDictionary(data: IDictionatyData): Promise<IRes<IDictionatyData>> {
  return request.post('/api/dictionary/update', data)
}
export function getDictionaryList(data: IQueryParams): Promise<IRes<IDictionatyDataList<IDictionatyData>>> {
  return request.post('/api/dictionary/getList', data)
}

export function deleteDictionary(data: any): Promise<IRes<IDictionatyData>> {
  return request.get('/api/dictionary/del', { params: data })
}

export function banDictionary(data: any): Promise<IRes<IDictionatyData>> {
  return request.get('/api/dictionary/ban', {
    params: data,
  })
}

export async function getRootDictionary() {
  const res = await request.get<IRes<IDictionatyData[]>>('/api/dictionary/getRoot')
  return res as unknown as IRes<IDictionatyData[]>
}

export async function getSearchDictionaryList() {
  const res = await request.get<IRes<ISearchDictionatyData[]>>('/api/searchDictionary/getAll')
  return res as unknown as IRes<ISearchDictionatyData[]>
}

export async function getSearchDictionaryFieldList(data: IDictionaryConfigId) {
  const res = await request.post<IRes<IDictionaryConfigIdData<ISearchDictionatyData>>>(
    '/api/searchDictionary/getDictionaryByConfigId',
    data
  )
  return res as unknown as IRes<IDictionaryConfigIdData<ISearchDictionatyData>>
}

export async function addSearchDictionary(data: SearchDictionary) {
  const res = await request.post<IRes<any>>('/api/searchDictionary/add', data)
  return res as unknown as IRes<any>
}

export async function updateSearchDictionary(data: SearchDictionary) {
  const res = await request.post<IRes<any>>('/api/searchDictionary/update', data)
  return res as unknown as IRes<any>
}

export function deleteSearchDictionary(id: any): Promise<IRes<any>> {
  return request.get(`/api/searchDictionary/delete/${id}`)
}
