<template>
  <DetailValue v-if="componentConfig?.isDetail" :componentConfig="componentConfig" :value="value" class="view-box" />
  <FTextarea class="custom-textarea" v-else v-model:value="value" v-bind="$attrs" />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import DetailValue from '@/views/process-operate/components/CustomComponents/BusinessComponent/DetailValue/index.vue'

interface IProps {
  value: string | undefined
  componentConfig?: any
}

const props = defineProps<IProps>()
const emits = defineEmits(['update:value'])
const value = computed({
  get: () => props.value,
  set: val => emits('update:value', val),
})
</script>
