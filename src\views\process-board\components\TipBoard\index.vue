<template>
  <div class="tip-board-container">
    <Layout
      :title="i18n.t('项目总数 (个）')"
      :num="Number(processData?.projectNum || 0)"
      :rate="Number(processData?.addRingRatio || 0)"
      bg-color="#EBF3FD5C"
      style="margin-right: 16px"
    >
      <FSpin size="large" :spinning="processLoading">
        <BaseEchart :option="projectChartOption" height="100px" width="140px" />
      </FSpin>
    </Layout>
    <div class="item-box">
      <Layout
        class="layout-line"
        :title="i18n.t('已完成 (个)')"
        :num="Number(processData?.done || 0)"
        bg-color="#EAFAF25C"
      >
        <FSpin size="large" :spinning="processLoading">
          <BaseEchart :option="doneChartOption" height="100px" width="170px" />
        </FSpin>
      </Layout>
      <Layout
        class="layout-line"
        :title="i18n.t('逾期完成 (个)')"
        :num="Number(processData?.delayDone || 0)"
        bg-color="#EAFAF25C"
      >
        <FSpin size="large" :spinning="processLoading">
          <BaseEchart :option="delayDoneChartOption" height="100px" width="170px" />
        </FSpin>
      </Layout>
      <Layout
        :title="i18n.t('进行中 (个)')"
        :num="Number(processData?.doing || 0)"
        bg-color="#EAFAF25C"
        style="margin-right: 16px"
      >
        <img class="up-rate" src="./components/images/up-rate.png" />
      </Layout>
    </div>
    <Layout
      :title="i18n.t('任务总数 (个）')"
      :num="Number(taskData?.taskNum || 0)"
      :rate="Number(taskData?.addRingRatio || 0)"
      bg-color="#FEF4E95C"
    >
      <FSpin size="large" :spinning="taskLoading">
        <BaseEchart :option="taskChartOption" height="100px" width="190px" />
      </FSpin>
    </Layout>
  </div>
</template>

<script setup lang="ts">
import Layout from './components/Layout/index.vue'
import BaseEchart from '@/components/BaseEchart/index.vue'
import type { EChartsOption } from '@/components/BaseEchart/config'
import { barOptions, pieOptions } from './chartsOptions'
import { computed } from 'vue'
import { useI18n } from '@/utils'
const i18n = useI18n()

type propsType = {
  processLoading: boolean
  taskLoading: boolean
  processData: any
  taskData: any
}

const props = defineProps<propsType>()
const projectChartOption = computed<EChartsOption>(() => {
  if (props.processData) {
    return {
      grid: {
        left: '0',
        right: '0',
        top: '12%',
        bottom: '0',
      },
      xAxis: {
        data: (props.processData?.projectChart || []).map((item: any) => item.month + '月'),
      },
      yAxis: {
        type: 'value',
        show: false,
      },
      series: [
        {
          type: 'bar',
          name: i18n.t('项目数量'),
          data: (props.processData?.projectChart || []).map((item: any) => item.num),
          barWidth: 16,
          barMinHeight: 3,
          barCategoryGap: 8,
          stack: 'Total',
          label: {
            show: true,
            distance: 2,
            position: 'top',
            color: '#666',
            fontSize: '10px',
          },
        },
      ],
    }
  } else {
    return barOptions
  }
})

const doneChartOption = computed<EChartsOption>(() => {
  if (props.processData) {
    return {
      title: {
        show: true,
        text: (props.processData?.doneRatio || 0) + '%',
        subtext: i18n.t('项目完成率'),
        left: '25%',
        top: '37%',
        textAlign: 'center',
        itemGap: 4,
        textStyle: {
          fontSize: '12px',
          color: '#333333',
          fontWeight: 'bold',
        },
        subtextStyle: {
          fontSize: '10px',
          color: '#666666',
          fontWeight: 400,
        },
      },
      color: ['#2FCC83', '#82E0B4'],
      series: [
        {
          type: 'pie',
          center: ['28%', '50%'],
          radius: ['60%', '92%'],
          avoidLabelOverlap: false,
          label: {
            position: 'inside',
            formatter: '{c}',
            color: '#fff',
            fontSize: 10,
          },
          emphasis: {
            scale: false,
          },
          data: [
            {
              value: props.processData?.done || 0,
              name: i18n.t('已完成'),
            },
            {
              value: props.processData?.doing || 0,
              name: i18n.t('进行中'),
            },
          ],
        },
      ],
    }
  } else {
    return pieOptions
  }
})

const delayDoneChartOption = computed<EChartsOption>(() => {
  if (props.processData) {
    return {
      title: {
        show: true,
        text: (props.processData?.delayDoneRatio || 0) + '%',
        subtext: i18n.t('逾期完成率'),
        left: '25%',
        top: '37%',
        textAlign: 'center',
        itemGap: 4,
        textStyle: {
          fontSize: '12px',
          color: '#333333',
          fontWeight: 'bold',
        },
        subtextStyle: {
          fontSize: '10px',
          color: '#666666',
          fontWeight: 400,
        },
      },
      color: ['#D9E000', '#E8EC66'],
      series: [
        {
          type: 'pie',
          center: ['28%', '50%'],
          radius: ['60%', '92%'],
          avoidLabelOverlap: false,
          label: {
            position: 'inside',
            formatter: '{c}',
            color: '#fff',
            fontSize: 10,
          },
          emphasis: {
            scale: false,
          },
          data: [
            {
              value: props.processData?.delayDone || 0,
              name: i18n.t('逾期完成'),
            },
            {
              value: Number(props.processData?.done || 0) - Number(props.processData?.delayDone || 0),
              name: i18n.t('按时完成'),
            },
          ],
        },
      ],
    }
  } else {
    return pieOptions
  }
})

const taskChartOption = computed<EChartsOption>(() => {
  if (props.taskData) {
    return {
      color: ['#E8EC66', '#FCBB7B', '#FA8F23'],
      tooltip: {
        valueFormatter: (value: any) => {
          return value + '%'
        },
      },
      series: [
        {
          type: 'pie',
          center: ['28%', '50%'],
          radius: ['60%', '92%'],
          avoidLabelOverlap: false,
          label: {
            position: 'inside',
            formatter: '{c}%',
            color: '#fff',
            fontSize: 10,
          },
          emphasis: {
            scale: false,
          },
          data: [
            {
              value: props.taskData?.earlyDoneRatio || 0,
              name: i18n.t('提前完成率'),
            },
            {
              value: props.taskData?.onTimeRatio || 0,
              name: i18n.t('按时完成率'),
            },
            {
              value: props.taskData?.delayDoneRatio || 0,
              name: i18n.t('逾期完成率'),
            },
          ],
        },
      ],
    }
  } else {
    return pieOptions
  }
})
</script>

<style scoped lang="scss">
.tip-board-container {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
  padding: 16px 24px;
  overflow-x: scroll;
  background: #ffffff;
  box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
  border-radius: 4px;
  .item-box {
    display: flex;
  }
  :deep(.layout-line) {
    position: relative;
    &::after {
      content: '';
      position: absolute;
      right: 0;
      width: 1px;
      height: calc(100% - 48px);
      background-color: #fff;
    }
  }
  .up-rate {
    width: 104px;
  }
}
</style>
