<template>
  <div class="export-template-configuration-container">
    <FSpin :spinning="pageLoading">
      <div class="flex space-between align-items marginB16">
        <span class="title color333 fontSize12">模版信息</span>
        <div class="extra-box">
          <FSpace :size="[8]">
            <FButton v-if="refFiledList?.dataList?.length" size="small" @click="onPreviewFn">预览</FButton>
            <FButton v-if="isDetail" type="primary" size="small" @click="isDetail = !isDetail">编辑</FButton>
            <FButton v-if="!isDetail" size="small" @click="onCloseFn">取消</FButton>
            <FButton v-if="!isDetail" type="primary" size="small" :loading="submitLoading" @click="onSubmitFn"
              >提交</FButton
            >
          </FSpace>
        </div>
      </div>
      <div class="content-box">
        <FForm ref="formRef" :model="fromData" :layout="isDetail ? 'horizontal' : 'vertical'">
          <BaseForm v-model:form-data="fromData" :components="components" :is-detail="isDetail" />
        </FForm>
      </div>
      <div class="line"></div>
    </FSpin>

    <FiledList ref="refFiledList" :templateId="fromData?.id" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, inject, Ref, watch } from 'vue'
import { deepClone } from '@/utils'
import { isNotEmpty } from '@/views/process-operate/components/CustomComponents/BusinessComponent/utils'
import { getByIdExportTemplateConfig, updateByIdExportTemplateConfig, previewTemplateField } from '@/api'
import BaseForm from '@/views/process-class-detail/components/BaseForm/index.vue'
import CustomInput from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomInput/index.vue'
import CustomInputNumber from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomInputNumber/index.vue'
import CustomRadioGroup from '@/views/process-operate/components/CustomComponents/BusinessComponent/CustomRadioGroup/index.vue'
import FiledList from './components/FiledList/index.vue'

const classId = inject<Ref<string>>('classId')
const isDetail = ref<boolean>(true)
const submitLoading = ref<boolean>(false)
const pageLoading = ref<boolean>(false)
const oldFromData = ref<any>({})
const fromData = ref<any>({})
const formRef = ref()
const refFiledList = ref()

const plainOptions = [
  { label: '是', value: 1 },
  { label: '否', value: 0 },
]
const components = reactive<Record<string, any>>({
  baseParams: {
    fieldKey: 'baseParams',
    label: '此处为基础参数，设置isShowComponent为返回false，将不在页面显示',
    isShowComponent: () => false,
    onSubmitValueFormatFn: (config, fromData) => {
      return {
        id: fromData?.id,
        processConfigId: classId.value,
        freezeRow: 0,
      }
    },
  },
  templateName: {
    component: CustomInput,
    dataType: 'input',
    fieldKey: 'templateName',
    label: '数据表头',
    required: true,
    componentAttrs: {
      placeholder: '请输入数据表头',
    },
  },
  fileName: {
    component: CustomInput,
    dataType: 'input',
    fieldKey: 'fileName',
    label: '文件名',
    required: true,
    componentAttrs: {
      placeholder: '请输入文件名',
    },
  },
  freezeCol: {
    component: CustomInputNumber,
    dataType: 'inputNumber',
    fieldKey: 'freezeCol',
    label: '数据冻结（列）',
    required: true,
    componentAttrs: {
      placeholder: '请输入数据冻结（列）',
    },
  },

  isEnabled: {
    component: CustomRadioGroup,
    dataType: 'radio',
    fieldKey: 'isEnabled',
    label: '是否启用',
    componentAttrs: {
      placeholder: '请选择是否启用',
      options: plainOptions,
    },
  },
})

const onPreviewFn = async () => {
  if (!refFiledList?.value?.dataList?.length) return
  const params = Object.assign({}, deepClone(oldFromData.value), { fields: refFiledList?.value?.dataList })
  const res = await previewTemplateField(params)
  if (res.code !== 200) throw new Error(res.msg)
  res?.data && window.open(res?.data)
}

const onCloseFn = () => {
  fromData.value = deepClone(oldFromData.value)
  formRef.value?.resetFields()
  isDetail.value = true
}

const onSubmitFn = async () => {
  await formRef?.value?.validate()
  submitLoading.value = true
  try {
    const params = Object.entries(components).reduce((acc, [key, config]) => {
      if (config?.onSubmitValueFormatFn) {
        Object.assign(acc, config?.onSubmitValueFormatFn(config, fromData.value))
      } else if (isNotEmpty(fromData.value[key])) {
        acc[key] = fromData.value[key]
      }
      return acc
    }, {})
    await updateByIdExportTemplateConfig(params as any)
    await onGetByIdExportTemplateConfig()
    isDetail.value = true
  } finally {
    submitLoading.value = false
  }
}

const onGetByIdExportTemplateConfig = async () => {
  if (!classId.value) {
    throw new Error('流程配置类ID不能为空')
  }
  try {
    pageLoading.value = true
    const res = await getByIdExportTemplateConfig(classId.value)
    if (res.code !== 200) throw new Error(res.msg)
    oldFromData.value = res?.data ?? {}
  } finally {
    pageLoading.value = false
  }
}

watch(
  () => oldFromData.value,
  newVal => {
    fromData.value = deepClone(newVal)
  },
  { immediate: true, deep: true }
)

onMounted(() => {
  onGetByIdExportTemplateConfig()
})
</script>

<style lang="scss" scoped>
.export-template-configuration-container {
  .line {
    margin: 16px 0;
    border-bottom: 1px dashed #eee;
  }
}
.card-content-header {
  padding: 16px 24px;
}
.flex {
  display: flex;
  align-items: center;
}
.space-between {
  justify-content: space-between;
}
.align-items {
  align-items: center;
}
.padding24 {
  padding: 24px;
}
</style>
