<template>
  <div class="task-container">
    <FormRender
      ref="formRef"
      :id="(props.task.formKey as unknown as number)"
      :type="TFFormType()"
      :data="TFFormData(props.task)"
    />
    <!-- 附件 -->
    <div class="milepost-files-tiling-list" v-if="toRaw(props.task).formData?.file">
      <div class="milepost-files-item" v-for="fileItem in toRaw(props.task).formData?.file" :key="fileItem.url">
        <p>
          {{ i18n.t('文件名：') }}<a @click.stop="download(fileItem.url, fileItem.name)">{{ fileItem.name }}</a>
        </p>
        <p>{{ i18n.t('文件大小：') }}{{ (fileItem.size / 1024 / 1024).toFixed(2) }} MB</p>
      </div>
    </div>
    <div class="button-container marginB24" v-if="isEditPower">
      <FButton type="primary" @click="onSubmit(props.task)" style="margin-left: 16px">{{ i18n.t('提交') }}</FButton>
      <FButton style="margin-left: 16px; margin-right: 11px" @click="onSave(props.task)" class="grey-btn">{{
        i18n.t('保存')
      }}</FButton>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { EmitType } from '@/views/process-detail/config'
import FormRender from '@/components/FormRender/index.vue'
import { ITask } from '@/types/handle'
import { computed, inject, markRaw, ref, toRaw } from 'vue'
import { messageInstance as message } from '@fs/smart-design'
import { download, useI18n } from '@/utils'
import dayjs from 'dayjs'

interface IProps {
  task: ITask
}

const editStatus: number[] = [0, 1, 4]
const i18n = useI18n()
const props = defineProps<IProps>()
const formRef = ref()
const processId = inject<number>('processId') // 流程 id
const milestoneOperate = inject('milestoneOperate') as (key: keyof typeof EmitType, data: unknown) => void
const isEditPower = computed(() => {
  return editStatus.includes(props.task.status) && props.task.superviserRole == 1
})

// 提交任务
const onSubmit = async (task: ITask) => {
  try {
    const scoped = formRef.value?.getAmisScoped()
    const $form = scoped?.getComponentByName('page.taskForm')
    const formData = await $form?.submit()

    const data: Record<string, any> = { id: task.id, approverUuid: task.approverUuid }
    // 有表单数据的时候
    if (formData) {
      data.formData = { ...formData }
      let file = toRaw(props.task)?.formData?.file || []
      file && !Array.isArray(file) && (file = JSON.parse(`[${file}]`))
      formData.file && file.push(...JSON.parse(`[${formData.file}]`))
      data.formData.file = file.reduce((fileList: any[], cur: any) => {
        !fileList.find((item: any) => item.url === cur.url) && fileList.push(cur)
        return fileList
      }, [])
    }
    !data.approverUuid &&
      (data.isDelayStatus =
        task?.invalid === 1 && task?.isDelayReason === 1 && task?.status === 1 && dayjs().isAfter(task?.forcastTime))
    milestoneOperate(EmitType.handleTask, data)
  } catch (error: any) {
    message.error(error.message)
  }
}

// 保存任务
const onSave = async (task: ITask) => {
  try {
    const scoped = formRef.value?.getAmisScoped()
    const $form = scoped?.getComponentByName('page.taskForm')
    const formData = await $form?.submit()

    const data: Record<string, any> = { id: task.id }
    // 有表单数据的时候
    if (formData) {
      data.formData = { ...formData }
      let file = toRaw(props.task)?.formData?.file || []
      file && !Array.isArray(file) && (file = JSON.parse(`[${file}]`))
      formData.file && file.push(...JSON.parse(`[${formData.file}]`))
      data.formData.file = file.reduce((fileList: any[], cur: any) => {
        !fileList.find((item: any) => item.url === cur.url) && fileList.push(cur)
        return fileList
      }, [])
    }

    milestoneOperate(EmitType.saveTask, data)
  } catch (error: any) {
    message.error(error.message)
  }
}

// 获取表单类型
const TFFormType = () => {
  if (isEditPower.value) return 'edit'
  return 'view'
}

const TFFormData = (taskData: ITask) => {
  const {
    creator,
    createdTime,
    contentData,
    formData,
    children,
    forcastTime,
    taskCompletedTime,
    taskStartTime,
    updatedUser,
    updatedTime,
    ...data
  } = taskData
  return markRaw({ envData: { instanceId: processId, ...data }, envDefaultFormData: formData })
}
</script>

<style lang="scss" scoped>
.task-container {
  .button-container {
    display: flex;
    justify-content: flex-end;
  }
  .milepost-files-tiling-list {
    display: flex;
    padding: 12px 0;
    margin: 0 12px;

    > .milepost-files-item {
      display: flex;
      padding: 12px 8px;
      margin-right: 10px;
      justify-content: space-between;
      flex-direction: column;
      box-shadow: 0 0 10px 0 #dfe2e6;

      > p {
        margin: 0;
        padding: 0;
        font-size: 12px;
        color: #666;
      }
    }
  }
}
</style>
