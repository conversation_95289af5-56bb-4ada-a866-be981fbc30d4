<template>
  <div className="trigger-list-container">
    <div class="bread-container">
      <Breadcrumb :data="title" />
    </div>
    <trigger-search
      :process-type-list="processTypeList"
      :create-user-list="createUserList"
      @search="onSearch"
    ></trigger-search>
    <trigger-list :searchParams="searchParams" :process-type-list="processTypeList"></trigger-list>
  </div>
</template>

<script lang="ts" setup>
import TriggerSearch from '@/views/trigger/components/trigger-search.vue'
import TriggerList from '@/views/trigger/components/trigger-list.vue'
import { processTye, triggerListSearch } from '@/types/excutionListModel'
import { onMounted, ref, computed } from 'vue'
import { getProcessTypeListNew } from '@/api/automotion'
import { messageInstance as message } from '@fs/smart-design'
import { getAllUsers } from '@/api'
import { IUser } from '@/types/handle'
import Breadcrumb from '@/views/process-list-one/components/Breadcrumb/index.vue'
import { useI18n } from '@fs/i18n'
const processTypeList = ref<processTye[]>([])
const createUserList = ref<IUser[]>([])
const i18n = useI18n()
let searchParams = ref<triggerListSearch>({})
const onSearch = function (params: triggerListSearch) {
  searchParams.value = params
}
const title = computed(() => [i18n.t('首页'), i18n.t('流程自动化')])
const getProcessList = async () => {
  const res = await getProcessTypeListNew()
  if (res.code === 200) {
    processTypeList.value = res.data
  } else {
    message.warn(res.msg)
  }
}
const getCreatUserList = async () => {
  const res = await getAllUsers()
  if (res.code == 200) {
    createUserList.value = res.data
  }
}
onMounted(async () => {
  await getProcessList()
  await getCreatUserList()
})
</script>

<style lang="scss" scoped>
.trigger-list-container {
  padding: 4px;
  .bread-container {
    margin-bottom: 24px;
  }
}
</style>
