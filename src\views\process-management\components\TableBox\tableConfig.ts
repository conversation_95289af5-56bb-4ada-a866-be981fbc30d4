import { computed } from 'vue'
import i18n from '@/i18n'

export const columns = computed<any[]>(() => [
  {
    title: i18n.t('需求编码'),
    dataIndex: 'processInstanceCode',
    key: 'processInstanceCode',
    width: 180,
  },
  {
    title: i18n.t('当前进度'),
    dataIndex: 'node',
    key: 'node',
    width: 180,
  },
  {
    title: i18n.t('产品线'),
    dataIndex: 'productLine',
    key: 'productLine',
    width: 110,
  },
  {
    title: i18n.t('需求等级'),
    dataIndex: 'isUrgent',
    key: 'isUrgent',
    sorter: true,
    width: 140,
  },
  {
    title: i18n.t('周期评级'),
    dataIndex: 'cycleRating',
    key: 'cycleRating',
    width: 140,
  },
  {
    title: i18n.t('需求类型'),
    dataIndex: 'source',
    key: 'source',
    width: 122,
  },
  {
    title: i18n.t('需求来源'),
    dataIndex: 'demand',
    key: 'demand',
    width: 110,
  },
  {
    title: i18n.t('需求场景'),
    dataIndex: 'scene',
    key: 'scene',
    width: 242,
  },
  {
    title: i18n.t('需求描述'),
    dataIndex: 'what',
    key: 'what',
    width: 270,
  },
  {
    title: i18n.t('需求原因'),
    dataIndex: 'why',
    key: 'why',
    width: 171,
  },
  {
    title: i18n.t('验收标准'),
    dataIndex: 'how',
    key: 'how',
    width: 170,
  },
  {
    title: i18n.t('需求生命特征'),
    dataIndex: 'when',
    key: 'when',
    width: 170,
  },
  {
    title: i18n.t('期望需求完成时间'),
    dataIndex: 'expectedTime',
    key: 'expectedTime',
    width: 170,
  },
  {
    title: i18n.t('需求截图/资料'),
    dataIndex: 'zlfile',
    key: 'zlfile',
    width: 170,
  },
  {
    title: i18n.t('客户信息'),
    dataIndex: 'customersIndustry',
    key: 'customersIndustry',
    width: 114,
  },
  {
    title: i18n.t('关联产品型号'),
    dataIndex: 'products',
    key: 'products',
    width: 170,
  },
  {
    title: i18n.t('竞争者信息'),
    dataIndex: 'competitor',
    key: 'competitor',
    width: 170,
  },
  {
    title: i18n.t('创建人/创建时间'),
    dataIndex: 'creatorName',
    key: 'creatorName',
    width: 170,
  },
  {
    title: i18n.t('是否承接需求'),
    dataIndex: 'accept',
    key: 'accept',
    width: 110,
  },
  {
    title: i18n.t('需求分析总结'),
    dataIndex: 'sumUp',
    key: 'sumUp',
    width: 170,
  },
  {
    title: i18n.t('需求分发'),
    dataIndex: 'distribute',
    key: 'distribute',
    width: 170,
  },
  {
    title: i18n.t('关联流程进度'),
    dataIndex: 'relevanceList',
    key: 'relevanceList',
    width: 220,
  },
  {
    title: i18n.t('需求分析总结'),
    dataIndex: 'sumUp',
    key: 'sumUp',
    width: 170,
  },
  {
    title: i18n.t('验收是否通过'),
    dataIndex: 'adopt',
    key: 'adopt',
    width: 170,
  },
  {
    title: i18n.t('需求验收评分'),
    dataIndex: 'score',
    key: 'score',
    width: 170,
  },
  {
    title: i18n.t('需求验收总结'),
    dataIndex: 'acceptanceResults',
    key: 'acceptanceResults',
    width: 170,
  },
  {
    title: i18n.t('操作'),
    dataIndex: 'handle',
    key: 'handle',
    fixed: 'right',
    width: 112,
  },
])
