import type { TableColumnsType } from '@fs/smart-design/dist/ant-design-vue_es'
import { VNode, h } from 'vue'
import i18n from '@/i18n'

export const columns: TableColumnsType = [
  {
    title: i18n.t('项目总数量'),
    dataIndex: 'projectTotal',
    sorter: true,
    width: 116,
  },
  {
    title: i18n.t('进行中'),
    dataIndex: 'doing',
    sorter: true,
    width: 140,
  },
  {
    title: i18n.t('已完成'),
    dataIndex: 'doen',
    sorter: true,
    width: 140,
  },
  {
    title: i18n.t('已延期'),
    dataIndex: 'delay',
    sorter: true,
    width: 146,
  },
  {
    title: i18n.t('已办结'),
    dataIndex: 'transactDone',
    sorter: true,
    width: 160,
  },
  {
    title: i18n.t('平均计划周期'),
    dataIndex: 'avgPlanPeriod',
    sorter: true,
    width: 146,
  },
  {
    title: i18n.t('平均实际周期'),
    dataIndex: 'avgRealityPeriod',
    sorter: true,
    width: 120,
  },
  {
    title: i18n.t('计划偏差度'),
    dataIndex: 'planDepartureDegree',
    width: 120,
  },
  {
    title: i18n.t('计划偏差占比'),
    dataIndex: 'planDepartureProportion',
    customRender: ({ text, value, record }): VNode => {
      return h('span', text + '%')
    },
    width: 120,
  },
  {
    title: i18n.t('延期率'),
    dataIndex: 'delayRate',
    customRender: ({ text, value, record }): VNode => {
      return h('span', (record.delay / record.projectTotal).toFixed(2) + '%')
    },
    width: 120,
  },
]
