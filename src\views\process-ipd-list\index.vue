<template>
  <div class="process-ipd-list">
    <Breadcrumb class="none-container-padding" />
    <div class="card-content-shadow pt24 pl24 pr24 mb16">
      <SearchContent v-model:query-data="queryData" />
    </div>
    <div class="card-content-shadow pt24 pl24 pr24 mb24">
      <div class="flex space-between mb16">
        <div class="fw-500 f14">IPD流程列表</div>
        <FSpace :size="[12]" wrap class="handle-row-box">
          <FButton type="primary" class="mr6" @click="HandleProcessFn({ key: NEW_IPD_CONFIG_ID })">
            <template #icon><i class="iconfont icontubiao_tianjia1" style="margin-right: 4px" /></template>
            创建流程</FButton
          >
          <!-- <component :is="columnsConfig.Operation"></component> -->
        </FSpace>
      </div>
      <FTable
        class="table-warp"
        :columns="columnsConfig.tableColumns"
        :loading="loading"
        :data-source="dataList"
        :row-key="(data:any) => data.id"
        :row-selection="rowSelection"
        :sticky="{ offsetHeader: -20 }"
        :scroll="{ x: 'min-content' }"
        :pagination="{
          total: paging.total,
          current: paging.pageNum,
          pageSize: paging.pageSize,
          showTotal: (total: number) => `共${total}条`,
          showQuickJumper: true,
          showSizeChanger: true,
          onChange: onPaginationChangeFn
        }"
        :customRow="getCustomRow"
        :components="tableComponents"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'creatorInfo'">
            <div class="label-box">
              <span class="label">[录入时间]</span>
              <span>{{
                (record?.createdTime && transformDate(record?.createdTime, 'YYYY-MM-DD HH:mm:ss')) ?? '--'
              }}</span>
              <span class="marginL4 code-link" @click="logModalRef?.onOpenFn(record?.id)">查看日志</span>
            </div>
            <div class="label-box">
              <span class="label">[预计完成时间]</span>
              <span>{{
                (record?.forcastTime && transformDate(record?.forcastTime, 'YYYY-MM-DD HH:mm:ss')) ?? '--'
              }}</span>
            </div>
          </template>
          <template v-if="column.dataIndex === 'mmInfo'">
            <div class="label-box">
              <span class="label">[MM名称]</span>
              <span>{{ record?.mm?.topicName ?? '--' }}</span>
            </div>
            <div class="label-box">
              <span class="label">[MM项目编码]</span>
              <span :class="[record?.mm?.id ? 'code-link' : '']" @click="onJumpDemandDetial(record?.mm ?? {})">{{
                record?.mm?.processInstanceCode ?? '--'
              }}</span>
            </div>
            <div class="label-box">
              <span class="label">[MM录入人]</span>
              <span>{{ record?.mm?.creatorName ?? '--' }}</span>
            </div>
            <div class="label-box">
              <span class="label">[IPD流程名称]</span>
              <span>{{ record?.topicName ?? '--' }}</span>
            </div>
            <div class="label-box">
              <span class="label">[IPD流程编号]</span>
              <span class="code-link" @click="onJumpDemandDetial(record)">{{
                record?.processInstanceCode ?? '--'
              }}</span>
            </div>
          </template>
          <template v-if="column.dataIndex === 'handleKey'">
            <FDropdown trigger="click" destroy-popup-on-hide :overlay-style="{ minWidth: 'auto' }">
              <TipBtn tip-title="创建流程">
                <i class="iconfont icontubiao_shenhe1 hover-btn"></i>
              </TipBtn>
              <template #overlay>
                <FMenu @click="HandleProcessFn">
                  <FMenuItem :key="IR_CONFIG_ID">创建IR流程</FMenuItem>
                  <FMenuItem :key="SR_CONFIG_ID">创建SR流程</FMenuItem>
                  <FMenuItem :key="AR_CONFIG_ID">创建AR流程</FMenuItem>
                  <FMenuItem :key="MM_CONFIG_ID">创建测试流程</FMenuItem>
                </FMenu>
              </template>
            </FDropdown>
          </template>
          <template v-if="['solution'].includes(column.dataIndex)">
            <ExpandableTree
              v-if="(record?.solutionLists ?? [])?.length"
              :ref="el => (record.solutionTreeRef = el)"
              :items="record?.solutionLists ?? []"
            >
              <template #label="{ item }">
                <span>{{ item?.solutionCode ?? '--' }}</span
                >/
                <span class="code-link" @click="onOpenDocument($event, item)">{{ item?.solutionName ?? '--' }}</span>
              </template>
              <template #default="{ item }">
                <span class="marginR4">[{{ item?.type ?? '--' }}]</span>
                <span>{{ item?.businessCode ?? '--' }}</span
                >/
                <span>{{ item?.description || '--' }}</span>
              </template>
            </ExpandableTree>
            <span v-else>--</span>
          </template>
          <template v-if="['irInfo'].includes(column.dataIndex)">
            <CountInfo :data="record?.ir ?? {}" :processInstanceCode="record?.processInstanceCode" />
            <AppraiseInfo class="mt8" :data="record?.trOne ?? {}" title="TR1评审" />
          </template>
          <template v-if="['srInfo'].includes(column.dataIndex)">
            <CountInfo :data="record?.sr ?? {}" :processInstanceCode="record?.processInstanceCode" />
            <AppraiseInfo class="mt8" :data="record?.trTwo ?? {}" title="TR2评审" />
          </template>
          <template v-if="['arInfo'].includes(column.dataIndex)">
            <CountInfo :data="record?.ar ?? {}" :processInstanceCode="record?.processInstanceCode" />
            <AppraiseInfo class="mt8" :data="record?.trThree ?? {}" title="TR3评审" />
            <AppraiseInfo class="mt8" :data="record?.pdcp ?? {}" title="PDCP评审" />
          </template>
          <template v-if="['storyInfo'].includes(column.dataIndex)">
            <CountInfo :data="record?.ir ?? {}" :processInstanceCode="record?.processInstanceCode" />
            <AppraiseInfo class="mt8" :data="record?.trFour ?? {}" title="TR4评审" />
          </template>
          <template v-if="['sdv'].includes(column.dataIndex)">
            <RelateProcess v-for="item in record?.sdv ?? []" :data="item" :key="item?.id" />
            <AppraiseInfo class="mt8" :data="record?.trFourA ?? {}" title="TR4A评审" />
          </template>
          <template v-if="['sit'].includes(column.dataIndex)">
            <RelateProcess v-for="item in record?.sit ?? []" :data="item" :key="item?.id" />
            <AppraiseInfo class="mt8" :data="record?.trFive ?? {}" title="TR5评审" />
          </template>
          <template v-if="['svt'].includes(column.dataIndex)">
            <ExpandableTree :ref="el => (record.svtTreeRef = el)" :items="record?.svtInfo ?? []">
              <template #default="{ item }">
                <RelateProcess :data="item ?? {}" />
              </template>
            </ExpandableTree>
            <AppraiseInfo class="mt8" :data="record?.trSix ?? {}" title="TR6评审" />
            <AppraiseInfo class="mt8" :data="record?.trAdcp ?? {}" title="ADCP评审" />
          </template>
        </template>
      </FTable>
    </div>
    <LogModal ref="logModalRef" />
  </div>
</template>
<script setup lang="ts">
import { ref, computed, watch, reactive, h } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { FButton, message } from '@fs/smart-design'
import Breadcrumb from '@/components/Breadcrumb/index.vue'
import SearchContent from './components/SearchContent/index.vue'
import CountInfo from './components/CountInfo/index.vue'
import AppraiseInfo from './components/AppraiseInfo/index.vue'
import RelateProcess from './components/RelateProcess/index.vue'
import ExpandableTree from './components/ExpandableTree/index.vue'
import LogModal from './components/LogModal/index.vue'
import { tableColumnKeys, useTableColumn } from '@/components/TableOperation/index'
import TipBtn from '@/views/message-template/components/TipBtn/index.vue'
import { getIpdProcessList, ProcessIpdListParams } from '@/api'
import { getByIdProcessConfig } from '@/api/processManagement'
import {
  cache,
  transformDate,
  jumpToDemand,
  NEW_IPD_CONFIG_ID,
  MM_CONFIG_ID,
  IR_CONFIG_ID,
  SR_CONFIG_ID,
  AR_CONFIG_ID,
} from '@/utils'

const { currentRoute, resolve } = useRouter()
const route = useRoute()
const routerName = computed<any>(() => currentRoute.value?.name)
const loading = ref(false)
const paging = reactive<any>({ pageNum: 1, pageSize: 10, total: 0 })
const dataList = ref<any[]>([])
const selectedKeys = ref<string[]>([])
const rowSelection = computed(() => ({
  selectedRowKeys: selectedKeys,
  onChange: (selectedRowKeys: string[]) => (selectedKeys.value = selectedRowKeys),
}))
const columnsConfig = reactive(
  useTableColumn(
    [
      { title: '录入时间/操作日志', dataIndex: 'creatorInfo', key: 'creatorInfo', width: 260 },
      { title: 'MM项目编号', dataIndex: 'mmInfo', key: 'mmInfo', width: 260 },
      { title: '操作', dataIndex: 'handleKey', key: 'handleKey', width: 80 },
      { title: '一条龙经理', dataIndex: 'pdOwner', key: 'pdOwner', width: 110 },
      { title: 'Solution ID/SKUID/BOMID', dataIndex: 'solution', key: 'solution', width: 260 },
      { title: 'IR概要设计', dataIndex: 'irInfo', key: 'irInfo', width: 210 },
      { title: 'SR概要设计', dataIndex: 'srInfo', key: 'srInfo', width: 210 },
      { title: 'AR详细设计', dataIndex: 'arInfo', key: 'arInfo', width: 210 },
      { title: 'story状态/描述（开发与测试）', dataIndex: 'storyInfo', key: 'storyInfo', width: 210 },
      { title: '系统设计验证/描述(SDV测试)', dataIndex: 'sdv', key: 'sdv', width: 260 },
      { title: 'SIT测试', dataIndex: 'sit', key: 'sit', width: 260 },
      { title: 'SVT及实验局认证', dataIndex: 'svt', key: 'svt', width: 300 },
      { title: 'SKUID/描述(项目完结)', dataIndex: 'skuList', key: 'skuList', width: 210 },
    ],
    tableColumnKeys.processListTable_ipd
  )
)
const queryData = ref<ProcessIpdListParams>({})
const logModalRef = ref()

const getCustomRow = (record, index) => {
  return {
    record,
    index,
  }
}

const onOpenDocument = (e: MouseEvent, item: any) => {
  e.stopPropagation()
  if (item?.documentLink) {
    window.open(item?.documentUrl, '_blank')
  } else {
    message.error('文档不存在')
  }
}

// 需求详情跳转
const onJumpDemandDetial = (record: any) => {
  record?.id && jumpToDemand(record.id, undefined, true)
}

const handleExpand = (record: any) => {
  const flag = !record?.expanded
  if (flag) {
    record?.svtTreeRef?.expandAll()
    record?.solutionTreeRef?.expandAll()
  } else {
    record?.svtTreeRef?.collapseAll()
    record?.solutionTreeRef?.collapseAll()
  }
  record.expanded = flag
}

const tableComponents = reactive({
  body: {
    row: (rowProps, { slots: defaultSlots }) => {
      const defaultTds = defaultSlots.default?.() || []
      const extraTd = h(
        'div',
        { class: 'custom-more-cell-container' },
        h(
          'span',
          { class: 'custom-more-cell', onClick: () => handleExpand(rowProps?.record) },
          h('i', { class: ['iconfont', (rowProps?.record?.expanded && 'icontubiao_shanchu') || 'icontubiao_tianjia'] })
        )
      )
      return h(
        'tr',
        {
          class: 'custom-more-row-container',
        },
        [...defaultTds, (rowProps?.record?.hasExpanded && extraTd) || undefined]
      )
    },
  },
})

const goCreatePage = (id, processDefineKey) => {
  const { href } = resolve({
    name: 'DemandAdd',
    params: {
      id: id,
      processDefineKey: processDefineKey,
    },
    query: {
      localName: route.name as string,
    },
  })
  window.open(href, '_blank')
}

const HandleProcessFn = async ({ key }) => {
  if (!key) return
  const { data = {} } = (await getByIdProcessConfig(key)) as any
  data?.processDefineKey && goCreatePage(key, data?.processDefineKey)
}

// 查询列表
const queryDataList = async () => {
  try {
    loading.value = true
    const data = { ...queryData.value }
    cache.set(
      routerName?.value,
      JSON.stringify({
        ...(data?.cacheValue ?? {}),
        pageNum: paging.pageNum,
        pageSize: paging.pageSize,
      })
    )
    delete data.cacheValue
    const res = await getIpdProcessList(data, paging)
    dataList.value = (res?.data?.list || []).map((item: any) => {
      return {
        ...item,
        svtInfo: [
          {
            id: 1,
            label: '系统验证测试',
            children: item?.svt ?? [],
          },
          {
            id: 2,
            label: 'beta实验局测试编号/描述(项目验证)',
            children: item?.stest ?? [],
          },
        ],
        solutionLists: (item?.solutionList ?? []).map((solution: any) => {
          return {
            ...solution,
            children: solution?.skuProductCatalogList,
          }
        }),
        hasExpanded: (item?.svt ?? [])?.length || (item?.stest ?? [])?.length || (item?.solutionList ?? [])?.length,
        expanded: false,
      }
    })
    paging.total = res?.data?.totalCount || 0
  } finally {
    selectedKeys.value = []
    loading.value = false
  }
}

const onPaginationChangeFn = (current: number, pageSize: number) => {
  paging.pageNum = current
  paging.pageSize = pageSize
  queryDataList()
}

// 查询列表
const onGetSearchData = (data: any) => {
  queryData.value = data
  paging.pageNum = data?.cacheValue?.pageNum || 1
  paging.pageSize = data?.cacheValue?.pageSize || 10
  queryDataList()
}

watch(
  () => queryData.value,
  val => {
    onGetSearchData(val)
  },
  { deep: true }
)
</script>
<style scoped lang="scss">
.process-ipd-list {
  .none-container-padding {
    margin-top: -20px;
    margin-left: -20px;
    width: calc(100% + 40px);
  }
  .card-content-shadow {
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
    border-radius: 4px;
  }
  .flex {
    display: flex;
    align-items: center;
  }
  .space-between {
    justify-content: space-between;
  }
  .code-link {
    cursor: pointer;
    color: #378eef;
  }
  .label-box {
    display: flex;
    color: #333;
    .label {
      margin-right: 4px;
      color: #999;
      white-space: nowrap;
    }
  }
  .mr6 {
    margin-right: 6px;
  }
  .mt8 {
    margin-top: 8px;
  }
  .hover-btn {
    color: #378eef;
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    &:hover {
      background-color: #d8d8d8;
    }
  }
  .count-info-content {
    line-height: 18px;
  }
  :deep(.custom-more-row-container) {
    position: relative;
    &:hover {
      .custom-more-cell-container {
        visibility: visible;
      }
    }
    .custom-more-cell-container {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 12px;
      visibility: hidden;
      .custom-more-cell {
        position: sticky;
        left: 50%;
        display: flex;
        width: 48px;
        height: 12px;
        justify-content: center;
        align-items: center;
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
        background: #eeeeee;
        color: #979797;
        cursor: pointer;
        &:hover {
          background: #d7e8fb;
          color: #378eef;
        }
        .iconfont {
          font-size: 5px;
        }
      }
    }
  }
  :deep(.fs-table-body) {
    .fs-table-cell {
      &:empty {
        &::before {
          content: '--';
        }
      }
    }
  }
}
</style>
