import { createI18n } from '@fs/i18n'
import { getLanguage, getToken, APP_ENV } from '@/utils'

const POWER_ENV = ['production', 'compliance'].includes(APP_ENV) ? 'prod' : 'test'

const i18n = createI18n({
  env: POWER_ENV,
  path: 'BPM',
  language: getLanguage() || 'zh-CN',
  token: () => {
    const token = getToken()
    return (token.startsWith('Bearer') && token.replace('Bearer ', '')) || token
  },
})

// 微前端环境下，国际化语言变化监听
if (window.__MICRO_APP_ENVIRONMENT__) {
  // 监听基座下发的数据变化
  window.microApp.addDataListener((data: { path: string; language: string }) => {
    if (data.language) {
      i18n.setLanguage(data.language)
    }
  })
}
export default i18n
