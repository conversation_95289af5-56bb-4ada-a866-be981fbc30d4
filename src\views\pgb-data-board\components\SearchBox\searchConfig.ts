import { transformDate } from '@/utils'
import { getPgbScreened } from '@/api/pgbDataBoard'
import { messageInstance as message } from '@fs/smart-design'
import { ProcessListParams, IOptions } from '@/types/pgbDataBoard'
import { reactive, computed } from 'vue'
import i18n from '@/i18n'

export const getPgbScreenedList = await (async () => {
  const res = await getPgbScreened()
  let data: any = []
  if (res.code === 200) {
    data = res.data || []
  } else {
    message.warn(res.msg)
  }
  return data
})()

export class Search {
  options: IOptions;
  [key: string]: any
  constructor(emits: any) {
    this.emits = emits
    this.options = reactive({
      processConfigId: {
        componentName: 'FSelect',
        componentValueKey: 'processConfigId',
        componentValue: undefined,
        componentAttrs: {
          style: {
            width: '120px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          placeholder: computed(() => i18n.t('全部')),
          pressLine: computed(() => i18n.t('流程类型')),
          fieldNames: { label: 'processConfigName', value: 'processConfigId' },
          showSearch: true,
          optionFilterProp: 'processConfigName',
          options: getPgbScreenedList || [],
          allowClear: true,
          onChange: (value: any, option: any) => {
            this.options.type.componentAttrs.options = []
            this.options.bu.componentAttrs.options = []
            this.options.dt.componentAttrs.options = []

            this.options.type.componentValue = undefined
            this.options.bu.componentValue = undefined
            this.options.dt.componentValue = undefined
            this.options.nodeId.componentValue = undefined
            this.options.nodeId.componentAttrs.options = option?.nodes || []
            switch (option?.processConfigName || '') {
              case 'IPD流程':
                this.options.type.componentAttrs.options = option.dictionarys.filter(
                  (item: any) => item.field === 'source'
                )
                this.options.bu.componentAttrs.options = option.dictionarys.filter((item: any) => item.field === 'pbu')
                this.options.dt.componentAttrs.options = option.dictionarys.filter((item: any) => item.field === 'type')
                break
              case '1-N流程':
                this.options.type.componentAttrs.options = option.dictionarys.filter(
                  (item: any) => item.field === 'type'
                )
                this.options.dt.componentAttrs.options = option.dictionarys.filter(
                  (item: any) => item.field === 'business'
                )
                break
              case 'PBG项目管理流程':
                this.options.type.componentAttrs.options = option.dictionarys.filter(
                  (item: any) => item.field === 'pbg_demand'
                )
                this.options.bu.componentAttrs.options = option.dictionarys.filter(
                  (item: any) => item.field === 'pbg_pbu'
                )
                break
            }
            this.emits('onGetSearchData', this.submit())
          },
        },
      },
      type: {
        componentName: 'FSelect',
        componentValueKey: 'type',
        componentValue: undefined,
        componentAttrs: {
          style: {
            width: '120px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          placeholder: computed(() => i18n.t('全部')),
          pressLine: computed(() => i18n.t('需求类型')),
          options: [],
          allowClear: true,
          onChange: () => {
            this.emits('onGetSearchData', this.submit())
          },
        },
      },
      isUrgent: {
        componentName: 'FSelect',
        componentValueKey: 'isUrgent',
        componentValue: undefined,
        componentAttrs: {
          style: {
            width: '120px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          placeholder: computed(() => i18n.t('全部')),
          pressLine: computed(() => i18n.t('紧急程度')),
          options: computed(() => [
            { label: i18n.t('一般'), value: 0 },
            { label: i18n.t('加急'), value: 1 },
          ]),
          allowClear: true,
          onChange: () => {
            this.emits('onGetSearchData', this.submit())
          },
        },
      },
      dt: {
        componentName: 'FSelect',
        componentValueKey: 'dt',
        componentValue: undefined,
        componentAttrs: {
          style: {
            width: '120px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          placeholder: computed(() => i18n.t('全部')),
          pressLine: computed(() => i18n.t('所属DT')),
          options: [],
          allowClear: true,
          onChange: () => {
            this.emits('onGetSearchData', this.submit())
          },
        },
      },
      status: {
        componentName: 'FSelect',
        componentValueKey: 'status',
        componentValue: undefined,
        componentAttrs: {
          style: {
            width: '120px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          placeholder: computed(() => i18n.t('全部')),
          pressLine: computed(() => i18n.t('项目状态')),
          options: computed(() => [
            { label: i18n.t('进行中'), value: 0 },
            { label: i18n.t('已完成'), value: 1 },
            { label: i18n.t('已办结'), value: 2 },
          ]),
          allowClear: true,
          onChange: () => {
            this.emits('onGetSearchData', this.submit())
          },
        },
      },
      bu: {
        componentName: 'FSelect',
        componentValueKey: 'bu',
        componentValue: undefined,
        componentAttrs: {
          style: {
            width: '120px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          placeholder: computed(() => i18n.t('全部')),
          pressLine: computed(() => i18n.t('所属BU')),
          options: [],
          allowClear: true,
          onChange: () => {
            this.emits('onGetSearchData', this.submit())
          },
        },
      },
      nodeId: {
        componentName: 'FSelect',
        componentValueKey: 'nodeId',
        componentValue: undefined,
        componentAttrs: {
          style: {
            width: '120px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          placeholder: computed(() => i18n.t('全部')),
          pressLine: computed(() => i18n.t('项目节点')),
          fieldNames: { label: 'milepostName', value: 'nodeId' },
          options: [],
          allowClear: true,
          onChange: () => {
            this.emits('onGetSearchData', this.submit())
          },
        },
      },
      isDelay: {
        componentName: 'FSelect',
        componentValueKey: 'isDelay',
        componentValue: undefined,
        componentAttrs: {
          style: {
            width: '120px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          placeholder: computed(() => i18n.t('全部')),
          pressLine: computed(() => i18n.t('是否延期')),
          options: computed(() => [
            { label: i18n.t('不延期'), value: 0 },
            { label: i18n.t('延期'), value: 1 },
          ]),
          allowClear: true,
          onChange: () => {
            this.emits('onGetSearchData', this.submit())
          },
        },
      },
      time: {
        componentName: 'FRangePicker',
        componentValueKey: 'time',
        componentValue: undefined,
        // componentLabel: i18n.t('预计完成时间'),
        componentAttrs: {
          style: {
            width: '240px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          pressLine: computed(() => i18n.t('预计完成时间')),
          placeholder: computed(() => [i18n.t('开始日期'), i18n.t('结束日期')]),
          onChange: () => {
            this.emits('onGetSearchData', this.submit())
          },
        },
        getComponentValueFormat: (value: any) => {
          if (!value || value.length !== 2) return undefined
          return { startTime: transformDate(value[0], 'YYYY-MM-DD'), endTime: transformDate(value[1], 'YYYY-MM-DD') }
        },
      },
      queryInput: {
        componentName: 'FInput',
        componentValueKey: 'queryInput',
        componentValue: undefined,
        // componentLabel: i18n.t('快速搜索'),
        componentAttrs: {
          style: {
            width: '240px',
            marginRight: '12px',
            marginBottom: '12px',
          },
          placeholder: computed(() => i18n.t('项目名称/项目编号')),
          pressLine: computed(() => i18n.t('快速搜索')),
          onPressEnter: () => {
            this.emits('onGetSearchData', this.submit())
          },
        },
      },
      clearBtn: {
        componentName: 'span',
        componentAttrs: {
          style: {
            display: 'none',
            height: '32px',
            lineHeight: '32px',
            marginLeft: '4px',
            cursor: 'pointer',
            color: '#378eef',
            fontSize: '12px',
          },
          textContent: computed(() => i18n.t('清空已选')),
          onClick: () => {
            this.clear()
          },
        },
      },
    })
    this.init()
    this.checkClearStatus()
  }

  init() {
    this.emits('onGetSearchData', this.submit())
  }

  checkClearStatus() {
    const clearStatus = Object.values(this.options).some(value => {
      return value.componentValue !== undefined
    })
    this.options.clearBtn.componentAttrs.style.display = clearStatus ? 'inline-block' : 'none'
  }

  submit() {
    const params: ProcessListParams = {}
    Object.values(this.options).forEach(value => {
      ;(value.componentValue &&
        value.getComponentValueFormat &&
        Object.assign(params, value.getComponentValueFormat(value.componentValue))) ||
        (params[value.componentValueKey] = value.componentValue)
    })
    this.checkClearStatus()
    return params
  }

  clear() {
    Object.entries(this.options).forEach(([key, value]) => {
      value.componentValue = undefined
    })
    this.emits('onGetSearchData', this.submit())
  }
}
