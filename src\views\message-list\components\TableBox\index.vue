<template>
  <div class="table-box-container">
    <FTable
      :data-source="list"
      :loading="loading"
      :columns="columns"
      table-layout="fixed"
      :pagination="false"
      :scroll="{ x: '100%' }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'topicName'">
          <span style="cursor: pointer; color: #378eef" @click="onJumpDemandDetial(record)">{{
            record.topicName
          }}</span>
        </template>
        <template v-if="column.dataIndex === 'content'">
          <FTooltip>
            <template #title>
              <div style="max-height: 46vh; overflow-y: scroll">{{ getHTMLSpingtext(record.content) }}</div>
            </template>
            <span class="text-ellipsis">{{ getHTMLSpingtext(record.content) }}</span>
          </FTooltip>
        </template>
        <template v-if="column.dataIndex === 'handle'">
          <FTooltip>
            <template #title>{{ i18n.t('查看详情') }}</template>
            <i
              class="iconfont iconfont-hover cursor color4677C7 icontubiao_chakanxiangqing"
              @click="goMessageInfo(record)"
            ></i>
          </FTooltip>
        </template>
      </template>
    </FTable>
    <!-- 消息协同 -->
    <FDrawer
      class="custom-class"
      :title="i18n.t('消息协同')"
      :width="380"
      :mask-closable="true"
      v-model:visible="messageFlag"
      placement="right"
    >
      <ChatRoom
        :process-no="processInfo.processInstanceCode"
        :instance-id="+processInfo.instanceId"
        :process-name="processInfo.topicName"
        :params-wrapper="paramsWrapper"
      />
    </FDrawer>
  </div>
</template>

<script setup lang="ts">
import { IMessageListItem } from '@/types/request'
import { columns } from './tableConfig'
import { useI18n, getHTMLSpingtext, jumpToDemand } from '@/utils'
import ChatRoom from '@/components/ChatRoom/index.vue'
import { ref } from 'vue'
const i18n = useI18n()

type propsType = {
  list: IMessageListItem[]
  loading: boolean
}
const props = withDefaults(defineProps<propsType>(), {
  list: () => [],
  loading: false,
})

const processInfo = ref<IMessageListItem>({
  processType: '',
  topicName: '',
  processInstanceCode: '',
  nodeName: '',
  nodeId: '',
  instanceId: '',
  content: '',
  sendBy: '',
  receiver: '',
  isRead: '',
  isNew: '',
  sendDate: '',
})
const messageFlag = ref<boolean>(false)
const paramsWrapper = <T>(
  data: T
): T & { processType: string; processInstanceCode: string; instanceTopicName: string } => {
  return {
    ...data,
    processType: processInfo.value.processType,
    instanceTopicName: processInfo.value.topicName,
    processInstanceCode: processInfo.value.processInstanceCode,
  }
}

const goMessageInfo = (record: any) => {
  processInfo.value = record
  messageFlag.value = true
}

const onJumpDemandDetial = (record: any) => {
  jumpToDemand(record.instanceId, record.processConfigId)
}
</script>

<style scoped lang="scss">
.table-box-container {
  padding-top: 12px;
  .text-ellipsis {
    width: 100%;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  :deep(.ant-table-cell) {
    &:empty {
      &::after {
        content: '--';
      }
    }
  }
  :deep(.ant-table-tbody) {
    > tr:hover:not(.ant-table-expanded-row) > td,
    .ant-table-row-hover,
    .ant-table-row-hover > td {
      background: #f1f4f8 !important;
    }
    tr > td {
      background: #fff !important;
    }
    .table-striped > td {
      background: #fbfdff !important;
    }
  }
  :deep(.ant-table-container) {
    &::after {
      box-shadow: none;
    }
  }
  .fs-pagination {
    padding-top: 11px;
  }
  :deep(.popover-select-notes) {
    .ant-popover-inner-content {
      padding: 12px;
    }
  }
}
.message-header-box {
  height: 40px;
  margin: 0px 20px 0;
  border-bottom: 1px solid #eeeeee;
  display: flex;
  align-items: center;
  .title {
    display: flex;
    align-items: center;
    width: 100%;
    margin: 0;
    i {
      display: inline-block;
      width: 14px;
      height: 12px;
      background: url('~@/assets/images/forwarder_detial_title01.png') repeat no-repeat;
      background-size: 100% 100%;
      margin-right: 5px;
      flex-shrink: 0;
    }
    span {
      flex: 1;
      font-size: 12px;
      color: #999999;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
