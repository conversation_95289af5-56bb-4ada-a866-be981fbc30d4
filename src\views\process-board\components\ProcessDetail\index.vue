<template>
  <div class="process-detail-board-container">
    <div class="board-box">
      <div class="board-header">
        <span class="title">{{ i18n.t('项目详细指标') }}</span>
        <div class="board-search">
          <FCascader
            v-model:value="queryData.milepostSuperviser"
            class="cust-select"
            style="width: 220px; margin-right: 12px"
            :placeholder="i18n.t('任务负责人')"
            :options="userList"
            show-arrow
            max-tag-count="responsive"
            multiple="true"
            allow-clear
            show-search
            :field-names="{ label: 'name', value: 'uuid', children: 'departmentChildrens' }"
            showCheckedStrategy="SHOW_CHILD"
            @change="onRoleSearchChangeFn"
          >
          </FCascader>
          <FSelect
            v-model:value="queryData.dimension"
            class="cust-select"
            style="width: 76px"
            placement="bottomRight"
            :placeholder="i18n.t('统计维度')"
            allow-clear
            :options="dimensionList"
            :get-popup-container="(triggerNode: Element) => { return triggerNode.parentNode}"
            @change="onSearchChangeFn"
          >
          </FSelect>
        </div>
      </div>
      <div class="process-board">
        <FSpin size="large" :spinning="processDetailLoading">
          <BaseEchart :option="projectChartOption" height="324px" />
        </FSpin>
      </div>
    </div>
    <div class="table-box">
      <div class="header">
        <span class="title">{{ i18n.t('项目完成情况') }}</span>
        <span class="btn" @click="emits('onExportProcess', queryData)">
          <i class="icontubiao_xiazai iconfont"></i>{{ i18n.t('下载') }}
        </span>
      </div>
      <FTable
        :data-source="processDetailData"
        :loading="processDetailLoading"
        :columns="currentColumns"
        table-layout="fixed"
        :pagination="false"
        :scroll="{ x: '100%', y: '417px' }"
        @change="handleTableChange"
      >
      </FTable>
      <div class="fei-su-pagination">
        <FPagination
          v-model:current="pageData.currPage"
          v-model:pageSize="pageData.pageSize"
          :total="pageData.total"
          @change="onChangeFn"
          show-size-changer
          show-quick-jumper
          :show-total="() => `${i18n.t('共')} ${pageData.total} ${i18n.t('条')}`"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import BaseEchart from '@/components/BaseEchart/index.vue'
import type { EChartsOption } from '@/components/BaseEchart/config'
import { columns } from './tableConfig'
import { barOptions } from './chartsOptions'
import { computed, reactive, ref, onMounted } from 'vue'
import { useI18n } from '@/utils'
import { BasicPageParams } from '@/types/processBoard'
import type { TableColumnsType } from '@fs/smart-design/dist/ant-design-vue_es'
import { getDepartment } from '@/api'
const i18n = useI18n()

type propsType = {
  processDetailLoading: boolean
  processDetailData: any
  processDetailPageData: BasicPageParams
  processConfigId: any
}

const getSplitInterval = (data: number[]) => {
  let max = Math.ceil(Math.max(...data))
  if (max === 0) {
    max = 100
  }
  return max
}

const props = defineProps<propsType>()
const emits = defineEmits(['onPageChange', 'onExportProcess', 'update:processDetailPageData'])
const pageData = computed<BasicPageParams>({
  get: () => props.processDetailPageData,
  set: val => emits('update:processDetailPageData', val),
})
let selectLists: string | any[] = []
const userList = ref<any[]>([])
const dimensionList = computed(() => {
  const boardSearchWhite = new Function(`return ${process.env.VUE_APP_BOARD_SEARCH_WHITE}`)()
  const list = [
    { label: i18n.t('负责人'), value: 2 },
    { label: i18n.t('业务线'), value: 3 },
  ]
  boardSearchWhite.includes(props.processConfigId) && list.push({ label: i18n.t('PBU团队'), value: 4 })
  return list
})
const queryData = reactive({
  milepostSuperviser: undefined,
  dimension: undefined,
})
const currentColumns = computed<TableColumnsType>(() => {
  const name = [
    {
      title:
        (queryData.dimension === 2 && i18n.t('负责人名称')) ||
        (queryData.dimension === 3 && i18n.t('业务线名称')) ||
        (queryData.dimension === 4 && i18n.t('PBU团队名称')) ||
        i18n.t('业务线名称'),
      dataIndex: 'name',
      sorter: true,
      width: 152,
    },
  ]
  return [...name, ...columns]
})

const onSearchChangeFn = () => {
  const params: any = { dimension: queryData.dimension }
  let projectUuidList = checkChildren(selectLists || [])
  if (projectUuidList.length) {
    params.milepostSuperviser = projectUuidList
  } else {
    queryData.milepostSuperviser = undefined
  }
  emits('onPageChange', params)
}

const onRoleSearchChangeFn = (value: any, selectedOptions: any) => {
  selectLists =
    selectedOptions.map((item: any) => {
      return item[item.length - 1]
    }) || []
  onSearchChangeFn()
}

const onChangeFn = (current: number, pageSize: number) => {
  pageData.value.currPage = current
  pageData.value.pageSize = pageSize
  onSearchChangeFn()
}

const handleTableChange = (pagination: any, filters: any, sorter: any) => {
  const order = (sorter?.order && sorter.order.slice(0, sorter.order.length - 3)) || undefined
  const params: any = { dimension: queryData.dimension }
  let projectUuidList = checkChildren(selectLists || [])
  if (projectUuidList.length) {
    params.milepostSuperviser = projectUuidList
  } else {
    queryData.milepostSuperviser = undefined
  }
  emits(
    'onPageChange',
    Object.assign({}, params, (order && { indexSortStr: sorter?.field, indexSortMothod: order }) || null)
  )
}

const handleTree = (node: any, users: any = []) => {
  return node.reduce((users: any, cur: any) => {
    if (cur.departmentChildrens) {
      const data = JSON.parse(JSON.stringify(cur))
      data.departmentChildrens = JSON.parse(JSON.stringify(data.uuidAndNames || []))
      !((cur.uuidAndNames || []).length + (cur.departmentChildrens || []).length) && (data.disabled = true)
      users.push(data)
      cur.departmentChildrens.length && handleTree(cur.departmentChildrens, data.departmentChildrens)
    }
    return users
  }, users)
}

const checkChildren = (node: any, users: any = []) => {
  return node.reduce((users: any, cur: any) => {
    if (cur.uuid && cur.name && Object.keys(cur).length === 2) {
      users.push(cur.uuid)
    } else if (cur.departmentChildrens && cur.departmentChildrens.length) {
      checkChildren(cur.departmentChildrens, users)
    }
    return users
  }, users)
}

const getDepartmentFn = async () => {
  const res = await getDepartment()
  if (res.code !== 200) throw new Error(res.msg)
  userList.value = handleTree(res.data)
}

const projectChartOption = computed<EChartsOption>(() => {
  if (props?.processDetailData?.length) {
    const nameList: any[] = [],
      doingList: any[] = [],
      delayList: any[] = [],
      doenList: any[] = [],
      transactDoneList: any[] = [],
      rateLists: any[] = [],
      barMaxList: any[] = []
    props.processDetailData.forEach((item: any) => {
      nameList.push(item.name)
      doingList.push(item.doing)
      delayList.push(item.delay)
      doenList.push(item.doen)
      transactDoneList.push(item.transactDone)
      rateLists.push((item.delay / item.projectTotal).toFixed(2))
      barMaxList.push(item.doing + item.delay + item.doen + item.transactDone)
    })
    const nummax = getSplitInterval(barMaxList)
    const ratemax = getSplitInterval(rateLists)
    return {
      dataZoom: {
        show: props.processDetailData?.length > 8, // 为true 滚动条出现
        realtime: true,
        type: 'slider', // 有type这个属性，滚动条在最下面，也可以不行，写y：36，这表示距离顶端36px，一般就是在图上面。
        height: 6, // 表示滚动条的高度，也就是粗细
        start: 0, // 表示默认展示百分比范围。
        end: (props.processDetailData?.length > 8 && (8 / props.processDetailData?.length) * 100) || 100,
        showDetail: false,
        zoomLock: true,
        brushSelect: false,
        bottom: 20,
        handleStyle: {
          borderWidth: 0,
          borderCap: 'round',
          color: 'rgba(221, 225, 229, 1)',
        },
        textStyle: {
          color: 'transparent',
        },
        borderColor: 'transparent',
        dataBackground: {
          areaStyle: {
            color: 'transparent',
          },
          lineStyle: {
            color: 'transparent',
          },
        },
        selectedDataBackground: {
          areaStyle: {
            color: 'transparent',
          },
          lineStyle: {
            color: 'transparent',
          },
        },
        fillerColor: '#DDD',
        moveHandleStyle: {
          color: '#DDD',
          shadowColor: '#DDD',
          borderCap: 'round',
        },
      },
      xAxis: {
        data: nameList,
      },
      yAxis: [
        {
          min: 0,
          max: nummax,
          splitNumber: 5,
          minInterval: 1,
          interval: nummax / 5,
        },
        {
          min: 0,
          max: ratemax,
          splitNumber: 5,
          minInterval: 1,
          interval: ratemax / 5,
        },
      ],
      series: [
        {
          name: i18n.t('进行中'),
          data: doingList,
          barWidth: 40,
          stack: 'Ad',
          type: 'bar',
        },
        {
          name: i18n.t('已延期'),
          data: delayList,
          barWidth: 40,
          stack: 'Ad',
          type: 'bar',
        },
        {
          name: i18n.t('已完成'),
          data: doenList,
          barWidth: 40,
          stack: 'Ad',
          type: 'bar',
        },
        {
          name: i18n.t('已办结'),
          data: transactDoneList,
          barWidth: 40,
          stack: 'Ad',
          type: 'bar',
        },
        {
          name: i18n.t('延期率'),
          data: rateLists,
          barWidth: 40,
          barGap: '40%',
          type: 'line',
          yAxisIndex: 1,
          symbol: 'circle',
          tooltip: {
            valueFormatter: (value: any) => {
              return value + '%'
            },
          },
        },
      ],
    }
  } else {
    return barOptions
  }
})

onMounted(() => {
  getDepartmentFn()
})
</script>

<style scoped lang="scss">
.process-detail-board-container {
  width: 100%;
  margin-top: 16px;
  padding: 24px;
  background: #ffffff;
  box-shadow: 0px 2px 8px 0px rgba(88, 98, 110, 0.08);
  border-radius: 4px;
  .board-box {
    .board-header {
      display: flex;
      justify-content: space-between;
      .board-search {
        display: flex;
        align-items: center;
        :deep(.cust-select) {
          .fs-select-selector {
            padding: 0;
            box-shadow: none !important;
            border: none;
          }
        }
      }
    }
    .title {
      margin-bottom: 24px;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      line-height: 24px;
    }
    .process-board {
      width: 100%;
      height: 100%;
    }
  }
  .table-box {
    margin-top: 24px;
    .header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
      .title {
        font-size: 14px;
        font-weight: 500;
        color: #333333;
        line-height: 22px;
      }
      .btn {
        font-size: 12px;
        font-weight: 400;
        color: #378eef;
        line-height: 18px;
        cursor: pointer;
      }
    }
    :deep(.fs-table-cell) {
      &:empty {
        &::after {
          content: '--';
        }
      }
    }
    :deep(.fs-table-tbody) {
      tr > td {
        background: #fff;
      }
    }
    :deep(.fs-table-container) {
      &::after {
        box-shadow: none;
      }
    }
    :deep(.fs-table-content) {
      padding-bottom: 4px;
    }
    .fei-su-pagination {
      padding-top: 11px;
      padding-bottom: 0;
    }
  }
}
</style>
