import type { TableColumnsType } from 'ant-design-vue'
import { VNode, h, computed } from 'vue'
import { transformDate } from '@/utils'
import i18n from '@/i18n'

const statusData = computed(() => ({
  0: {
    label: i18n.t('进行中'),
    class: 'iconfont icontubiao_jinhangzhong colorFA8F23',
  },
  1: {
    label: i18n.t('已完成'),
    class: 'iconfont iconyiwanchengicon color2FCC83',
  },
  2: {
    label: i18n.t('已办结'),
    class: 'iconfont iconyiwanchengicon color378EEF',
  },
}))

export const columns = computed<TableColumnsType>(() => [
  {
    title: i18n.t('项目编号'),
    dataIndex: 'topicName',
    width: 160,
  },
  {
    title: i18n.t('需求场景'),
    dataIndex: 'demand',
    width: 132,
  },
  {
    title: i18n.t('产品ID'),
    dataIndex: 'productId',
    width: 84,
  },
  {
    title: i18n.t('产品型号名'),
    dataIndex: 'productType',
    width: 120,
  },
  {
    title: i18n.t('产品状态'),
    dataIndex: 'productStatus',
    width: 87,
  },
  {
    title: i18n.t('厂商下架时间'),
    dataIndex: 'manufacturerOffTime',
    width: 104,
    customRender: ({ text, value, record }): VNode => {
      return h('span', transformDate(text, 'YYYY-MM-DD'))
    },
  },
  {
    title: i18n.t('我司预计下架时间'),
    dataIndex: 'offTime',
    width: 128,
    customRender: ({ text, value, record }): VNode => {
      return h('span', transformDate(text, 'YYYY-MM-DD'))
    },
  },
  {
    title: i18n.t('下架原因'),
    dataIndex: 'reason',
    width: 152,
  },
  {
    title: i18n.t('我司替代ID'),
    dataIndex: 'replaceProductId',
    width: 92,
  },
  {
    title: i18n.t('替代品型号'),
    dataIndex: 'replaceProductType',
    width: 92,
  },
  {
    title: i18n.t('我司替代ID'),
    dataIndex: 'replaceProductId',
    width: 92,
  },
  {
    title: i18n.t('对标文档（替代品差异）'),
    dataIndex: 'replaceFile',
    width: 224,
    customRender: ({ text, value, record }): VNode => {
      return h(
        'a',
        {
          href: text,
          title: text,
          style: {
            display: 'inline-block',
            width: '100%',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          },
        },
        text
      )
    },
  },
  {
    title: i18n.t('软件是否可持续更新'),
    dataIndex: 'updateOrNot',
    width: 140,
  },
  {
    title: i18n.t('产品线'),
    dataIndex: 'productLine',
    width: 104,
  },
  {
    title: i18n.t('当前进度'),
    dataIndex: 'milestone',
    width: 104,
  },
  {
    title: i18n.t('当前节点负责人'),
    dataIndex: 'milestoneUserName',
    width: 118,
  },
  {
    title: i18n.t('项目状态'),
    dataIndex: 'projecStatus',
    width: 84,
    customRender: ({ text, value, record }): VNode => {
      return (
        text &&
        h(
          'span',
          {
            style: {
              display: 'flex',
              alignItems: 'center',
              height: '18px',
              color: '#333333',
            },
          },
          [
            h('i', {
              style: {
                marginRight: '4px',
              },
              class: text && statusData.value[text as keyof typeof statusData.value].class,
            }),
            (text && statusData.value[text as keyof typeof statusData.value].label) || '',
          ]
        )
      )
    },
  },
  {
    title: i18n.t('创建时间'),
    dataIndex: 'createdTime',
    width: 160,
    customRender: ({ text, value, record }): VNode => {
      return h('span', transformDate(text, 'YYYY-MM-DD HH:mm:ss'))
    },
  },
  {
    title: i18n.t('创建人'),
    dataIndex: 'createdUserName',
    width: 118,
  },
])
