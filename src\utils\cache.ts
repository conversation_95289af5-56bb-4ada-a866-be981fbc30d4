const CACHE_KEY = 'BPM_APP_CACHE-'

/**
 * @description: 缓存类
 * @class Cache
 * @property {string} prefix 缓存前缀
 * @property {Map<string, unknown>} cache 缓存
 * @property {Storage} store 缓存存储
 * @method {getCacheKey} 获取缓存 key
 * @method {get} 获取缓存
 * @method {set} 设置缓存
 * @method {remove} 删除缓存
 * @method {clear} 清空缓存
 * @example
 * const cache = new Cache(CACHE_KEY)
 * cache.set('key', 'value')
 * cache.get('key')
 * cache.remove('key')
 * cache.clear()
 */
class Cache {
  private prefix: string
  private cache: Map<string, unknown>
  private store: Storage

  constructor(prefix: string) {
    this.prefix = prefix
    this.store = window.localStorage
    this.cache = new Map<string, unknown>()
  }

  private getCacheKey(key: string): string {
    return this.prefix + key
  }

  public get(key: string): unknown {
    const cacheKey = this.getCacheKey(key)
    return this.cache.get(cacheKey) ?? this.store.getItem(cacheKey)
  }

  public set(key: string, value: string, isStore = true): void {
    const cacheKey = this.getCacheKey(key)
    this.cache.set(cacheKey, value)
    isStore && this.store.setItem(cacheKey, value)
  }

  public remove(key: string): void {
    const cacheKey = this.getCacheKey(key)
    this.cache.delete(cacheKey)
    this.store.removeItem(cacheKey)
  }

  public clear(): void {
    this.cache.clear()
    this.store.clear()
  }
}

export const cache = new Cache(CACHE_KEY)
