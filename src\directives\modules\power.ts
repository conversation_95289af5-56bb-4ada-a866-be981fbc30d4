import type { DirectiveBinding } from 'vue'
import { getPower } from '@/utils'

const { fnPower = [] } = getPower() // 角色权限
const fnPowerMap = fnPower.reduce((pre: Map<string, unknown>, cur: any) => {
  pre.set(cur.permissionCode, cur)
  return pre
}, new Map())

export const hasFnPower = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { value } = binding
    const flag = fnPowerMap.has(value)
    if (!flag) {
      el.parentNode && el.parentNode.removeChild(el)
    }
  },
}
