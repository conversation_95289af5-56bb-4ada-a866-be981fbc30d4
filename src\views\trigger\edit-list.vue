<template>
  <div class="edit-list-container shadow-radius bg-white">
    <FForm ref="formRef" :model="formState" :rules="rules" layout="vertical">
      <div class="card-base">
        <div class="fontSize14 fontWeight600">{{ i18n.t('基本信息') }}</div>
      </div>
      <div class="gray1 marginB24"></div>
      <FRow :gutter="[24, 24]">
        <FCol :span="8">
          <FFormItem :label="i18n.t('触发器名称')" name="triggerName">
            <FInput v-model:value="formState.triggerName" :placeholder="i18n.t('请输入触发器名称')" class="cursor" />
          </FFormItem>
        </FCol>
        <FCol :span="8">
          <FFormItem :label="i18n.t('状态')" name="status">
            <FSelect
              v-model:value="formState.status"
              :placeholder="i18n.t('请选择')"
              style="width: 100%"
              :options="statusList"
            >
              <template #suffixIcon>
                <i class="cursor iconfont colord8d8d8 fontSize12">&#xe799;</i>
              </template>
            </FSelect>
          </FFormItem>
        </FCol>
        <FCol :span="8">
          <FFormItem :label="i18n.t('执行方式')" name="isSyn">
            <FSelect
              v-model:value="formState.isSyn"
              :placeholder="i18n.t('请选择')"
              style="width: 100%"
              :options="isSynList"
            >
              <template #suffixIcon>
                <i class="cursor iconfont colord8d8d8 fontSize12">&#xe799;</i>
              </template>
            </FSelect>
          </FFormItem>
        </FCol>
        <FCol :span="24" class="marginB24">
          <FFormItem :label="i18n.t('备注')" name="remarks" class="fs-textarea">
            <FTextarea v-model:value="formState.remarks" :placeholder="i18n.t('请输入内容')" class="cursor" />
          </FFormItem>
        </FCol>
      </FRow>
      <!-- <FRow class="marginB24">
        <FCol style="width: 1058px">
          <FFormItem :label="i18n.t('备注')" name="remarks" class="fs-textarea">
            <FTextarea v-model:value="formState.remarks" :placeholder="i18n.t('请输入内容')" class="cursor" />
          </FFormItem>
        </FCol>
      </FRow> -->
      <div class="gray16"></div>
      <div class="card-action">
        <div class="fontSize14 fontWeight600">{{ i18n.t('动作发生') }}</div>
      </div>
      <div class="gray1 marginB24"></div>
      <FRow class="marginB24">
        <FCol style="width: 517px">
          <FFormItem :label="i18n.t('流程类型')" name="processConfigId">
            <FSelect
              v-model:value="formState.processConfigId"
              :placeholder="i18n.t('请选择')"
              style="width: 100%"
              :options="processTypeList.map(item => ({ value: item.id, label: item.processName }))"
              option-filter-prop="label"
              show-search
              @change="onChangeProcessConfig"
            >
              <template #suffixIcon>
                <i class="cursor iconfont colord8d8d8 fontSize12">&#xe799;</i>
              </template>
            </FSelect>
          </FFormItem>
        </FCol>
      </FRow>
      <FRow class="marginB24">
        <FCol style="width: 1058px">
          <FFormItem :label="i18n.t('附加过滤条件')" name="condition" class="fs-textarea">
            <FTextarea v-model:value="formState.condition" :placeholder="i18n.t('请输入表达式')" class="cursor" />
          </FFormItem>
        </FCol>
      </FRow>
      <FRow class="marginB24">
        <FCol style="width: 1058px">
          <FFormItem :label="i18n.t('触发动作')" name="triggerType">
            <FSpace direction="vertical">
              <FRadioGroup v-model:value="formState.triggerType" :options="triggerList" @change="triggerChange" />
            </FSpace>
          </FFormItem>
        </FCol>
      </FRow>
      <FRow class="marginB24" v-if="formState.triggerType == TypeTriggerEnum.UPDATE">
        <FCol style="width: 1058px" class="marginB4">
          <FFormItem
            :label="i18n.t('表达式')"
            name="updateCondition"
            class="fs-textarea"
            :rules="[{ required: true, message: i18n.t('请输入值表达式') }]"
          >
            <FTextarea
              v-model:value="formState.updateCondition"
              :placeholder="i18n.t('请输入值表达式')"
              class="cursor"
            />
          </FFormItem>
        </FCol>
        <FCol :span="24">
          <p class="fontSize12 fontColorOrange marginB0">{{ i18n.t('更新后的值满足什么条件') }}</p>
        </FCol>
      </FRow>
      <FRow
        class="marginB24"
        v-if="
          formState.triggerType == TypeTriggerEnum.NODE_FININSH ||
          formState.triggerType == TypeTriggerEnum.TASK_POOL_FININSH
        "
      >
        <FCol style="width: 517px">
          <FFormItem
            :label="i18n.t('节点选择')"
            name="milepostNodeId"
            :rules="[{ required: true, message: i18n.t('请选择节点'), trigger: 'change' }]"
          >
            <FSelect
              v-model:value="formState.milepostNodeId"
              :placeholder="i18n.t('请选择')"
              style="width: 100%"
              :options="nodeList"
              :field-names="{ label: 'milepostName', value: 'nodeId' }"
              @change="changeNode"
            >
              <template #suffixIcon>
                <i class="cursor iconfont colord8d8d8 fontSize12">&#xe799;</i>
              </template>
            </FSelect>
          </FFormItem>
        </FCol>
      </FRow>
      <FRow :gutter="[24, 0]" class="marginB24" v-if="formState.triggerType == TypeTriggerEnum.TASK_FINISH">
        <FCol :span="8">
          <FFormItem
            :label="i18n.t('子流程选择')"
            name="milepostNodeId"
            :rules="[{ required: true, message: i18n.t('请选择子流程'), trigger: 'change' }]"
          >
            <FSelect
              v-model:value="formState.milepostNodeId"
              :placeholder="i18n.t('请选择')"
              style="width: 100%"
              :options="nodeList"
              :field-names="{ label: 'milepostName', value: 'nodeId' }"
              @change="changeNode"
            >
              <template #suffixIcon>
                <i class="cursor iconfont colord8d8d8 fontSize12">&#xe799;</i>
              </template>
            </FSelect>
          </FFormItem>
        </FCol>
        <FCol :span="8">
          <FFormItem :label="i18n.t('流程选择')">
            <FSelect
              v-model:value="formState.processDefineKey"
              :options="processList"
              :field-names="{ label: 'processName', value: 'id' }"
              :placeholder="i18n.t('请选择流程id')"
              option-filter-prop="processName"
              style="width: 100%"
              show-search
              allow-clear
              @change="changeProcess"
            />
          </FFormItem>
        </FCol>
        <FCol :span="8">
          <FFormItem
            :label="i18n.t('任务选择')"
            name="taskNodeId"
            :rules="[{ required: true, message: i18n.t('请选择任务'), trigger: 'change' }]"
          >
            <FSelect
              v-model:value="formState.taskNodeId"
              :placeholder="i18n.t('请选择')"
              style="width: 100%"
              :field-names="{ label: 'name', value: 'id' }"
              :options="currentTaskList"
              show-search
              option-filter-prop="name"
            >
              <template #suffixIcon>
                <i class="cursor iconfont colord8d8d8 fontSize12">&#xe799;</i>
              </template>
            </FSelect>
          </FFormItem>
        </FCol>
      </FRow>
      <FRow :gutter="[24, 24]" class="marginB24" v-if="formState.triggerType == TypeTriggerEnum.TIME">
        <FCol :span="8">
          <FFormItem
            :label="i18n.t('定时任务类型')"
            name="jobType"
            :rules="[{ required: true, message: i18n.t('请选择定时任务类型'), trigger: 'change' }]"
          >
            <FSelect
              v-model:value="formState.jobType"
              placeholder="定时任务类型"
              :options="[
                { value: 1, label: '节点开始' },
                { value: 2, label: '节点超时' },
                { value: 3, label: '流程激活' },
              ]"
              @change="changeJobType"
            />
          </FFormItem>
        </FCol>
        <FCol :span="8">
          <FFormItem
            :label="i18n.t('脚本时间')"
            name="jobTime"
            :rules="[{ required: true, message: i18n.t('请选择脚本时间'), trigger: 'change' }]"
          >
            <FInputGroup compact>
              <FSelect
                v-model:value="jobTimeType"
                placeholder="请选择脚本时间"
                style="width: 80px"
                :options="[
                  { value: 1, label: '天' },
                  { value: 2, label: '小时' },
                ]"
              />
              <FInputNumber
                style="width: calc(100% - 80px)"
                v-model:value="formState.jobTime"
                :min="0"
                :precision="0"
                placeholder="请输入脚本时间"
              />
            </FInputGroup>
          </FFormItem>
        </FCol>
        <FCol :span="8">
          <FFormItem
            v-if="[1, 2].includes(formState.jobType)"
            :label="i18n.t('节点选择')"
            name="milepostNodeId"
            :rules="[{ required: true, message: i18n.t('请选择节点'), trigger: 'change' }]"
          >
            <FSelect
              v-model:value="formState.milepostNodeId"
              placeholder="请选择"
              style="width: 100%"
              :options="nodeList"
              :field-names="{ label: 'milepostName', value: 'nodeId' }"
              mode="multiple"
              maxTagCount="responsive"
            />
          </FFormItem>
        </FCol>
        <FCol :span="24">
          <FFormItem
            :label="i18n.t('Cron表达式')"
            name="cron"
            :rules="[{ required: true, message: i18n.t('Cron表达式'), trigger: 'change' }]"
          >
            <CronEditor
              v-model:value="formState.cron"
              :is-valid="isExpressionValid"
              @change="handleCronChange"
              @error="handleError"
            />
          </FFormItem>
        </FCol>
      </FRow>
      <FRow v-if="formState.triggerType == TypeTriggerEnum.PROCESS_CREATE"></FRow>
      <FRow class="marginB24" v-if="formState.triggerType == TypeTriggerEnum.NODE_SAVE">
        <FCol style="width: 517px">
          <FFormItem
            :label="i18n.t('节点选择')"
            name="milepostNodeId"
            :rules="[{ required: true, message: i18n.t('请选择节点'), trigger: 'change' }]"
          >
            <FSelect
              v-model:value="formState.milepostNodeId"
              :placeholder="i18n.t('请选择')"
              style="width: 100%"
              :options="nodeList"
              :field-names="{ label: 'milepostName', value: 'nodeId' }"
              @change="changeNode"
            >
              <template #suffixIcon>
                <i class="cursor iconfont colord8d8d8 fontSize12">&#xe799;</i>
              </template>
            </FSelect>
          </FFormItem>
        </FCol>
      </FRow>
      <FRow :gutter="[24, 0]" class="marginB24" v-if="formState.triggerType == TypeTriggerEnum.TASK_SAVE">
        <FCol :span="8">
          <FFormItem
            :label="i18n.t('子流程选择')"
            name="milepostNodeId"
            :rules="[{ required: true, message: i18n.t('请选择子流程'), trigger: 'change' }]"
          >
            <FSelect
              v-model:value="formState.milepostNodeId"
              :placeholder="i18n.t('请选择')"
              style="width: 100%"
              :options="nodeList"
              :field-names="{ label: 'milepostName', value: 'nodeId' }"
              @change="changeNode"
            >
              <template #suffixIcon>
                <i class="cursor iconfont colord8d8d8 fontSize12">&#xe799;</i>
              </template>
            </FSelect>
          </FFormItem>
        </FCol>
        <FCol :span="8">
          <FFormItem :label="i18n.t('流程选择')">
            <FSelect
              v-model:value="formState.processDefineKey"
              :options="processList"
              :field-names="{ label: 'processName', value: 'id' }"
              :placeholder="i18n.t('请选择流程id')"
              option-filter-prop="processName"
              style="width: 100%"
              show-search
              allow-clear
              @change="changeProcess"
            />
          </FFormItem>
        </FCol>
        <FCol :span="8">
          <FFormItem
            :label="i18n.t('任务选择')"
            name="taskNodeId"
            :rules="[{ required: true, message: i18n.t('请选择任务'), trigger: 'change' }]"
          >
            <FSelect
              v-model:value="formState.taskNodeId"
              :placeholder="i18n.t('请选择')"
              style="width: 100%"
              :field-names="{ label: 'name', value: 'id' }"
              :options="currentTaskList"
              show-search
              option-filter-prop="name"
            >
              <template #suffixIcon>
                <i class="cursor iconfont colord8d8d8 fontSize12">&#xe799;</i>
              </template>
            </FSelect>
          </FFormItem>
        </FCol>
      </FRow>
      <FRow v-if="formState.triggerType == TypeTriggerEnum.WORK_FLOW_FININSH"></FRow>
      <div class="gray16" v-if="id"></div>
      <action-list
        v-if="id"
        :data-source="dataSource"
        :process-type-list="processTypeList"
        @deleteAction="deleteAction"
        @addAction="addAction"
        @add="add"
      ></action-list>
      <div class="gray24"></div>
      <div class="marginT16">
        <FButton key="back" type="primary" @click="handleOk(formRef)" class="marginR16">{{ i18n.t('提交') }}</FButton>
        <FButton key="submit" @click="handleReset(formRef)">{{ i18n.t('重置') }}</FButton>
      </div>
    </FForm>
  </div>
</template>

<script lang="ts" setup>
interface FormState {
  triggerName: string
  status: number
  remarks?: string
  processConfigId: number | string
  condition?: string
  triggerType: number
  updateCondition?: string
  milepostNodeId?: any
  taskNodeId?: string
  eventIds?: string
  id?: number
  processDefineKey?: number | string
  isSyn: number
  cron: string
  [key: string]: any
}
import { useI18n } from '@/utils'
import { Rule } from '@fs/smart-design/dist/ant-design-vue_es/form'
import { onMounted, reactive, ref, computed, watch } from 'vue'
import ActionList from '@/views/trigger/components/action-list.vue'
import CronEditor from '@/components/CronEditor/index.vue'
import { useRoute, useRouter } from 'vue-router'
import { FormInstance, SelectProps } from '@fs/smart-design/dist/ant-design-vue_es'
import { FSelect, message } from '@fs/smart-design'
import {
  addOrEditAction,
  addOrEditTriggerData,
  event,
  FAKE_ID,
  IResExcution,
  nodeType,
  processTye,
  taskType,
  TriggerData,
  triggerTypeRadioList,
  TypeActionEnum,
  TypeTriggerEnum,
} from '@/types/excutionListModel'
import {
  addApiAction,
  editApiAction,
  addApiTrigger,
  editApiTrigger,
  deleteApiAction,
  getProcessTypeListNew,
  getResNodeList,
  getTaskList,
  getTriggerById,
} from '@/api/automotion'
import { getProcess } from '@/api'
import lodash from 'lodash'
const route = useRoute()
const router = useRouter()
const id = route?.query?.id
const formRef = ref<FormInstance>()
const i18n = useI18n()
let editData: IResExcution<TriggerData<event>>
let processDefineNodeKey = ref<string>('')
let formState = reactive<FormState>({
  triggerName: '',
  status: 1,
  remarks: '',
  processConfigId: '',
  condition: 'default',
  triggerType: TypeTriggerEnum.UPDATE,
  updateCondition: '',
  milepostNodeId: undefined,
  taskNodeId: '',
  eventIds: '',
  id: undefined,
  processDefineKey: undefined,
  isSyn: 0,
  cron: undefined,
})
const rules: Record<string, Rule[]> = {
  triggerName: [{ required: true, message: i18n.t('请输入触发器名称') }],
  status: [{ required: true, message: i18n.t('请选择状态'), trigger: 'change' }],
  isSyn: [{ required: true, message: i18n.t('请选择执行方式'), trigger: 'change' }],
  processConfigId: [{ required: true, message: i18n.t('请选择流程类型'), trigger: 'change' }],
  triggerType: [{ required: true, message: i18n.t('请选择触发动作'), trigger: 'change' }],
}
const statusList = ref<SelectProps['options']>([
  { value: 0, label: i18n.t('停用') },
  { value: 1, label: i18n.t('启用') },
])

const isSynList = ref<SelectProps['options']>([
  { value: 0, label: i18n.t('异步执行') },
  { value: 1, label: i18n.t('同步执行') },
])
const triggerList = triggerTypeRadioList
const processTypeList = ref<processTye[]>([])
const nodeList = ref<nodeType[]>([])
const taskNodeList = ref<taskType[]>([])
const dataSource = ref<event[]>([])
const processList = ref<any[]>([])
const processTaskNodeList = ref<taskType[]>([])
const currentTaskList = computed(() => {
  return [...taskNodeList.value, ...processTaskNodeList.value].reduce((list: taskType[], cur: taskType) => {
    !list.find((item: taskType) => item.id === cur.id) && list.push(cur)
    return list
  }, [])
})

const isExpressionValid = ref(true)
const jobTimeType = ref(2)

const handleCronChange = (value: string) => {
  isExpressionValid.value = true
}

const handleError = (error: string) => {
  isExpressionValid.value = false
  message.warning(`Cron错误: ${error}`)
}

const handleOk = async (formRef: FormInstance | undefined) => {
  if (!formRef) return
  await formRef.validate()
  const params = lodash.cloneDeep(formState)
  if (params.triggerType == TypeTriggerEnum.TIME) {
    params.jobTime = params.jobTime * (jobTimeType.value == 1 ? 24 : 1)
    params.milepostNodeId = params.milepostNodeId.join(',')
  }
  if (!id) {
    // 表单校验通过, 请求添加触发器接口
    const res = await addApiTrigger(params as unknown as addOrEditTriggerData)
    if (res.code == 200) {
      message.success(i18n.t('添加触发器成功'))
      await router.replace({
        name: 'AutoMationList',
      })
    } else {
      message.info(res.msg)
    }
  } else {
    const eventIds = dataSource.value
      .filter(item => item.id != FAKE_ID)
      .map(item => item.id)
      .join(',')
    params.eventIds = eventIds
    const res = await editApiTrigger(params as unknown as addOrEditTriggerData)
    if (res.code == 200) {
      message.success(i18n.t('编辑触发器成功'))
      await router.replace({
        name: 'AutoMationList',
      })
    } else {
      message.info(res.msg)
    }
  }
}
const handleReset = async (formRef: FormInstance | undefined) => {
  if (!formRef) return
  await formRef.resetFields()
  await init()
}

const init = async () => {
  const resProcessType = await getProcessTypeListNew()
  processTypeList.value = resProcessType.data
  // 获取流程类型列表
  if (id) {
    // 获取编辑数据
    await getEditData()
    // 获取里程碑节点
    await getMilePostList()
    // 获取任务节点
    await getTask()
    await changeProcess()
  }
}
const getMilePostList = async () => {
  if (formState.processConfigId != '') {
    const resNode = await getResNodeList(formState.processConfigId)
    nodeList.value = resNode.data
  } else {
    formState.milepostNodeId = undefined
    nodeList.value = []
  }
}
const getTask = async () => {
  let currNode = nodeList.value.filter(item => item.nodeId == formState.milepostNodeId)
  if (currNode.length > 0) {
    processDefineNodeKey.value = currNode[0].processDefineNodeKey
    await getTaskNode()
  }
}
const getTaskNode = async () => {
  if (processDefineNodeKey.value != '') {
    // 获取任务节点
    const taskNode = await getTaskList(processDefineNodeKey.value)
    taskNodeList.value = taskNode.data
  } else {
    taskNodeList.value.some(item => item.id === formState.taskNodeId) && (formState.taskNodeId = '')
    taskNodeList.value = []
  }
}
const getEditData = async () => {
  editData = await getTriggerById(id)
  dataSource.value = editData.data.events
  formState.condition = editData.data.condition
  formState.eventIds = editData.data.eventIds
  formState.id = editData.data.id
  formState.milepostNodeId =
    editData.data.triggerType == TypeTriggerEnum.TIME
      ? editData.data.milepostNodeId.split(',')
      : editData.data.milepostNodeId
  formState.processConfigId = editData.data.processConfigId
  formState.remarks = editData.data.remarks
  formState.status = editData.data.status
  formState.taskNodeId = editData.data.taskNodeId
  formState.triggerName = editData.data.triggerName
  formState.triggerType = editData.data.triggerType
  formState.updateCondition = editData.data.updateCondition
  formState.processDefineKey = editData.data.processDefineKey
  formState.isSyn = editData.data.isSyn
  formState.cron = editData?.data?.cron ?? undefined
  formState.jobTime = editData.data.jobTime
  formState.jobType = editData.data.jobType
  jobTimeType.value = 2
}
const onChangeProcessConfig = async (val: number | string) => {
  formState.processConfigId = val
  // 节点完成单选
  formState.milepostNodeId = undefined
  formState.taskNodeId = ''
  nodeList.value = []
  taskNodeList.value = []
  // 获取里程碑节点
  if (formState.processConfigId != '') {
    await getMilePostList()
  }
}
const changeNode = async (val: number | string) => {
  formState.milepostNodeId = val
  formState.taskNodeId = ''
  taskNodeList.value = []
  if (formState.milepostNodeId) {
    await getTask()
  }
}
// 添加动作
const addAction = async (obj: addOrEditAction, callback: (id: number | string) => void) => {
  const isRule = ruleParams(obj)
  if (!isRule) {
    // 保存参数校验没通过
    return
  }
  let res: IResExcution<event>
  let formData = lodash.cloneDeep(obj)
  if (formData.id == FAKE_ID) {
    formData.id = '' // 假id置空
    res = await addApiAction(formData)
  } else {
    res = await editApiAction(formData)
  }
  if (res.code == 200) {
    message.success(i18n.t('保存动作成功'))
    // 重新查询动作列表
    callback(obj.id)
    // 删除假数据
    const index = dataSource.value.findIndex(item => item.id == FAKE_ID)
    if (index != -1) {
      // 添加数据,删除假数据,新增后端数据
      dataSource.value.splice(index, 1, res.data)
    }
  } else {
    message.info(res.msg)
  }
}
const ruleParams = (action: addOrEditAction) => {
  let isRule = true
  if (action.eventName == undefined || action.eventName == '') {
    isRule = false
    message.info(i18n.t('请填写事件名称'))
    return isRule
  }
  if (action.processConfigId == undefined || action.processConfigId == '') {
    isRule = false
    message.info(i18n.t('请选择流程类型'))
    return isRule
  }
  if (action.eventPriority == undefined || String(action.eventPriority) == '') {
    isRule = false
    message.info(i18n.t('请填写优先级'))
    return isRule
  }
  if (action.eventType == undefined || String(action.eventType) == '') {
    isRule = false
    message.info(i18n.t('请选择动作类型'))
    return isRule
  }
  if (action.eventType == TypeActionEnum.UPDATE) {
    if (action.updateField == '' || action.updateCondition == '') {
      isRule = false
    }
  } else if (action.eventType == TypeActionEnum.CALL_INTERFACE) {
    if (action.url == '') {
      isRule = false
    }
  } else if (action.eventType == TypeActionEnum.NODE_FININSH || action.eventType == TypeActionEnum.TASK_FINISH) {
    if (!action.milepostNodeId) {
      isRule = false
    }
  } else if (action.eventType == TypeActionEnum.ADD_TASK) {
    if (!action.processDefineKey || !action.fields) {
      isRule = false
    }
  } else if (action.eventType == TypeActionEnum.NODE_MESSAGE_NOTIFICATION) {
    if (!action.templateCode || !action.roleCode) {
      isRule = false
    }
  } else if (action.eventType == TypeActionEnum.GROUP_NODE_MESSAGE_NOTIFICATION) {
    if (!action.templateCode || !action.chatsId) {
      isRule = false
    }
  } else if (action.eventType == TypeActionEnum.TASK_MESSAGE_NOTIFICATION) {
    if (!action.templateCode || !action.roleCode || !action.milepostNodeId || !action.taskNodeId) {
      isRule = false
    }
  } else if (action.eventType == TypeActionEnum.GROUP_TASK_MESSAGE_NOTIFICATION) {
    if (!action.templateCode || !action.chatsId || !action.milepostNodeId || !action.taskNodeId) {
      isRule = false
    }
  }
  if (!isRule) {
    message.info(i18n.t('请添写动作内容'))
  }
  return isRule
}
const add = (callback: (obj: event) => void) => {
  const obj = {
    condition: 'default',
    eventName: '',
    eventPriority: 1,
    eventType: TypeActionEnum.UPDATE,
    id: FAKE_ID,
    milepostNodeId: undefined,
    processConfigId: '',
    taskNodeId: '',
    updateCondition: '',
    updateField: '',
    url: '',
    milepostNodeName: '',
    taskNodeName: '',
    processDefineKey: null,
    fields: '',
    processDefineName: '',
  }
  // 添加未保存的编辑项
  dataSource.value.push(obj)
  callback(obj)
}
const deleteAction = async (id: number, callback: (id: number) => void) => {
  // 删除动作
  if (id) {
    const res = await deleteApiAction(id)
    if (res.code == 200) {
      message.success(i18n.t('删除成功'))
      let index: number = dataSource.value.findIndex(item => item.id == id)
      dataSource.value.splice(index, 1)
      callback(id)
    } else {
      message.error(res.msg)
    }
  } else {
    message.error(i18n.t('删除动作参数错误'))
  }
}

const getProcessFn = async () => {
  const res = await getProcess({ currPage: 1, pageSize: 9999, processName: '' })
  if (res.code !== 200) throw new Error(res.msg)
  processList.value = (res?.data?.list || []).map(item => {
    item.id = item.id.toString()
    return item
  })
}

const changeProcess = async () => {
  if (!formState.processDefineKey || formState.processDefineKey === processDefineNodeKey.value) {
    processTaskNodeList.value = []
    return
  }
  const res = await getTaskList(formState.processDefineKey as string)
  if (res.code === 200) {
    processTaskNodeList.value = res.data || []
  } else {
    processTaskNodeList.value = []
  }
}

const triggerChange = () => {
  formState.milepostNodeId = undefined
}

const changeJobType = value => {
  value == 3 && (formState.milepostNodeId = undefined)
}

onMounted(() => {
  // 初始化
  init()
  requestIdleCallback(getProcessFn)
})
</script>

<style lang="scss" scoped>
.edit-list-container {
  padding: 14px 24px 16px 24px;
  background-color: #fff;
  :deep(.fs-form-item-explain-error) {
    color: #333333 !important;
    &:before {
      content: '\e61d';
      margin-right: 4px;
      color: #fa8f23;
      font-family: 'bpm-iconfont' !important;
    }
  }
}
:deep(.fs-form-item-control-input-content) {
  height: auto !important;
}
.card-base {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}
.card-action {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 52px;
  line-height: 52px;
}
.gray1 {
  display: block;
  background-color: #eee;
  height: 1px;
  margin-left: -24px;
  margin-right: -24px;
}
.gray16 {
  display: block;
  background-color: #f1f4f8;
  height: 16px;
  margin-left: -24px;
  margin-right: -24px;
}
.gray24 {
  display: block;
  background-color: #f1f4f8;
  height: 24px;
  margin-left: -24px;
  margin-right: -24px;
}
.fontColorOrange {
  color: #fa8f23;
}
.shadow-radius {
  box-shadow: 0 2px 8px 0 rgba(88, 98, 110, 0.08);
  border-radius: 4px;
}
.bg-white {
  background: #ffffff;
}
.marginT16 {
  margin-top: 16px;
}
.marginB4 {
  margin-bottom: 4px;
}
.marginB0 {
  margin-bottom: 0;
}
</style>
