<template>
  <div class="content-box">
    <FTable v-bind="$attrs" :columns="columns" table-layout="fixed" :sticky="{ offsetHeader: -20 }" :pagination="false">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'demand-type'">
          <div class="demand-type">
            <p class="type-title">{{ record.processType }}</p>
            <p class="type-process-code" v-if="record.sapCode">
              [PS编号]
              <span>{{ record.sapCode }}</span>
            </p>
            <p class="type-process-code" v-if="record.processInstanceCode">
              [{{ i18n.t('流程编码') }}] <a @click="onJumpDemandDetial(record)">{{ record.processInstanceCode }}</a>
            </p>
            <p class="type-process-code" v-if="record.caseNumber">
              [Case Number]
              <a :href="`${ERPURL}/YX_sJsBEkT12004/case_number_list.php?search=${record.caseNumber}`">{{
                record.caseNumber
              }}</a>
            </p>
            <p class="type-process-code" v-if="record.orderNumber">
              [内部订单号]
              <span>{{ record.orderNumber }}</span>
            </p>
            <div class="type-process-code related-modify" v-if="record?.relatedModifyInstances?.length">
              <span class="related-modify-label">[Modify] </span>
              <div>
                <ProcessDetail
                  v-for="item in record?.relatedModifyInstances ?? []"
                  :key="item.id"
                  :value="item"
                  :component-config="{
                    currtRecord: item,
                    componentAttrs: { showValueKey: 'processInstanceCode', hasTooltip: true },
                  }"
                />
              </div>
            </div>
          </div>
        </template>
        <template v-if="column.key === 'demand-info'">
          <div class="demand-info-box">
            <div class="top">
              <p class="urgent" v-if="record.formData?.isUrgent == 1 || record.isUrgent == 1">
                <i class="icon-urgent" />
                <span class="marginB1">{{ i18n.t('加急') }}</span>
              </p>
              <FTooltip>
                <template #title>{{ record.topicName }}</template>
                <span class="demand-title marginR5" @click="onJumpDemandDetial(record)" :title="record.name">
                  {{ record.topicName }}
                </span>
              </FTooltip>
              <FTooltip>
                <template #title>{{ i18n.t('复制编号及详情页链接') }}</template>
                <i class="iconfont icontubiao_fuzhi copy-text" :data-clipboard-text="copyTest" @click="copy(record)" />
              </FTooltip>
            </div>
            <div class="middle">
              <FPopover :get-popup-container="getPopupContainer">
                <span class="info-text"> [{{ i18n.t('描述') }}] {{ getHTMLSpingtext(record.formData?.describe) }}</span>
                <template #content>
                  <!-- <div class="info-text-all">{{ getHTMLSpingtext(record.formData?.describe) }}</div> -->
                  <div @click="showImage($event)">
                    <div class="info-text-all" v-html="record.formData?.describe"></div>
                    <PicturePreview ref="picturePreviewRef" />
                  </div>
                </template>
              </FPopover>
            </div>
            <div class="bottom" v-if="Array.isArray(record.createTagList) && record.createTagList.length">
              <div class="label-more-box" v-if="record.createTagList.length <= 3">
                <div
                  v-for="(item, index) in record.createTagList"
                  :class="['info-type', `info-type${index % 4}`]"
                  :key="item"
                  @click="onHandleTagClick(item, record)"
                >
                  {{ item }}
                </div>
              </div>
              <FPopover :get-popup-container="getPopupContainer" overlay-class-name="mine-pover-class" v-else>
                <i class="label-more"></i>
                <template #content>
                  <div class="label-more-box">
                    <div
                      v-for="(item, index) in record.createTagList"
                      :class="['info-type', `info-type${index % 4}`]"
                      :key="item"
                      @click="onHandleTagClick(item, record)"
                    >
                      {{ item }}
                    </div>
                  </div>
                </template>
              </FPopover>
            </div>
          </div>
        </template>
        <template v-if="column.key === 'track-progress'">
          <div class="scroll-box">
            <fs-step :data="handleMileposts(record)">
              <template #footer="{ row }">
                <div class="marginB16 cup relative" v-if="row.status !== -99999">
                  <span
                    v-if="row.status === 2 && (isNowUser(row) || props.type === 1)"
                    :title="i18n.t('处理')"
                    class="iconfont marginR16 handle"
                    @click="actions(1, row, record)"
                    >&#xe624;</span
                  >
                  <span
                    v-if="row.status === 2"
                    :title="i18n.t('消息协同')"
                    class="iconfont marginR16"
                    :class="{ remind: record.message_remind }"
                    @click="actions(2, row, record)"
                    >&#xe628;</span
                  >
                  <span
                    :title="i18n.t('工作协同')"
                    v-if="row.status === 2"
                    class="iconfont marginR16"
                    :class="{ remind: record.task_remind }"
                    @click="actions(3, row, record)"
                    >&#xe626;
                  </span>
                  <DownloadFiles v-if="record?.attachmentData?.length" :data="record.attachmentData">
                    <template #icon>
                      <span class="iconfont marginR10">&#xe625;</span>
                    </template>
                  </DownloadFiles>
                </div>
              </template>
            </fs-step>
          </div>
        </template>
      </template>
      <template #emptyText>
        <FEmpty :image="require(`@/assets/images/empty.png`)" />
      </template>
    </FTable>

    <SubTask
      @input="taskModalShow = false"
      :task-modal-show="taskModalShow"
      :processes-uuid="processesUuid"
      :processes-name="processesName"
      :id="id"
      :process-config-id="processConfigId"
    />
    <!-- 消息协同 -->
    <FDrawer
      :width="380"
      :mask-closable="false"
      v-model:visible="visible"
      class="custom-class"
      placement="right"
      :title="i18n.t('消息协同')"
    >
      <Message
        :process-name="(processesName as string)"
        :process-no="processInstanceCode"
        :instance-id="(processesUuid as number)"
        :params-wrapper="paramsWrapper"
      />
      <!-- <ChatRoom
        :process-no="processInstanceCode"
        :instance-id="processesUuid"
        :process-name="processesName"
        :params-wrapper="paramsWrapper"
      /> -->
    </FDrawer>
  </div>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import fsStep from '@/components/Step/StepIndex.vue'
import DownloadFiles from '../DownloadFiles.vue'
import SubTask from '../SubTask.vue'
import Message from '@/components/Message/index.vue'
import ChatRoom from '@/components/ChatRoom/index.vue'
import type { TableColumnsType } from '@fs/smart-design/dist/ant-design-vue_es'
import { ERPURL, getUserInfo, jumpToDemand, useI18n } from '@/utils'
import { UserInfo } from '@/types/common'
import { IProcess } from '@/types/handle'
import { getHTMLSpingtext, BAESURL } from '@/utils'
import PicturePreview from '@/components/PicturePreview/index.vue'
import ProcessDetail from '@/views/process-operate/components/CustomComponents/BusinessComponent/ProcessDetail/index.vue'
import Clipboard from 'clipboard'
import { messageInstance as message } from '@fs/smart-design'

// 其他 props 的参数，以 $attr 的形式绑定到了 table 组件上了
interface IProps {
  type?: number
}

const i18n = useI18n()
const router = useRouter()
const props = defineProps<IProps>()

const userInfo: UserInfo = getUserInfo()
const taskModalShow = ref<boolean>(false)
const processesUuid = ref<number>()
const processesName = ref<string>()
const visible = ref(false)
const columns = computed<TableColumnsType>(() => [
  { title: `${i18n.t('流程类型')}/${i18n.t('流程编码')}/Case Number`, key: 'demand-type', width: 260 },
  { title: i18n.t('需求信息'), key: 'demand-info', width: 284 },
  { title: i18n.t('跟踪进度'), key: 'track-progress' },
])

const getPopupContainer = (trigger: HTMLElement) => trigger.parentElement

// 处理里程碑状态
const handleMileposts = (data: any = {}) => {
  if (!Array.isArray(data?.mileposts)) return []
  let flag = false
  // 处理结办后的节点在时间轴上置灰的标识
  data.mileposts.forEach(item => {
    item.baseFormData = data?.formData || {}
    if (flag) item.status = -99999
    else if (item.status === 4) flag = true
  })
  return data.mileposts
}

// 判断当前用户
const isNowUser = (row: any) => {
  if (userInfo?.uuid === row.superviserUuid) return true
  return false
}

const onHandleTagClick = (label: string, record: any) => {
  if (record?.tagUrl && label.startsWith('IPD')) {
    window.open(record.tagUrl)
  }
}

// 需求详情跳转
const onJumpDemandDetial = (record: any) => {
  if (props.type === 7) {
    router.push({
      name: 'DemandAdd',
      params: { id: record.processConfigId, processDefineKey: record.processDefineKey },
      query: { draftId: record.draftId },
    })
  } else {
    jumpToDemand(record.id, record.processConfigId, true)
  }
}
const id = ref<number>()
const processInstanceCode = ref('')
const processType = ref<number>()
const processConfigId = ref<number>()
const processName = ref<string>()
const actions = (type: number, row: any, record: any) => {
  processType.value = record.processType
  processName.value = record.topicName
  processInstanceCode.value = record.processInstanceCode
  switch (type) {
    case 1:
      onJumpDemandDetial(record)
      break
    case 2:
      visible.value = true
      processesUuid.value = row.instanceId
      break
    case 3:
      taskModalShow.value = true
      processesUuid.value = row.id
      id.value = record.id
      processConfigId.value = record.processConfigId
      break
  }
}
// 添加参数包装
const paramsWrapper = <T>(data: T): T & { processInstanceCode: string; instanceId: number } => {
  return {
    ...data,
    processType: processType.value,
    instanceTopicName: processName.value,
    processInstanceCode: processInstanceCode.value,
    instanceId: processesUuid.value as number,
  }
}
const picturePreviewRef = ref()
const showImage = (e: any) => {
  picturePreviewRef.value.showImage(e)
}

const copyTest = ref('')
const copy = (data: any) => {
  copyTest.value = `${data.topicName} ${data.processInstanceCode} ${BAESURL}/bpm-manage/process/detail/${data.id}`
  const clipboard = new Clipboard('.copy-text')
  clipboard.on('success', e => {
    message.success(i18n.t('复制成功'))
    clipboard.destroy()
  })
  clipboard.on('error', e => {
    message.warning(i18n.t('复制失败'))
    clipboard.destroy()
  })
}
</script>
<style lang="scss" scoped>
:deep.fs-drawer-body {
  padding: 0 !important;
}

.remind {
  position: relative;
  &::after {
    content: '';
    position: absolute;
    top: 1px;
    right: 0;
    display: inline-block;
    height: 4px;
    width: 4px;
    background-color: #ff4a4a;
    border-radius: 50%;
  }
}
.demand-type {
  .type-title {
    font-size: 12px;
    color: #333333;
    line-height: 16px;
    margin-bottom: 8px;
    .delay {
      display: inline-block;
      height: 16px;
      width: 46px;
      background-color: rgb(238, 39, 39);
      color: #fff;
      line-height: 16px;
      text-align: center;
      border-radius: 2px;
      margin-right: 4px;
      & > .iconfont {
        margin-right: 2px;
      }
    }
  }
  .type-process-code {
    font-size: 12px;
    color: #999999;
    line-height: 13px;
    margin-bottom: 8px;
  }
  .related-modify {
    display: flex;
    .related-modify-label {
      margin-top: 2px;
      margin-right: 4px;
    }
  }
}

.demand-info-box {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  .top {
    width: 100%;
    height: 20px;
    font-size: 12px;
    font-weight: bold;
    color: #333333;
    margin-bottom: 8px;
    line-height: 20px;
    display: flex;
    align-items: center;
    & > span {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .demand-title {
      flex: none;
      max-width: 72%;
      cursor: pointer;
    }

    .demand-title:hover {
      color: #378eef;
    }

    .copy-text {
      color: #d8d8d8;
      cursor: pointer;
    }
  }
  .middle {
    margin-bottom: 6px;
    .info-text {
      font-size: 12px;
      color: #999999;
      line-height: 16px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      cursor: pointer;
    }
    .info-text-all {
      max-width: 750px;
      font-size: 12px;
      color: #666666;
      line-height: 16px;
      max-height: 300px;
      overflow-y: auto;
    }
  }
  .bottom,
  .mine-pover-class {
    .bottom-box {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
    }

    .info-type {
      height: 24px;
      border-radius: 2px;
      font-size: 12px;
      line-height: 24px;
      padding: 0px 6px;
      margin-bottom: 10px;
      margin-right: 10px;
      cursor: pointer;
    }
    .info-type0 {
      color: #60b6ff;
      background: #f1f7fb;
    }
    .info-type1 {
      color: #3dcca6;
      background: #eaf9f6;
    }
    .info-type2 {
      color: #f8af11;
      background: #fdf7e9;
    }
    .info-type3 {
      color: #a697fe;
      background: rgba(166, 151, 254, 0.2);
    }

    .label-more-box {
      display: flex;
      flex-wrap: wrap;
    }
  }

  .label-more {
    width: 12px;
    height: 12px;
    display: inline-block;
    background: url('~@/assets/images/bpm/label_more.png') center no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
  }
}

.draft-box {
  .draft-title {
    height: 12px;
    font-size: 12px;
    color: #333333;
    line-height: 12px;
    margin-bottom: 10px;
  }
  .draft-info {
    height: 16px;
    font-size: 12px;
    color: #999;
    line-height: 16px;
    margin-bottom: 12px;
    & > span > i {
      font-size: 12px;
    }
    .text {
      // margin-right: 4px;
      color: #bbbbbb;
    }
  }
  .edit-btn {
    height: 28px;
    line-height: 22px;
    padding: 0px 10px;

    .iconicon_piliangbianji {
      font-size: 12px;
      margin-right: 2px;
    }
  }
}
.message-header-box {
  height: 40px;
  margin: 0px 20px;
  margin-top: -20px;
  border-bottom: 1px solid #eeeeee;
  display: flex;
  align-items: center;

  .title {
    display: flex;
    align-items: center;
    width: 100%;
    i {
      display: inline-block;
      width: 14px;
      height: 12px;
      background: url('~@/assets/images/bpm/forwarder_detial_title01.png') repeat no-repeat;
      background-size: 100% 100%;
      margin-right: 5px;
      flex-shrink: 0;
    }
    span {
      flex: 1;
      font-size: 12px;
      color: #999999;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.scroll-box {
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  .marginB16 {
    margin-bottom: 16px;
  }
}
.marginR10 {
  margin-right: 10px;
}
.content-box :deep .fs-table:before {
  background-color: transparent;
}
.content-box :deep .fs-table-wrapper {
  overflow: visible !important;
}
.content-box :deep .fs-table .fs-table-header th {
  padding: 16px 0 !important;
}
.content-box :deep .fs-table-cell-slot {
  line-height: 18px;
  padding: 10px 0px;
}
.content-box :deep .fs-table th {
  background-color: #ebf3fd;
  font-size: 12px !important;
  color: #333 !important;
  font-family: 'SimHei';
  font-weight: normal !important;
  border-bottom: none;
}
.content-box :deep .fs-table td {
  border-bottom: none !important;
}
.content-box :deep .fs-table .fs-table-row:nth-child(2n) td {
  background: #f9fdff;
}
.content-box :deep .fs-table-header thead > tr > th:first-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.content-box :deep .fs-table-header thead > tr > th:last-child {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.content-box :deep .fs-table-body {
  border-bottom: 2px solid #eeeeee;
}

:deep(.fs-table-tbody) {
  > tr:hover:not(.fs-table-expanded-row) > td,
  .fs-table-row-hover,
  .fs-table-row-hover > td {
    background: #f1f4f8 !important;
  }
}
.fs-table-fixed {
  .fs-table-row-hover,
  .fs-table-row-hover > td {
    background: #f1f4f8 !important;
  }
}

.cup {
  .iconfont {
    font-size: 14px;
    color: #868688;
  }
  & > .handle {
    color: #378eef;
  }
  & > span:hover {
    color: #0272f0;
  }
  span {
    cursor: pointer;
  }
}
.splace-icon {
  margin-right: 2px;
  margin-left: 6px;
}

div.top .urgent {
  flex: 0 0 50px;
  margin: 0;
  margin-right: 5px;
  height: 18px;
  line-height: 18px;
  color: #ee2727;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid #ff6a6a;
  border-radius: 2px;
  background: #fff1f1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  > .icon-urgent {
    display: inline-block;
    vertical-align: text-top;
    width: 12px;
    height: 12px;
    background: url('~@/assets/images/bpm/urgent.png') center no-repeat;
    background-size: 100% 100%;
    flex-shrink: 0;
  }
  .marginB1 {
    margin-top: 1px;
  }
}
</style>
