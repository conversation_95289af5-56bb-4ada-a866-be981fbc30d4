import { createApp } from 'vue'
import SmartDesign from '@fs/smart-design'

import router from './router'
import store from './store'
import i18n from './i18n'
import directives from './directives'

import VueViewer from 'v-viewer'

import App from './App.vue'

// import 'amis-ui/lib/locale/en-US'
import './micro-public-path'

import 'normalize.css/normalize.css'
// import '@fs/smart-design/dist/style.css'
// import 'fs-speed/dist/style.css'
import 'viewerjs/dist/viewer.css'
import '@logicflow/core/dist/style/index.css'
import '@logicflow/extension/lib/style/index.css'
import './assets/fonts/iconfont.css'
import './styles/index.scss'

createApp(App)
  .use(i18n)
  .use(directives)
  .use(VueViewer)
  .use(SmartDesign)
  .use(store)
  .use(router)
  .mount('#bpmProjectManage')
