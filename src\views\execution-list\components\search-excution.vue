<template>
  <div class="select-box shadow-radius">
    <div class="select-box-left">
      <div class="left-box">
        <div class="marginR12">
          <FSelect
            v-if="props.processTypeList && props.processTypeList.length > 0"
            v-model:value="searchData.processConfigId"
            :press-line="i18n.t('触发流程类型')"
            show-search
            allow-clear
            style="width: 120px"
            :placeholder="i18n.t('请选择')"
            :options="props.processTypeList.map(item => ({ value: item.id, label: item.processName }))"
            option-filter-prop="label"
            @change="onProcessType"
          >
            <template #suffixIcon>
              <i class="cursor iconfont colorBBB fontSize12" @click="onSearch">&#xe799;</i>
            </template>
          </FSelect>
        </div>

        <div class="marginR12">
          <FSelect
            v-model:value="searchData.status"
            :press-line="i18n.t('状态')"
            style="width: 120px"
            allow-clear
            :placeholder="i18n.t('请选择')"
            :options="statusList"
            @change="onSearch"
          >
            <template #suffixIcon>
              <i class="cursor iconfont colorBBB fontSize12" @click="onSearch">&#xe799;</i>
            </template>
          </FSelect>
        </div>

        <div class="marginR12">
          <FSelect
            v-model:value="searchData.triggerType"
            :press-line="i18n.t('触发器类型')"
            allow-clear
            style="width: 120px"
            :options="triggerList"
            :placeholder="i18n.t('请选择')"
            @change="onSearch"
          >
            <template #suffixIcon>
              <i class="cursor iconfont colorBBB fontSize12" @click="onSearch">&#xe799;</i>
            </template>
          </FSelect>
        </div>
        <div class="marginR12">
          <FRangePicker
            v-model:value="range"
            @change="onSearch"
            :press-line="i18n.t('执行时间')"
            format="YYYY-MM-DD"
            style="width: 240px; height: 32px"
            allow-clear
          />
        </div>
        <div class="marginR12">
          <FInput
            :placeholder="i18n.t('触发器名称/编号')"
            style="width: 240px"
            :press-line="i18n.t('快速搜索')"
            @press-enter="onSearch"
            v-model:value="searchData.input"
          >
            <template #suffix>
              <i class="cursor iconfont colorBBB fontSize12" @click="onSearch">&#xe70e;</i>
            </template>
          </FInput>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { SelectProps } from '@fs/smart-design/dist/ant-design-vue_es'
import dayjs from 'dayjs'
import { transformDate, useI18n } from '@/utils'
import { ExcutionListSearch, processTye, triggerTypeRadioList } from '@/types/excutionListModel'
interface IProps {
  processTypeList: processTye[]
}
const i18n = useI18n()
const props = defineProps<IProps>()
const searchData = reactive<ExcutionListSearch>({
  processConfigId: '',
  status: '',
  triggerType: '',
  startTime: '',
  endTime: '',
  input: '',
})
const range = ref<[dayjs.Dayjs, dayjs.Dayjs]>()
const statusList = computed<SelectProps['options']>(() => [
  { value: 0, label: i18n.t('失败') },
  { value: 1, label: i18n.t('成功') },
])
const triggerList = triggerTypeRadioList
const emit = defineEmits(['search'])
const filterOption = (input: string, option: any) => option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
// 选择标签联动
const onProcessType = (val: any) => {
  searchData.processConfigId = val
  onSearch()
}
const onSearch = () => {
  const params = { ...searchData }
  params.startTime = range.value?.[0] && transformDate(range.value[0], 'YYYY-MM-DD')
  params.endTime = range.value?.[1] && transformDate(range.value[1], 'YYYY-MM-DD')
  emit('search', params)
}
</script>

<style lang="scss" scoped>
.select-box {
  padding-top: 24px;
  padding-bottom: 24px;
  padding-left: 24px;
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  margin-bottom: 16px;
  .select-box-left {
    flex: 1;
    display: flex;
    .left-box {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      .btn_div_relative {
        display: inline-block;
        position: relative;
        height: 34px;
        line-height: 1;
        :deep(.fs-picker) {
          .fs-picker-input-active {
            background-color: #fff;
          }
        }
        :deep(.fs-picker-focused) {
          .fs-picker-input-active {
            background-color: #f1f4f8 !important;
          }
        }
        :deep(.btn_span_absolute) {
          display: inline-block;
          position: absolute;
          top: -7px;
          left: 4px;
          z-index: 10;
          padding: 0 3px;
          color: #999;
          background: #fff;
          font-size: 11px;
          -webkit-transform: scale(0.9);
          transform: scale(0.9);
        }
        .btn_span_absolute_left {
          left: 7px;
        }
      }
      .marginR12 {
        margin-right: 12px;
      }
    }
  }
}
.shadow-radius {
  background: #ffffff;
  box-shadow: 0 2px 8px 0 rgba(88, 98, 110, 0.08);
  border-radius: 4px;
}
</style>
