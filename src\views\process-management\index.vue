<template>
  <div class="process-management-container">
    <FSpin :spinning="tableLoading">
      <Breadcrumb :data="[i18n.t('流程管理'), i18n.t('需求流程管理')]" />
      <FCard class="header-box marginT24">
        <FTabSet :tab-list="tabList" v-model:activeKey="tabValue" @change="onTabChange"></FTabSet>
        <SearchBox
          ref="searchBoxRef"
          v-model:tabValue="tabValue"
          v-model:pageData="pageData"
          @onGetSearchData="onGetSearchData"
        />
      </FCard>
      <TableBox
        v-model:page="pageData"
        :loading="tableLoading"
        :list="list"
        :tabValue="tabValue"
        @onExportProcess="onExportProcess"
        @onPageChange="onGetListData"
        :key="updateTableKey"
      />
    </FSpin>
  </div>
</template>

<script setup lang="ts">
import Breadcrumb from '@/views/pgb-data-board/components/Breadcrumb/index.vue'
import SearchBox from './components/SearchBox/index.vue'
import TableBox from './components/TableBox/index.vue'
import { BasicPageParams } from '@/types/processBoard'
import { PageDemandParams, PageDemandRes } from '@/types/processManagement'
import { pageDemandList, pageDemandExport, getDemandCount, getByIdProcessConfig } from '@/api/processManagement'
import { FSpin, message } from '@fs/smart-design'
import { ref, reactive, onMounted, provide, computed, nextTick } from 'vue'
import { useI18n, deepClone } from '@/utils'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'

const i18n = useI18n()
const store = useStore()
const route = useRoute()
const { id } = route.params as { id: string }
const tableLoading = ref<boolean>(false)
const cacheKey = computed(() => {
  if (route.params?.demandType) return route.params?.demandType
  return 'noCache'
})
const tabValue = ref<number>(0)
const searchBoxRef = ref()

const tabList = ref<any>([
  {
    title: '全部',
    count: 0,
    status: 0,
  },
  {
    title: '我的待办',
    count: 0,
    status: 1,
  },
  {
    title: '我的已办',
    count: 0,
    status: 2,
  },
  {
    title: '进行中',
    count: 0,
    status: 3,
  },
  {
    title: '已完成',
    count: 0,
    status: 4,
  },
  {
    title: '已办结',
    count: 0,
    status: 5,
  },
])
const searchData = ref<PageDemandParams>({})
const cacheValue = ref<any>({})
const list = ref<PageDemandRes[]>([])
const pageData = reactive<BasicPageParams>({
  currPage: 1,
  pageSize: 10,
  total: 0,
})
const processConfig = ref<any>({})
const updateTableKey = ref(Date.now() + Math.random())
const demandTypeValues = {
  pbg: 'PBG需求',
  cbg: 'CBG需求',
  mbg: 'MBG需求',
}

const onTabChange = value => {
  nextTick(() => {
    searchBoxRef?.value?.onGetSearchDataFn()
  })
}

const onGetListData = async (data: any = null) => {
  try {
    store.commit('local/SET_LOCAL_PROCESS_MANAGEMENT_SEARCH_DATA', {
      [cacheKey.value as string]: {
        ...cacheValue.value,
        ...{
          status: tabValue.value,
          currPage: pageData.currPage,
          pageSize: pageData.pageSize,
        },
      },
    })
    tableLoading.value = true
    const params = deepClone(
      Object.assign(
        { xqCpm: demandTypeValues?.[cacheKey.value as string] ?? undefined },
        searchData.value,
        pageData,
        data
      )
    ) // 拷贝一份参数
    delete params.total
    const res = await pageDemandList(params)
    list.value = res?.data?.list || []
    pageData.total = res?.data?.totalCount || 0
  } finally {
    tableLoading.value = false
  }
}

const onGetListCount = async () => {
  try {
    tableLoading.value = true
    const params = deepClone(searchData.value) // 拷贝一份参数
    delete params.status
    const res = await getDemandCount({ xqCpm: demandTypeValues?.[cacheKey.value as string] ?? undefined, ...params })
    if (res.code !== 200) throw new Error(res.msg)
    tabList.value[1].count = res?.data?.waitDeal ?? 0
    tabList.value[2].count = res?.data?.processed ?? 0
    tabList.value[3].count = res?.data?.running ?? 0
    tabList.value[4].count = res?.data?.completed ?? 0
    tabList.value[5].count = res?.data?.completion ?? 0
  } finally {
    tableLoading.value = false
  }
}

const onExportProcess = async () => {
  if (!list.value.length) {
    message.warning(i18n.t('当前页面无数据，请重新选择！'))
    return
  }
  const params = deepClone(searchData.value) // 拷贝一份参数
  const res = await pageDemandExport(params)
  if (res.code !== 200) throw new Error(res.msg)
  message.success(i18n.t('下载成功，请在飞书查看！'))
}

const onGetByIdProcessConfig = async () => {
  const res = await getByIdProcessConfig(id)
  if (res.code !== 200) throw new Error(res.msg)
  processConfig.value = res?.data ?? {}
}

const onGetSearchData = async (data: any) => {
  try {
    tableLoading.value = true
    searchData.value = data.params
    cacheValue.value = data.cacheValue
    if (!data.type) {
      pageData.currPage = 1
      pageData.pageSize = 10
    }
    await onGetListData()
    await onGetListCount()
    updateTableKey.value = Date.now() + Math.random()
  } finally {
    tableLoading.value = false
  }
}

provide('processConfig', processConfig) // 流程类型

onMounted(() => {
  requestIdleCallback(onGetByIdProcessConfig)
})
</script>

<style scoped lang="scss">
.process-management-container {
  .header-box {
    :deep(.fs-card-body) {
      padding-top: 0 !important;
      padding-bottom: 0 !important;
    }
  }
}
</style>
