<template>
  <div class="demand-handle-wrapper">
    <FAnchor
      :data="
        processInfo
          .filter(item => ![0, 1, -99999].includes(item.status))
          .map(({ id, topicName }) => ({ id, name: topicName }))
      "
    />
    <div class="demand-handle">
      <!-- title -->
      <div class="demand-handle-header mb24">
        <h5 class="demand-handle-header-title">{{ processName }} - {{ processNo }}</h5>
        <div class="demand-handle-header-extend">
          <a class="copy-text" :data-clipboard-text="copyTest" @click="copy">{{ i18n.t('复制编号及链接') }}</a>
          <FButton class="ml16 inline" size="small" @click="router.back()">{{ i18n.t('返回') }}</FButton>
        </div>
      </div>

      <!-- process line -->
      <FCard class="mb24" :head-style="COMPONENT_CRAD_BODY_STYLE">
        <template #title><Icon icon="iconjinduicon" class="marginR5" />{{ i18n.t('项目进度') }}</template>
        <div class="__wrapper"><StepIndex :data="processInfo" /></div>
      </FCard>

      <!-- main -->
      <div class="demand-handle-main">
        <!-- 里程碑/角色/日志 -->
        <div class="demand-handle-main-info">
          <!-- Tab 标签 -->
          <FCard
            class="mb24"
            :tab-list="TAB_LIST"
            :active-tab-key="tagActive"
            :body-style="CRAD_BODY_STYLE"
            :head-style="COMPONENT_CRAD_BODY_STYLE"
            @tab-change="(key: TagKey) => (tagActive = key)"
          >
            <template #customTab="item">
              <FBadge v-if="item.key === TagKey.relevance" :count="relevanceNumber" :offset="[5, -3]" size="small">
                <span>{{ item.tab }}</span>
              </FBadge>
            </template>
          </FCard>

          <!-- Tab 组件 -->
          <FSpin :spinning="spinning">
            <component
              :is="TAB_COMPONENT[tagActive]"
              :process-info="processInfo"
              :other="{ processRoleInfo }"
              @operate="handleOperate"
            />
          </FSpin>
        </div>

        <!-- 消息协同 -->
        <Massage
          :process-id="processId"
          :process-no="processNo"
          :process-name="processName"
          :params-wrapper="paramsWrapper"
        />
      </div>

      <TaskModal
        v-model="taskModel.flag"
        :title="(taskModel.title as string)"
        :data="(taskModel.data as ITask)"
        :role="processRoleInfo"
        @submit="handleTaskSubmit"
      />
      <ProcessTask
        v-model:value="handleTaskModel.flag"
        :task-record="handleTaskModel.data"
        :params-wrapper="paramsWrapper"
        @father-method="getProcessInfo"
      />
      <TaskTodo
        v-model:value="judgeTaskModel.flag"
        :task-record="judgeTaskModel.data"
        :params-wrapper="paramsWrapper"
        @father-method="getProcessInfo"
      />
      <TaskDetail v-model:value="viewTaskModel.flag" :task-record="viewTaskModel.data" />
      <!-- <TransFerred v-model:value="finishModel.flag" @submit="handleFinish" /> -->
      <DispatchModal v-model:value="dispatchModel.flag" :is-remark="true" @submit="handleDispatchMilepost" />
      <RejectModal v-model:value="rejectModel.flag" :reject-list="processRejectInfo" @submit="handleRejectMilepost" />
      <!-- 新增弹框 -->
      <CreateProcessModal v-model="createProcessModal.flag" :params="{ processNo }" :is-detail="true" />
      <RemarkModal
        v-model="remarkModal.flag"
        :title="(remarkModal.title as string)"
        :text="i18n.t('撤回说明')"
        @submit="handleRecall"
      />
      <!-- 办结/提交 -->
      <NotificationModal
        v-model:value="notificationModel.flag"
        v-model:loading="notificationModel.loading"
        :title="(notificationModel.title as string)"
        :role="processRoleInfo"
        defaultRoleKey="milepostId"
        :defaultRoleValue="notificationModel.data.id"
        :isDelayStatus="(isDelayStatus as boolean)"
        @submit="handleNotificationMilepost"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive, onUnmounted, provide } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { FModal, messageInstance as message } from '@fs/smart-design'
import Clipboard from 'clipboard'
import {
  addChildTask,
  addTask,
  commitTask,
  editTask,
  getInstanceInfo,
  getProcessRoleAndUser,
  removeTask,
  saveMilepostFormData,
  transferrTask,
  turnTask,
  getProcessRelateList,
  batchDelTask,
  batchEditTask,
  recallProcess,
  rejectTask,
} from '@/api'
import {
  TAB_COMPONENT,
  TAB_LIST,
  TagKey,
  CRAD_BODY_STYLE,
  TASK_STATUS_NAME,
  EmitType,
  TaskModalTilte,
  TASK_STATUS_ICON,
  COMPONENT_CRAD_BODY_STYLE,
  ProcessModalTilte,
} from './config'
import { deepClone, transformDate, BAESURL, useI18n } from '@/utils'

import Icon from '@/components/Icon/index.vue'
import StepIndex from '@/components/Step/StepIndex.vue'
import Massage from './components/Message/index.vue'
import TaskModal from './components/TaskModal/index.vue'
import TaskTodo from '@/components/TaskTodo/index.vue'
import TaskDetail from '@/components/TaskDetail/index.vue'
import ProcessTask from '@/components/ProcessTask/index.vue'
import DispatchModal from '@/components/DispatchModal/index.vue'
import RejectModal from '@/components/RejectModal/index.vue'
import NotificationModal from '@/components/NotificationModal/index.vue'
import CreateProcessModal from '@/components/CreateProcessModal/index.vue'
import RemarkModal from './components/RemarkModal/index.vue'
import FAnchor from './components/FAnchor/index.vue'

import type { IProcess, ITask } from '@/types/handle'
import type { IProcessRoleAndUser } from '@/types/request'

interface IModel<T = ITask> {
  flag: boolean
  loading?: boolean
  data: T
  [key: string]: unknown
}
const i18n = useI18n()
const route = useRoute()
const router = useRouter()

const processId = +(route.params.id as string)
const processInfo = ref<IProcess[]>([])
const processRoleInfo = ref<IProcessRoleAndUser[]>([])
const processNo = computed(() => processInfo.value[0]?.processInstanceCode ?? '')
const processName = computed(() => processInfo.value[0]?.instanceTopicName ?? '')
const processType = computed(() => processInfo.value[0]?.processType)
const processRejectInfo = computed(() => processInfo.value.filter(item => item.status == 3 || item.status == 5))

const spinning = ref(false)
const tagActive = ref<TagKey>(TagKey.handle)
const relevanceNumber = ref(0)

const taskModel = reactive<IModel<ITask | ITask[] | IProcess>>({ flag: false, data: {} as ITask, title: '' })
const viewTaskModel = reactive<IModel>({ flag: false, data: {} as ITask })
const handleTaskModel = reactive<IModel>({ flag: false, data: {} as ITask })
const judgeTaskModel = reactive<IModel>({ flag: false, data: {} as ITask })
const dispatchModel = reactive<IModel<IProcess>>({ flag: false, data: {} as IProcess })
const rejectModel = reactive<IModel<IProcess>>({ flag: false, data: {} as IProcess })
// const finishModel = reactive<IModel<IProcess>>({ flag: false, data: {} as IProcess })
const createProcessModal = reactive<IModel<Record<string, unknown>>>({ flag: false, data: {} })
const remarkModal = reactive<IModel<IProcess>>({ flag: false, title: '', data: {} as IProcess })
const notificationModel = reactive<IModel<IProcess>>({ flag: false, data: {} as IProcess, title: '', loading: false })
const isDelayStatus = computed(
  () =>
    notificationModel.data?.invalid === 1 &&
    notificationModel.data?.isDelayReason === 1 &&
    notificationModel.data?.status === 2 &&
    notificationModel.data?.overdueDuration
)
// 初始化
onMounted(() => {
  getProcessInfo()
  getProcessRoleInfo()

  requestIdleCallback(initRelevanceNumber)

  // 用来和 amis 进行数据交互的 hack 手段
  window.rawWindow.FS_BPM_PROCESS_MILEPOST = {}
})
onUnmounted(() => {
  delete window.rawWindow.FS_BPM_PROCESS_MILEPOST
})

const operateFunctions = {
  // 任务
  [EmitType.createTask]: (data: IProcess) => {
    taskModel.data = data
    taskModel.flag = true
    taskModel.title = TaskModalTilte.add
  },
  [EmitType.createChildTask]: (data: ITask) => {
    taskModel.data = data
    taskModel.flag = true
    taskModel.title = TaskModalTilte.addChild
  },
  [EmitType.viewTask]: (data: ITask) => {
    viewTaskModel.data = data
    viewTaskModel.flag = true
  },
  [EmitType.editTask]: (data: ITask) => {
    taskModel.data = data
    taskModel.flag = true
    taskModel.title = TaskModalTilte.edit
  },
  [EmitType.batchEditTask]: (data: ITask[]) => {
    taskModel.data = data
    taskModel.flag = true
    taskModel.title = TaskModalTilte.batchEdit
  },
  [EmitType.handleTask]: (data: ITask) => {
    handleTaskModel.data = data
    handleTaskModel.flag = true
  },
  [EmitType.judgeTask]: (data: ITask) => {
    judgeTaskModel.data = data
    judgeTaskModel.flag = true
  },
  [EmitType.delTask]: (data: ITask) => {
    removeTask(paramsWrapper({ taskId: data.id })).then(res => {
      if (res.code == 200) {
        message.success(i18n.t('删除成功'))
        getProcessInfo()
      } else {
        message.error(res.msg)
      }
    })
  },
  [EmitType.batchDelTask]: (data: { milepost: IProcess; tasks: ITask[] }) => {
    const { milepost, tasks = [] } = data
    FModal.confirm({
      title: i18n.t('是否确认删除所选的任务？'),
      content: i18n.t('当前删除的任务中所包含的子任务会一并删除，请谨慎操作！'),
      okText: i18n.t('确定'),
      cancelText: i18n.t('取消'),
      onOk: async () => {
        await batchDelTask({
          instanceId: processId,
          instanceTopicName: processName.value,
          processInstanceCode: processNo.value,
          processType: processType.value,
          topicName: milepost.topicName,
          taskIdList: tasks.map(item => item.id),
        })
        message.success(i18n.t('删除成功'))
        getProcessInfo()
      },
    })
  },

  // 里程碑
  [EmitType.dispatch]: (data: IProcess) => {
    dispatchModel.data = data
    dispatchModel.flag = true
  },
  // 驳回
  [EmitType.reject]: (data: IProcess) => {
    rejectModel.data = data
    rejectModel.flag = true
  },
  [EmitType.finish]: async (data: IProcess) => {
    notificationModel.data = data
    notificationModel.flag = true
    notificationModel.title = ProcessModalTilte.finish
  },
  [EmitType.submit]: async (data: IProcess) => {
    notificationModel.data = data
    notificationModel.flag = true
    notificationModel.title = ProcessModalTilte.submit
  },
  [EmitType.revoke]: async (data: IProcess) => {
    remarkModal.data = data
    remarkModal.flag = true
    remarkModal.title = i18n.t('撤回')
  },
  [EmitType.createProcess]: async (data: IProcess) => {
    createProcessModal.flag = true
    createProcessModal.data = data
  },

  // role
  [EmitType.updateRole]: () => {
    getProcessRoleInfo()
    getProcessInfo()
  },
}

// 处理操作事件
const handleOperate = (value: { type: keyof typeof EmitType; data: unknown }) => {
  const { type, data } = value
  operateFunctions[type as keyof typeof operateFunctions](data as any)
}

// 添加参数包装
const paramsWrapper = <T>(
  data: T
): T & { processType: string; processInstanceCode: string; instanceTopicName: string } => {
  return {
    ...data,
    processType: processType.value,
    instanceTopicName: processName.value,
    processInstanceCode: processNo.value,
  }
}

// 处理撤回
const handleRecall = async (data: { remark: string; isSave: 0 | 1 }) => {
  try {
    spinning.value = true
    const params = { instanceId: processId, milepostId: remarkModal.data.id, msg: data.remark, isSave: data.isSave }
    const res = await recallProcess(paramsWrapper(params))
    if (res.code !== 200) return
    message.success(i18n.t('撤回成功'))
    getProcessInfo()
    if (data.isSave === 1) {
      message.success(i18n.t('3秒后将跳转到草稿页面'))
      setTimeout(() => {
        router.push({
          name: 'DemandAdd',
          params: { id: processId, processDefineKey: processInfo.value[0].formKey },
          query: { draftId: res.data, keyType: 'formId' },
        })
      }, 3000)
    }
  } finally {
    remarkModal.flag = false
    remarkModal.data = {} as IProcess
    remarkModal.title = ''
    spinning.value = false
  }
}

// 处理办结
// const handleFinish = async (data: IProcess) => {
//   const params = paramsWrapper({ milepostId: data.id, instanceId: data.instanceId })
//   const res = await transferrTask(params)
//   if (res.code !== 200) throw new Error(res.msg)
//   message.success('办结成功')
//   getProcessInfo()
// }

// 处理提交/办结
const handleNotificationMilepost = async (data: string[]) => {
  try {
    await handleFormSubmit(notificationModel.data.id, notificationModel.data.formData || {})
    if (notificationModel.title === ProcessModalTilte.finish) {
      const params = paramsWrapper({
        milepostId: notificationModel.data.id,
        instanceId: notificationModel.data.instanceId,
        ...data,
      })
      const res = await transferrTask(params)
      if (res.code !== 200) throw new Error(res.msg)
      message.success(i18n.t('办结成功'))
    } else {
      const params = paramsWrapper({ milepostInfoId: notificationModel.data.id, ...data })
      const res = await commitTask(params)
      if (res.code !== 200) throw new Error(res.msg)
      message.success(i18n.t('提交成功'))
    }
    window.rawWindow.FS_BPM_PROCESS_MILEPOST = {} // 清空表单数据
    notificationModel.flag = false
    getProcessInfo()
  } finally {
    notificationModel.loading = false
  }
}

// 转派里程碑
const handleDispatchMilepost = async (data: any) => {
  spinning.value = true
  const params = { ...data, milepostId: dispatchModel.data.id }
  try {
    const res = await turnTask(paramsWrapper(params))
    if (res.code !== 200) return
    getProcessInfo()
    message.success(i18n.t('转派成功'))
  } finally {
    dispatchModel.flag = false
    spinning.value = false
  }
}
// 驳回
const handleRejectMilepost = async (data: any) => {
  spinning.value = true
  const params = { ...data, instanceId: processId, sourceMilepostId: rejectModel.data.id }
  try {
    const res = await rejectTask(params)
    if (res.code !== 200) return
    await getProcessInfo()
    message.success(i18n.t('驳回成功'))
  } finally {
    rejectModel.flag = false
    spinning.value = false
  }
}

// 提交表单
const handleFormSubmit = async (id: number, oldFormData: Record<string, unknown>) => {
  const formData = window.rawWindow.FS_BPM_PROCESS_MILEPOST
  if (!formData || !Object.keys(formData).length) return
  const { file } = formData
  const { oldFile } = oldFormData
  let currFile =
    oldFile && typeof oldFile === 'string'
      ? JSON.parse(`[${oldFile}]`)
      : ((oldFile as []) || []).map((item: Record<string, unknown>) => ({ ...item })) // 防止表单数据变更导致表单数据还原成修改前
  if (file && oldFile) {
    currFile.push(...JSON.parse(`[${file}]`))
  } else if (file && !oldFile) {
    currFile = JSON.parse(`[${file}]`)
  }
  try {
    await saveMilepostFormData({ id, formData: { ...oldFormData, ...formData, file: currFile } })
    message.success(i18n.t('表单信息提交成功'))
  } catch (error) {
    message.success(i18n.t('表单信息提交失败'))
  }
}

// 提交任务
const handleTaskSubmit = async (data: ITask) => {
  let action
  let formData = deepClone(data)
  const currTask = taskModel.data as ITask
  formData.milepostId = currTask.milepostId as number
  if (taskModel.title === TaskModalTilte.edit) {
    action = editTask
    formData.id = currTask.id
    formData.preTask = currTask.childTaskFlag ? (currTask.preTask as number) : formData.preTask
  } else if (taskModel.title === TaskModalTilte.addChild) {
    action = addChildTask
    formData.preTask = currTask.id
  } else if (taskModel.title === TaskModalTilte.batchEdit) {
    action = batchEditTask
    formData = {
      isSys: data.isSys,
      superviserRoleCode: data.superviserRoleCode,
      superviser: data.superviser,
      approverRoleCode: data.approverRoleCode,
      approver: data.approver,
      forcastTime: data.forcastTime,
      instanceId: processId,
      taskIdList: ((currTask.tasks ?? []) as unknown as ITask[]).map(item => item.id),
    } as unknown as ITask
  } else {
    action = addTask
    formData.preTask = formData.preTask ?? 0
    formData.milepostId = (currTask as unknown as IProcess).id // 新增的时候 data 为 里程碑数据
  }
  spinning.value = true
  try {
    const { code } = await action(paramsWrapper(formData) as any)
    if (code !== 200) return
    getProcessInfo()
    message.success(i18n.t('操作成功'))
  } finally {
    taskModel.flag = false
    spinning.value = false
  }
}

// 处理流程信息数据
const handleProcessInfo = (data: IProcess[]) => {
  // 处理结办后节点的状态数 和 字符串文件的 hack
  let flag = false
  data.forEach(item => {
    if (flag) item.status = -99999
    else if (item.status === 4) flag = true

    if (item.formData && item.formData.file && typeof item.formData.file === 'string') {
      item.formData.file = JSON.parse(`[${item.formData.file}]`)
    }
  })
}

// 处理树形表格需要的 Key 字段
const handleTableTreeKey = (data: IProcess[] | ITask[] = [], index = 0, processInstanceCode: string | null = null) => {
  data?.forEach(item => {
    item.key = ++index
    processInstanceCode = (processInstanceCode || item.processInstanceCode) as string
    item.children && handleTableTreeKey(item.children, item.key * 10, processInstanceCode)
    item.children?.forEach(task => {
      task.statusName = TASK_STATUS_NAME[task.status as keyof typeof TASK_STATUS_NAME]
      task.statusIcon = TASK_STATUS_ICON[task.status as keyof typeof TASK_STATUS_ICON]
      task.isSysName = task.isSys ? i18n.t('是') : i18n.t('否')
      task.superviser = task.superviser || '--'
      task.approver = task.approver || '--'
      task.prefixTaskName = task.prefixTaskName || '--'
      task.forcastTimeStr = transformDate(task.forcastTime, 'YYYY-MM-DD')
      task.taskCompletedTimeStr = transformDate(task.taskCompletedTime, 'YYYY-MM-DD')
      task.processInstanceCode = task?.processInstanceCode || processInstanceCode
    })
  })
}

// 获取流程信息
const getProcessInfo = async () => {
  spinning.value = true
  const { data = [] } = await getInstanceInfo({ instanceId: processId })
  handleProcessInfo(data)
  handleTableTreeKey(data)
  processInfo.value = data
  spinning.value = false
}

// 获取流程角色信息
const getProcessRoleInfo = async () => {
  spinning.value = true
  try {
    const { data = [] } = await getProcessRoleAndUser(processId)
    processRoleInfo.value = data
  } finally {
    spinning.value = false
  }
}

const initRelevanceNumber = async () => {
  const { data = [] } = await getProcessRelateList(processId)
  relevanceNumber.value = data.length
}
// 复制编号及链接
const copyTest = ref()
const copy = () => {
  copyTest.value = `${processName.value} ${processNo.value} ${BAESURL}/bpm-manage/demand/handle/${processId}`
  const clipboard = new Clipboard('.copy-text')
  clipboard.on('success', e => {
    message.success(i18n.t('复制成功'))
    clipboard.destroy()
  })
  clipboard.on('error', e => {
    message.warning(i18n.t('复制失败,稍后再试'))
    clipboard.destroy()
  })
}

provide('setRlevanceNumber', (value: number) => (relevanceNumber.value = value))
provide('handleOperate', handleOperate)
provide('getProcessInfo', getProcessInfo)
</script>

<style lang="scss" scoped>
.mb24 {
  margin-bottom: 24px !important;
}

.ml16 {
  margin-left: 16px;
}

.inline {
  display: inline-block !important;
}

.__wrapper {
  width: 100%;
  overflow-x: auto;
}

.demand-handle-wrapper {
  display: flex;
  justify-content: center;

  .demand-handle {
    flex: auto;
    max-width: 1420px;
    min-width: 800px;

    // 头部
    > .demand-handle-header {
      height: 67px;
      padding: 0 24px;
      border-radius: 4px;
      background-color: white;
      box-shadow: 0 2px 10px 0 rgb(0 0 0 / 4%);
      margin-top: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      > .demand-handle-header-title {
        margin: 0;
        line-height: 67px;
        color: #333;
        font-size: 18px;
        font-weight: 700;
      }
    }

    //项目进度
    :deep(.fs-card-head) {
      padding: 0 32px;
    }
    :deep(.fs-card-body) {
      padding: 24px 32px !important;
    }

    // 主内容
    .demand-handle-main {
      display: flex;
      > .demand-handle-main-info {
        flex: 1;
        width: 0;
      }
    }
  }
}
</style>

<style lang="scss">
.demand-handle-main .fs-tabs-large > .fs-tabs-nav .fs-tabs-tab {
  font-size: 14px;
}
</style>
